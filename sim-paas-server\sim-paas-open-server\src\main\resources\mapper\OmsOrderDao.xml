<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bosi.sim.paas.open.server.dao.OmsOrderDao">
    <resultMap id="orderDetailMap" type="com.bosi.sim.paas.dao.model.sds.OmsOrder" >
        <id column="id" property="id" />
        <result column="member_id" property="memberId" />
        <result column="coupon_id" property="couponId" />
        <result column="order_sn" property="orderSn" />
        <result column="create_time" property="createTime" />
        <result column="member_username" property="memberUsername" />
        <result column="total_amount" property="totalAmount" />
        <result column="total_pay_amount" property="totalPayAmount" />
        <result column="total_promotion_amount" property="totalPromotionAmount" />
        <result column="total_coupon_amount" property="totalCouponAmount" />
        <result column="pay_type" property="payType" />
        <result column="source_type" property="sourceType" />
        <result column="status" property="status" />
        <result column="total_gift_point" property="totalGiftPoint" />
        <result column="total_gift_growth" property="totalGiftGrowth" />
        <result column="promotion_info" property="promotionInfo" />
        <result column="note" property="note" />
        <result column="receiver_detail_json" property="receiverDetailJson" />
        <result column="whether_confirm" property="whetherConfirm" />
        <result column="payment_time" property="paymentTime" />
        <result column="comment_time" property="commentTime" />
        <collection property="orderItemList" columnPrefix="ot_" javaType="java.util.List" resultMap="OrderItemResultMap"/>
    </resultMap>

    <resultMap id="OrderItemResultMap" type="com.bosi.sim.paas.dao.model.sds.OmsOrderItem">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="order_sn" property="orderSn" />
        <result column="product_id" property="productId" />
        <result column="product_pic" property="productPic" />
        <result column="product_name" property="productName" />
        <result column="product_brand_name" property="productBrandName" />
        <result column="product_sn" property="productSn" />
        <result column="per_product_price" property="perProductPrice" />
        <result column="product_quantity" property="productQuantity" />
        <result column="product_sku_id" property="productSkuId" />
        <result column="product_sku_code" property="productSkuCode" />
        <result column="whether_virtually" property="whetherVirtually" />
        <result column="product_category_name" property="productCategoryName" />
        <result column="promotion_name" property="promotionName" />
        <result column="per_promotion_amount" property="perPromotionAmount" />
        <result column="per_coupon_amount" property="perCouponAmount" />
        <result column="per_real_amount" property="perRealAmount" />
        <result column="per_gift_point" property="perGiftPoint" />
        <result column="per_gift_growth" property="perGiftGrowth" />
        <result column="product_attr" property="productAttr" />
        <result column="divide_detail_json" property="divideDetailJson" />
        <result column="divide_referral_code" property="divideReferralCode" />
        <result column="divide_total_amount" property="divideTotalAmount" />
        <result column="device_uid_json" property="deviceUidJson" />
    </resultMap>

    <select id="getDetailByOrderId" resultMap="orderDetailMap">
        select
            o.*,
            ot.id ot_id,
            ot.product_name ot_product_name,
            ot.product_sku_id ot_product_sku_id,
            ot.product_sku_code ot_product_sku_code,
            ot.product_quantity ot_product_quantity,
            ot.order_id  ot_order_id,
            ot.order_sn ot_order_sn,
            ot.product_id ot_product_id,
            ot.product_pic ot_product_pic,
            ot.product_name ot_product_name,
            ot.product_brand_name ot_product_brand_name,
            ot.product_sn ot_product_sn,
            ot.per_product_price ot_per_product_price,
            ot.product_quantity ot_product_quantity,
            ot.product_sku_id ot_product_sku_id,
            ot.product_sku_code ot_product_sku_code,
            ot.whether_virtually ot_whether_virtually,
            ot.product_category_name ot_product_category_name,
            ot.promotion_name ot_promotion_name,
            ot.per_promotion_amount ot_per_promotion_amount,
            ot.per_coupon_amount ot_per_coupon_amount,
            ot.per_real_amount ot_per_real_amount,
            ot.per_gift_point ot_per_gift_point,
            ot.per_gift_growth ot_per_gift_growth,
            ot.product_attr ot_product_attr,
            ot.device_uid_json ot_device_uid_json
        from
            oms_order o
        left join
            oms_order_item ot on o.id = ot.order_id
        where
            o.id = #{orderId}
        and
            o.whether_delete = false
    </select>

    <select id="getDetailByOrderSn" resultMap="orderDetailMap">
        select
            o.*,
            ot.id ot_id,
            ot.product_name ot_product_name,
            ot.product_sku_id ot_product_sku_id,
            ot.product_sku_code ot_product_sku_code,
            ot.product_quantity ot_product_quantity,
            ot.order_id  ot_order_id,
            ot.order_sn ot_order_sn,
            ot.product_id ot_product_id,
            ot.product_pic ot_product_pic,
            ot.product_name ot_product_name,
            ot.product_brand_name ot_product_brand_name,
            ot.product_sn ot_product_sn,
            ot.per_product_price ot_per_product_price,
            ot.product_quantity ot_product_quantity,
            ot.product_sku_id ot_product_sku_id,
            ot.product_sku_code ot_product_sku_code,
            ot.whether_virtually ot_whether_virtually,
            ot.product_category_name ot_product_category_name,
            ot.promotion_name ot_promotion_name,
            ot.per_promotion_amount ot_per_promotion_amount,
            ot.per_coupon_amount ot_per_coupon_amount,
            ot.per_real_amount ot_per_real_amount,
            ot.per_gift_point ot_per_gift_point,
            ot.per_gift_growth ot_per_gift_growth,
            ot.product_attr ot_product_attr,
            ot.device_uid_json ot_device_uid_json
        from
            oms_order o
        left join
            oms_order_item ot on o.id = ot.order_id
        where
            o.order_sn = #{orderSn}
        and
            o.whether_delete = false
    </select>

    <select id="getTimeOutOrders" resultMap="orderDetailMap">
        SELECT
            o.*,
            ot.id ot_id,
            ot.product_name ot_product_name,
            ot.product_sku_id ot_product_sku_id,
            ot.product_sku_code ot_product_sku_code,
            ot.product_quantity ot_product_quantity,
            ot.order_id  ot_order_id,
            ot.order_sn ot_order_sn,
            ot.product_id ot_product_id,
            ot.product_pic ot_product_pic,
            ot.product_name ot_product_name,
            ot.product_brand_name ot_product_brand_name,
            ot.product_sn ot_product_sn,
            ot.per_product_price ot_per_product_price,
            ot.product_quantity ot_product_quantity,
            ot.product_sku_id ot_product_sku_id,
            ot.product_sku_code ot_product_sku_code,
            ot.whether_virtually ot_whether_virtually,
            ot.product_category_name ot_product_category_name,
            ot.promotion_name ot_promotion_name,
            ot.per_promotion_amount ot_per_promotion_amount,
            ot.per_coupon_amount ot_per_coupon_amount,
            ot.per_real_amount ot_per_real_amount,
            ot.per_gift_point ot_per_gift_point,
            ot.per_gift_growth ot_per_gift_growth,
            ot.product_attr ot_product_attr,
            ot.device_uid_json ot_device_uid_json
        FROM
            oms_order o
            LEFT JOIN oms_order_item ot ON o.id = ot.order_id
        WHERE
            o.status = 0
            AND o.create_time &lt; date_add(NOW(), INTERVAL -#{minute} MINUTE);
    </select>

    <update id="updateSkuStock">
        UPDATE sds_product_sku
        SET
            stock = CASE id
            <foreach collection="itemList" item="item">
              WHEN #{item.productSkuId} THEN stock - #{item.productQuantity}
            </foreach>
            END,
            lock_stock = CASE id
            <foreach collection="itemList" item="item">
              WHEN #{item.productSkuId} THEN lock_stock - #{item.productQuantity}
            </foreach>
            END
        WHERE
            id IN
        <foreach collection="itemList" item="item" separator="," open="(" close=")">
            #{item.productSkuId}
        </foreach>
    </update>

    <update id="releaseSkuStockLock">
        UPDATE sds_product_sku
        SET
        lock_stock = CASE id
        <foreach collection="itemList" item="item">
            WHEN #{item.productSkuId} THEN lock_stock - #{item.productQuantity}
        </foreach>
        END
        WHERE
        id IN
        <foreach collection="itemList" item="item" separator="," open="(" close=")">
            #{item.productSkuId}
        </foreach>
    </update>
    <update id="lockStockBySkuId">
        UPDATE sds_product_sku
        SET lock_stock = lock_stock + #{quantity}
        WHERE
        id = #{productSkuId}
        AND lock_stock + #{quantity} &lt;= stock
    </update>
    <update id="reduceSkuStock">
        UPDATE sds_product_sku
        SET lock_stock = lock_stock - #{quantity},
            stock = stock - #{quantity},
            sale = sale + #{quantity}
        WHERE
            id = #{productSkuId}
          AND stock - #{quantity} &gt;= 0
          AND lock_stock - #{quantity} &gt;= 0
    </update>
    <update id="releaseStockBySkuId">
        UPDATE sds_product_sku
        SET lock_stock = lock_stock - #{quantity}
        WHERE
            id = #{productSkuId}
          AND lock_stock - #{quantity} &gt;= 0
    </update>
</mapper>
