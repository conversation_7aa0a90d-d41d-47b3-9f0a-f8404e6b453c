package com.bosi.sim.paas.admin.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.BizCode;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.dao.enums.sds.SdsAttributeTypeClassifyEnum;
import com.bosi.sim.paas.dao.mapper.pms.PmsAttributeMapper;
import com.bosi.sim.paas.dao.mapper.pms.PmsAttributeTypeMapper;
import com.bosi.sim.paas.dao.model.sds.PmsAttribute;
import com.bosi.sim.paas.dao.model.sds.PmsAttributeType;
import com.bosi.sim.paas.admin.server.service.PmsAttributeTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 商品属性管理Service实现类
 */
@Service
public class PmsAttributeTypeServiceImpl implements PmsAttributeTypeService {
    @Autowired
    private PmsAttributeTypeMapper pmsAttributeTypeMapper;

    @Autowired
    private PmsAttributeMapper pmsAttributeMapper;

    @Override
    public CommonPage<PmsAttributeType> page(Page<PmsAttributeType> page, PmsAttributeType productAttributeType) {
        LambdaQueryWrapper<PmsAttributeType> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotNull(productAttributeType.getAttributeId())) {
            queryWrapper.eq(PmsAttributeType::getAttributeId, productAttributeType.getAttributeId());
        }
        if (StringUtils.isNotNull(productAttributeType.getClassify())) {
            queryWrapper.eq(PmsAttributeType::getClassify, productAttributeType.getClassify());
        }
        return CommonPage.restPage(pmsAttributeTypeMapper.selectPage(page, queryWrapper));
    }

    @Override
    public List<PmsAttributeType> list(PmsAttributeType productAttributeType) {
        LambdaQueryWrapper<PmsAttributeType> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotNull(productAttributeType.getAttributeId())) {
            queryWrapper.eq(PmsAttributeType::getAttributeId, productAttributeType.getAttributeId());
        }
        if (StringUtils.isNotNull(productAttributeType.getClassify())) {
            queryWrapper.eq(PmsAttributeType::getClassify, productAttributeType.getClassify());
        }
        return pmsAttributeTypeMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional
    public int create(PmsAttributeType pmsProductAttributeType) {
        LambdaQueryWrapper<PmsAttributeType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PmsAttributeType::getCode, pmsProductAttributeType.getCode());
        PmsAttributeType attributeType = pmsAttributeTypeMapper.selectOne(lambdaQueryWrapper);
        if (attributeType != null) {
            throw BizException.build(BizCode.ATTRIBUTETYPE_CODE_REPEAT);
        }
        int count = pmsAttributeTypeMapper.insert(pmsProductAttributeType);
        //新增商品属性以后需要更新商品属性分类数量
        PmsAttribute productAttribute = pmsAttributeMapper.selectById(pmsProductAttributeType.getAttributeId());
        if (pmsProductAttributeType.getClassify().equals(SdsAttributeTypeClassifyEnum.SPEC.getClassify())) {
            productAttribute.setSpecCount(productAttribute.getSpecCount() + 1);
        } else if (pmsProductAttributeType.getClassify().equals(SdsAttributeTypeClassifyEnum.PARAM.getClassify())) {
            productAttribute.setParamCount(productAttribute.getParamCount() + 1);
        }
        pmsAttributeMapper.updateById(productAttribute);
        return count;
    }

    @Override
    public int update(PmsAttributeType pmsProductAttributeType) {
        LambdaQueryWrapper<PmsAttributeType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PmsAttributeType::getCode, pmsProductAttributeType.getCode());
        lambdaQueryWrapper.notIn(PmsAttributeType::getId, pmsProductAttributeType.getId());
        PmsAttributeType attributeType = pmsAttributeTypeMapper.selectOne(lambdaQueryWrapper);
        if (attributeType != null) {
            throw BizException.build(BizCode.ATTRIBUTETYPE_CODE_REPEAT);
        }
        return pmsAttributeTypeMapper.updateById(pmsProductAttributeType);
    }

    @Override
    public PmsAttributeType getItem(String id) {
        return pmsAttributeTypeMapper.selectById(id);
    }

    @Override
    public int delete(String id) {
        //获取分类
        PmsAttributeType pmsProductAttributeType = pmsAttributeTypeMapper.selectById(id);
        PmsAttribute pmsProductAttribute = pmsAttributeMapper.selectById(pmsProductAttributeType.getAttributeId());
        int count = pmsAttributeTypeMapper.deleteById(id);
        if (pmsProductAttributeType.getClassify().equals(SdsAttributeTypeClassifyEnum.SPEC.getClassify())) {
            if (pmsProductAttribute.getSpecCount() >= count) {
                pmsProductAttribute.setSpecCount(pmsProductAttribute.getSpecCount() - count);
            } else {
                pmsProductAttribute.setSpecCount(0);
            }
        } else if (pmsProductAttributeType.getClassify().equals(SdsAttributeTypeClassifyEnum.PARAM.getClassify())) {
            if (pmsProductAttribute.getParamCount() >= count) {
                pmsProductAttribute.setParamCount(pmsProductAttribute.getParamCount() - count);
            } else {
                pmsProductAttribute.setParamCount(0);
            }
        }
        pmsAttributeMapper.updateById(pmsProductAttribute);
        return count;
    }

}
