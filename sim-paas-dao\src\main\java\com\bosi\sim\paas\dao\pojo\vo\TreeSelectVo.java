package com.bosi.sim.paas.dao.pojo.vo;

import com.bosi.sim.paas.dao.model.tds.TdsMenu;
import com.bosi.sim.paas.dao.model.ads.AdsDept;
import com.bosi.sim.paas.dao.model.ads.AdsMenu;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Treeselect树结构实体类
 */
@Data
public class TreeSelectVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 节点ID
     */
    private String id;

    /**
     * 节点名称
     */
    private String label;

    /**
     * 子节点
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TreeSelectVo> children;

    public TreeSelectVo() {

    }

    public TreeSelectVo(AdsDept dept) {
        this.id = dept.getId();
        this.label = dept.getDeptName();
        this.children = dept.getChildren().stream().map(TreeSelectVo::new).collect(Collectors.toList());
    }

    public TreeSelectVo(AdsMenu menu) {
        this.id = menu.getId();
        this.label = menu.getMenuName();
        this.children = menu.getChildren().stream().map(TreeSelectVo::new).collect(Collectors.toList());
    }

    public TreeSelectVo(TdsMenu menu) {
        this.id = menu.getId();
        this.label = menu.getMenuName();
        this.children = menu.getChildren().stream().map(TreeSelectVo::new).collect(Collectors.toList());
    }

}
