package com.bosi.sim.paas.admin.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.dao.mapper.ads.AdsConfigMapper;
import com.bosi.sim.paas.dao.model.ads.AdsConfig;
import com.bosi.sim.paas.admin.server.service.CmsConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;

/**
 * 参数配置 服务层实现
 */
@Service
public class CmsConfigServiceImpl implements CmsConfigService {
    @Autowired
    private AdsConfigMapper configMapper;

    /**
     * 查询参数配置信息
     *
     * @param id 参数配置ID
     * @return 参数配置信息
     */
    @Override
    public AdsConfig selectConfigById(String id) {
        return configMapper.selectById(id);
    }

    /**
     * 根据键名查询参数配置信息
     *
     * @param configKey 参数key
     * @return 参数键值
     */
    @Override
    public String selectConfigByKey(String configKey) {
        LambdaQueryWrapper<AdsConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdsConfig::getConfigKey, configKey);
        AdsConfig retConfig = configMapper.selectOne(queryWrapper);
        if (StringUtils.isNotNull(retConfig)) {
            return retConfig.getConfigValue();
        }
        return StringUtils.EMPTY;
    }

    /**
     * 查询参数配置列表
     *
     * @param config 参数配置信息
     * @return 参数配置集合
     */
    @Override
    public CommonPage<AdsConfig> page(Page<AdsConfig> page, AdsConfig config) {
        LambdaQueryWrapper<AdsConfig> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(config.getConfigName())) {
            queryWrapper.like(AdsConfig::getConfigName, config.getConfigName());
        }
        if (StringUtils.isNotNull(config.getWhetherInner())) {
            queryWrapper.eq(AdsConfig::getWhetherInner, config.getWhetherInner());
        }
        if (StringUtils.isNotEmpty(config.getConfigKey())) {
            queryWrapper.like(AdsConfig::getConfigKey, config.getConfigKey());
        }
        return CommonPage.restPage(configMapper.selectPage(page, queryWrapper));
    }

    /**
     * 新增参数配置
     *
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public int insertConfig(AdsConfig config) {
        return configMapper.insert(config);
    }

    /**
     * 修改参数配置
     *
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public int updateConfig(AdsConfig config) {
        return configMapper.updateById(config);
    }

    /**
     * 批量删除参数信息
     *
     * @param configIds 需要删除的参数ID
     */
    @Override
    public void deleteConfigByIds(String[] configIds) {
        configMapper.deleteBatchIds(Arrays.asList(configIds));
    }

    /**
     * 校验参数键名是否唯一
     *
     * @param config 参数配置信息
     * @return 结果
     */
    @Override
    public boolean checkConfigKeyUnique(AdsConfig config) {
        String configId = StringUtils.isEmpty(config.getId()) ? "" : config.getId();
        LambdaQueryWrapper<AdsConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AdsConfig::getConfigKey, config.getConfigKey());
        AdsConfig info = configMapper.selectOne(queryWrapper);
        return !StringUtils.isNotNull(info) || info.getId().equals(configId);
    }

}
