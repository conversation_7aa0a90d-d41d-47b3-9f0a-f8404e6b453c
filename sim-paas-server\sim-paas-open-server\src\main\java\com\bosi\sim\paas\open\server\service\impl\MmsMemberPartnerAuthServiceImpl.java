package com.bosi.sim.paas.open.server.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.bosi.sim.paas.common.core.api.SystemCode;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.dao.enums.sds.SdsLoginSourceTypeEnum;
import com.bosi.sim.paas.open.server.config.login.LoginPartnerProperties;
import com.bosi.sim.paas.open.server.config.rest.RestTemplateService;
import com.bosi.sim.paas.open.server.domain.dto.MemberParterAuthInfoDto;
import com.bosi.sim.paas.open.server.service.MmsMemberPartnerAuthService;
import com.bosi.sim.paas.open.server.utils.AppleUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户地址管理Service实现类
 */
@Service
@Slf4j
public class MmsMemberPartnerAuthServiceImpl implements MmsMemberPartnerAuthService {

    @Autowired
    private LoginPartnerProperties loginPartnerProperties;

    @Autowired
    private RestTemplateService restTemplateService;

    @Override
    public MemberParterAuthInfoDto googleAuth(String code, String redirectUri) {
        try {
            String tokenUrl = "https://oauth2.googleapis.com/token";
            Map<String, Object> tokenParams = new HashMap<>();
            tokenParams.put("client_id", loginPartnerProperties.getGoogleClientId());
            tokenParams.put("client_secret", loginPartnerProperties.getGoogleClientSecret());
            tokenParams.put("redirect_uri", redirectUri);
            tokenParams.put("grant_type", "authorization_code");
            tokenParams.put("code", code);

            HttpHeaders headers = new HttpHeaders();
            //headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            RequestEntity<Map<String, Object>> requestEntity = new RequestEntity<>(tokenParams, headers, HttpMethod.POST, URI.create(tokenUrl));
            ResponseEntity<JSONObject> responseEntity = restTemplateService.exchange(requestEntity, JSONObject.class);
            JSONObject tokenResponseBody = responseEntity.getBody();
            log.info(tokenResponseBody.toJSONString());

            String accessToken = tokenResponseBody.getString("access_token");
            String userUrl = "https://www.googleapis.com/oauth2/v3/userinfo";
            HttpHeaders userHeaders = new HttpHeaders();
            //userHeaders.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            userHeaders.setBearerAuth(accessToken);
            RequestEntity<String> userRequest = new RequestEntity<>(userHeaders, HttpMethod.GET, URI.create(userUrl));
            ResponseEntity<JSONObject> userResponse = restTemplateService.exchange(userRequest, JSONObject.class);
            JSONObject userResponseBody = userResponse.getBody();
            log.info(userResponseBody.toJSONString());
            MemberParterAuthInfoDto memberParterAuthInfoDto = new MemberParterAuthInfoDto();
            memberParterAuthInfoDto.setPartnerUniqueId(userResponseBody.getString("sub"));
            memberParterAuthInfoDto.setNickname(userResponseBody.getString("name"));
            memberParterAuthInfoDto.setSourceType(SdsLoginSourceTypeEnum.GOOGLE.getSourceType());
            memberParterAuthInfoDto.setEmail(userResponseBody.getString("email"));
            return memberParterAuthInfoDto;
        } catch (Exception e) {
            log.error("googleAuth error", e);
            throw BizException.build(SystemCode.FAILED);
        }
    }

    @Override
    public MemberParterAuthInfoDto githubAuth(String code) {
        try {
            String tokenUrl = "https://github.com/login/oauth/access_token";
            Map<String, String> tokenParams = new HashMap<>();
            tokenParams.put("client_id", loginPartnerProperties.getGithubClientId());
            tokenParams.put("client_secret", loginPartnerProperties.getGithubClientSecret());
            tokenParams.put("code", code);
            HttpHeaders tokenHeaders = new HttpHeaders();
            tokenHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            RequestEntity<Map<String, String>> tokenRequest = new RequestEntity<>(tokenParams, tokenHeaders, HttpMethod.POST, URI.create(tokenUrl));
            ResponseEntity<JSONObject> tokenResponse = restTemplateService.exchange(tokenRequest, JSONObject.class);
            JSONObject tokenResponseBody = tokenResponse.getBody();
            log.info(tokenResponseBody.toJSONString());

            String accessToken = tokenResponseBody.getString("access_token");
            String userUrl = "https://api.github.com/user";
            HttpHeaders userHeaders = new HttpHeaders();
            userHeaders.setBearerAuth(accessToken);
            RequestEntity<String> userRequest = new RequestEntity<>(userHeaders, HttpMethod.GET, URI.create(userUrl));
            ResponseEntity<JSONObject> userResponse = restTemplateService.exchange(userRequest, JSONObject.class);
            JSONObject userResponseBody = userResponse.getBody();
            log.debug(userResponseBody.toJSONString());
            return null;
        } catch (Exception e) {
            log.error("githubAuth error", e);
            throw BizException.build(SystemCode.FAILED);
        }
    }

    @Override
    public MemberParterAuthInfoDto appleAuth(String code) {
        try {
            String tokenUrl = "https://appleid.apple.com/auth/token";
            Map<String, String> tokenParams = new HashMap<>();
            tokenParams.put("client_id", loginPartnerProperties.getAppleClientId());
            tokenParams.put("client_secret", loginPartnerProperties.getAppleClientSecret());
            tokenParams.put("code", code);
            HttpHeaders tokenHeaders = new HttpHeaders();
            tokenHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            RequestEntity<Map<String, String>> tokenRequest = new RequestEntity<>(tokenParams, tokenHeaders, HttpMethod.POST, URI.create(tokenUrl));
            ResponseEntity<JSONObject> tokenResponse = restTemplateService.exchange(tokenRequest, JSONObject.class);
            JSONObject tokenResponseBody = tokenResponse.getBody();
            log.info(tokenResponseBody.toJSONString());

            String idToken = tokenResponseBody.getString("id_token");

            String url = "https://appleid.apple.com/auth/keys";
            RequestEntity keyRequest = new RequestEntity<>(HttpMethod.GET, URI.create(url));
            ResponseEntity<JSONObject> keyResponse = restTemplateService.exchange(keyRequest, JSONObject.class);
            JSONArray authKeys = keyResponse.getBody().getJSONArray("keys");

            if (AppleUtil.verify(authKeys, idToken)) {
                JSONObject userInfo = AppleUtil.parserIdentityToken(idToken);
                log.debug(userInfo.toJSONString());
            }
            return null;
        } catch (Exception e) {
            log.error("appleAuth error", e);
            throw BizException.build(SystemCode.FAILED);
        }

    }
}
