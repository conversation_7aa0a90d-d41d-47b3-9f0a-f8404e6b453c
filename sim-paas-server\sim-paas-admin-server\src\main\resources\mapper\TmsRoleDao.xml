<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bosi.sim.paas.admin.server.dao.TmsRoleDao">

    <sql id="selectRoleVo">
        select distinct r.id, r.role_name, r.distributor_id, r.whether_admin,
                        r.whether_enable, r.create_time, r.remark, d.distributor_name
        from tms_role r
        left join tms_user_role ur on ur.role_id = r.id
        left join tms_distributor d on d.id = r.distributor_id
        left join tms_user u on u.id = ur.user_id
    </sql>

    <select id="selectRoleList" resultType="com.bosi.sim.paas.dao.model.tds.TdsRole">
        select
            distinct r.id,
            r.role_name,
            r.distributor_id,
            r.whether_admin,
            r.whether_enable,
            r.create_time,
            r.remark,
            d.distributor_name
        from tms_role r
        left join tms_distributor d on d.id = r.distributor_id
        where
            r.whether_delete = false
        and
            d.whether_delete = false
        <if test="params.id != null">
            and r.id = #{params.id}
        </if>
        <if test="params.distributorId != null">
            and r.distributor_id = #{params.distributorId}
        </if>
        <if test="params.roleName != null and params.roleName != ''">
            and r.role_name like concat('%', #{params.roleName}, '%')
        </if>
        <if test="params.whetherEnable != null">
            and r.whether_enable = #{params.whetherEnable}
        </if>
        <if test="params.queryBeginTime != null and params.queryBeginTime != ''"><!-- 开始时间检索 -->
            and date_format(r.create_time,'%y%m%d') &gt;= date_format(#{params.queryBeginTime},'%y%m%d')
        </if>
        <if test="params.queryEndTime != null and params.queryEndTime != ''"><!-- 结束时间检索 -->
            and date_format(r.create_time,'%y%m%d') &lt;= date_format(#{params.queryEndTime},'%y%m%d')
        </if>
    </select>

    <select id="selectRolePermissionByUserId" parameterType="String" resultType="com.bosi.sim.paas.dao.model.tds.TdsRole">
        <include refid="selectRoleVo"/>
        where r.whether_delete = false
        and ur.whether_delete = false
        and d.whether_delete = false
        and u.whether_delete = false and ur.user_id = #{userId}
    </select>

    <select id="selectRoleAll" resultType="com.bosi.sim.paas.dao.model.tds.TdsRole">
        <include refid="selectRoleVo"/>
        where r.whether_delete = false
        and ur.whether_delete = false
        and d.whether_delete = false
        and u.whether_delete = false
    </select>

    <select id="selectRoleListByUserId" parameterType="String" resultType="String">
        select
            r.id
        from
            tms_role r
        left join
            tms_user_role ur on ur.role_id = r.id
        left join
            tms_user u on u.id = ur.user_id
        where
            u.id = #{userId}
        and r.whether_delete = false
        and ur.whether_delete = false
        and u.whether_delete = false
    </select>

    <select id="selectRoleById" parameterType="String" resultType="com.bosi.sim.paas.dao.model.tds.TdsRole">
        <include refid="selectRoleVo"/>
        where r.id = #{roleId}
        and r.whether_delete = false
        and ur.whether_delete = false
        and d.whether_delete = false
        and u.whether_delete = false
    </select>

    <select id="selectRolesByUserName" resultType="com.bosi.sim.paas.dao.model.tds.TdsRole">
        <include refid="selectRoleVo"/>
        where r.whether_delete = false
        and ur.whether_delete = false
        and d.whether_delete = false
        and u.whether_delete = false
        and u.user_name = #{userName}
    </select>

</mapper>
