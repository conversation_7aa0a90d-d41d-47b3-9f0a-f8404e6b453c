package com.bosi.sim.paas.dao.enums.sds;


public enum SdsPromotionTypeEnum {

    NULL(0, "无促销"),

    DISCOUNT(1, "打折促销"),

    FULL_REDUCTION(2, "满减促销");

    private Integer promotionType;

    private String desc;

    SdsPromotionTypeEnum(Integer promotionType, String desc) {
        this.promotionType = promotionType;
        this.desc = desc;
    }

    public Integer getPromotionType() {
        return promotionType;
    }

    public String getDesc() {
        return desc;
    }
}
