package com.bosi.sim.paas.dao.model.sds.jsonclz;

import com.bosi.esim.mall.dao.model.sms.jsonclz.DivideAccountJsonarrayClass;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class DivideDetailJsonClass {

    private String distributorId;

    private String referralCode;

    private Integer settleMode;

    private Integer divideMode;

    private BigDecimal totalDivideAmount;

    private List<DivideAccountJsonarrayClass> divideAccountJsonarrayClassList;

}
