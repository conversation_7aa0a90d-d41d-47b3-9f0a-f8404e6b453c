package com.bosi.sim.paas.admin.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.dao.mapper.ads.AdsUserLoginLogMapper;
import com.bosi.sim.paas.dao.model.ads.AdsUserLoginLog;
import com.bosi.sim.paas.admin.server.service.UmsUserLoginLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 操作日志 服务层处理
 */
@Service
public class UmsUserLoginLogServiceImpl implements UmsUserLoginLogService {
    @Autowired
    private AdsUserLoginLogMapper userLoginLogMapper;

    @Override
    public CommonPage<AdsUserLoginLog> page(Page<AdsUserLoginLog> ipage, AdsUserLoginLog userLoginLog) {
        LambdaQueryWrapper<AdsUserLoginLog> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(userLoginLog.getIpaddr())) {
            queryWrapper.like(AdsUserLoginLog::getIpaddr, userLoginLog.getIpaddr());
        }
        if (StringUtils.isNotNull(userLoginLog.getWhetherSuccess())) {
            queryWrapper.eq(AdsUserLoginLog::getWhetherSuccess, userLoginLog.getWhetherSuccess());
        }
        if (StringUtils.isNotEmpty(userLoginLog.getLoginName())) {
            queryWrapper.like(AdsUserLoginLog::getLoginName, userLoginLog.getLoginName());
        }
        if (StringUtils.isNotEmpty(userLoginLog.getQueryBeginTime())) {
            queryWrapper.ge(AdsUserLoginLog::getLoginTime, userLoginLog.getQueryBeginTime());
        }
        if (StringUtils.isNotEmpty(userLoginLog.getQueryEndTime())) {
            queryWrapper.le(AdsUserLoginLog::getLoginTime, userLoginLog.getQueryEndTime());
        }
        IPage<AdsUserLoginLog> page = userLoginLogMapper.selectPage(ipage, queryWrapper);
        return CommonPage.restPage(page);
    }

    @Override
    public AdsUserLoginLog getById(String id) {
        return userLoginLogMapper.selectById(id);
    }

}
