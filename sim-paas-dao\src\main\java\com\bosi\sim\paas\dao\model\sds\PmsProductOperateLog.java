package com.bosi.esim.mall.dao.model.pms;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.esim.mall.dao.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("pms_product_operate_log")
public class PmsProductOperateLog extends BaseEntity {
    private String productId;

    private Integer operateType;

    private String oldProductInfo;

    private String editProductInfo;

    @ApiModelProperty(value = "操作人")
    private String operateMan;

}
