package com.bosi.sim.paas.dao.model.sds;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import com.bosi.sim.paas.dao.model.ads.AdsCompanyAddress;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("sds_order_return_apply")
public class SdsOrderReturnApply extends BaseEntity {
    @ApiModelProperty(value = "订单id")
    private String orderId;

    @ApiModelProperty(value = "公司收货地址表id")
    private String companyAddressId;

    @ApiModelProperty(value = "会员用户名")
    private String memberId;

    @ApiModelProperty(value = "退款金额")
    private BigDecimal returnAmount;

    @ApiModelProperty(value = "退货人姓名")
    private String returnName;

    @ApiModelProperty(value = "退货人电话")
    private String returnPhone;

    @ApiModelProperty(value = "申请状态：0->待处理；1->退货中；2->已完成；3->已拒绝")
    private Integer status;

    @ApiModelProperty(value = "处理时间")
    private Date handleTime;

    @ApiModelProperty(value = "原因")
    private String reason;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "凭证图片，以逗号隔开")
    private String proofPics;

    @ApiModelProperty(value = "处理备注")
    private String handleNote;

    @ApiModelProperty(value = "处理人员")
    private String handleMan;

    private String receiveMan;

    private Date receiveTime;

    private String receiveNote;

    @TableField(exist = false)
    private AdsCompanyAddress companyAddress;

}
