package com.bosi.sim.paas.dao.model.sds;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("sds_order_divide_settle_history")
public class SdsOrderDivideSettleHistory extends BaseEntity {
    private Integer settleMode;

    private String batchNo;

    private String jobId;

    private String orderDivideSettleId;

    private Boolean whetherSuccess;

    private String failReason;

    private String settleAccountId;

    private BigDecimal settleAmount;

    private String operateUser;
}
