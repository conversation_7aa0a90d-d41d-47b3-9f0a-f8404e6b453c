<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bosi.sim.paas.admin.server.dao.TmsNotifyRecordDao">

    <select id="page" resultType="com.bosi.sim.paas.dao.model.tds.TdsNotifyRecord">
        select
            a.*,d.distributor_name
        from
            tms_notify_record as a
        left join
            tms_distributor as d on a.distributor_id = d.id
        <where>
            <if test="true">
                and a.whether_delete = false
            </if>
            <if test="params.distributorName != null and params.distributorName != ''"><!-- 开始时间检索 -->
                and d.distributor_name like concat('%', #{params.distributorName}, '%')
            </if>
            <if test="params.notifyId != null and params.notifyId != ''"><!-- 开始时间检索 -->
                and a.notify_id = #{params.notifyId}
            </if>
        </where>
    </select>

    <select id="pageDistributorForNotify" resultType="com.bosi.sim.paas.dao.model.tds.TdsTenant">
        select
            a.*,(select count(1) from tms_notify_record where distributor_id = a.id and notify_id = #{params.notifyId} and whether_delete = false) as notify_count
        from
            tms_distributor as a
        <where>
            <if test="true">
                and a.whether_delete = false
            </if>
            <if test="params.distributorName != null and params.distributorName != ''"><!-- 开始时间检索 -->
                and a.distributor_name like concat('%', #{params.distributorName}, '%')
            </if>
        </where>
    </select>

    <insert id="insertList" useGeneratedKeys="true" keyProperty="id">
        <foreach collection="list" item="item" separator=";">
            insert into tms_notify_record
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">id,</if>
                <if test="item.notifyId != null">notify_id,</if>
                <if test="item.distributorId != null">distributor_id,</if>
                <if test="item.whetherRead != null">whether_read,</if>
            </trim>
            values
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">#{item.id},</if>
                <if test="item.notifyId != null">#{item.notifyId},</if>
                <if test="item.distributorId != null">#{item.distributorId},</if>
                <if test="item.whetherRead != null">#{item.whetherRead},</if>
            </trim>
        </foreach>
    </insert>

</mapper>
