package com.bosi.sim.paas.dao.enums.sds;


public enum SdsSettleSituationEnum {

    NOT_SETTLE(0, "待结算"),

    PART_SETTLE(1, "部分结算"),

    ALL_SETTLED(2, "全部结算");

    private Integer settleSituation;

    private String desc;

    SdsSettleSituationEnum(Integer settleSituation, String desc) {
        this.settleSituation = settleSituation;
        this.desc = desc;
    }

    public Integer getSettleSituation() {
        return settleSituation;
    }

    public String getDesc() {
        return desc;
    }
}
