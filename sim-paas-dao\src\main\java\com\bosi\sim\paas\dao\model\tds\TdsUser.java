package com.bosi.sim.paas.dao.model.tms;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 用户对象
 */
@Data
@TableName("tms_user")
public class TdsUser extends BaseEntity {

    private String tenantId;

    /**
     * 用户账号
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 用户性别
     */
    private Integer sex;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 密码
     */
    private String password;

    /**
     * 帐号状态（0正常 1停用）
     */
    private Boolean whetherEnable;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private Date loginDate;

    private Boolean whetherAdmin;

    /**
     * 角色ID
     */
    @TableField(exist = false)
    private String roleId;

    @TableField(exist = false)
    private String tenantName;

}
