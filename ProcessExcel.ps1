# Excel数据分裂脚本
# 处理MCCMNC列中包含'/'的数据，将其分裂成多行

Write-Host "Excel数据分裂工具" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green

$inputFile = "doc\test.xlsx"
$outputFile = "doc\test1.xlsx"

# 检查输入文件是否存在
if (-not (Test-Path $inputFile)) {
    Write-Error "错误: 找不到文件 $inputFile"
    exit 1
}

Write-Host "找到Excel文件: $inputFile" -ForegroundColor Yellow

try {
    # 创建Excel应用程序对象
    $excel = New-Object -ComObject Excel.Application
    $excel.Visible = $false
    $excel.DisplayAlerts = $false

    Write-Host "正在打开Excel文件..." -ForegroundColor Yellow

    # 打开工作簿
    $workbook = $excel.Workbooks.Open((Resolve-Path $inputFile).Path)
    $worksheet = $workbook.Worksheets.Item(1)

    # 获取使用的范围
    $usedRange = $worksheet.UsedRange
    $rowCount = $usedRange.Rows.Count
    $colCount = $usedRange.Columns.Count

    Write-Host "原始数据: $rowCount 行, $colCount 列" -ForegroundColor Yellow

    # 查找MCCMNC列
    $mccmncColumn = -1
    for ($col = 1; $col -le $colCount; $col++) {
        $headerValue = $worksheet.Cells.Item(1, $col).Value2
        if ($headerValue -eq "MCCMNC") {
            $mccmncColumn = $col
            break
        }
    }

    if ($mccmncColumn -eq -1) {
        Write-Error "未找到MCCMNC列"
        $workbook.Close($false)
        $excel.Quit()
        exit 1
    }
    
    Write-Host "找到MCCMNC列: 第 $mccmncColumn 列" -ForegroundColor Yellow
    
    # 读取所有数据
    $allData = @()
    
    # 添加表头
    $headerRow = @()
    for ($col = 1; $col -le $colCount; $col++) {
        $headerRow += $worksheet.Cells.Item(1, $col).Value2
    }
    $allData += ,$headerRow
    
    $splitCount = 0
    
    # 处理数据行
    for ($row = 2; $row -le $rowCount; $row++) {
        $currentRow = @()
        for ($col = 1; $col -le $colCount; $col++) {
            $cellValue = $worksheet.Cells.Item($row, $col).Value2
            if ($cellValue -eq $null) {
                $currentRow += ""
            } else {
                $currentRow += $cellValue.ToString()
            }
        }
        
        $mccmncValue = $currentRow[$mccmncColumn - 1]
        
        if ($mccmncValue -and $mccmncValue.Contains("/")) {
            # 分割包含'/'的数据
            $parts = $mccmncValue.Split("/")
            $splitCount++
            
            Write-Host "行 $row : '$mccmncValue' 分裂为 $($parts.Length) 行" -ForegroundColor Cyan
            
            # 为每个分割后的值创建新行
            foreach ($part in $parts) {
                $newRow = $currentRow.Clone()
                $newRow[$mccmncColumn - 1] = $part.Trim()
                $allData += ,$newRow
            }
        } else {
            # 不包含'/'的行直接添加
            $allData += ,$currentRow
        }
    }
    
    # 关闭原始工作簿
    $workbook.Close($false)
    
    Write-Host "创建新的Excel文件..." -ForegroundColor Yellow
    
    # 创建新工作簿
    $newWorkbook = $excel.Workbooks.Add()
    $newWorksheet = $newWorkbook.Worksheets.Item(1)
    
    # 写入处理后的数据
    for ($row = 0; $row -lt $allData.Count; $row++) {
        for ($col = 0; $col -lt $allData[$row].Count; $col++) {
            $newWorksheet.Cells.Item($row + 1, $col + 1) = $allData[$row][$col]
        }
    }
    
    # 保存新文件
    $outputPath = (Resolve-Path "doc").Path + "\test1.xlsx"
    $newWorkbook.SaveAs($outputPath)
    $newWorkbook.Close($false)
    
    Write-Host "处理完成!" -ForegroundColor Green
    Write-Host "原始数据行数: $rowCount" -ForegroundColor Green
    Write-Host "分裂了 $splitCount 行包含'/'的数据" -ForegroundColor Green
    Write-Host "处理后数据行数: $($allData.Count)" -ForegroundColor Green
    Write-Host "结果已保存到: $outputFile" -ForegroundColor Green
    
} catch {
    Write-Error "处理过程中出现错误: $($_.Exception.Message)"
} finally {
    # 清理Excel对象
    if ($excel) {
        $excel.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($excel) | Out-Null
    }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}

Write-Host "脚本执行完成" -ForegroundColor Green
