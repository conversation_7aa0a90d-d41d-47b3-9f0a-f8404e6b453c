package com.bosi.sim.paas.admin.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.common.optlog.dto.OperateLogDTO;
import com.bosi.sim.paas.dao.mapper.ads.AdsOperateLogMapper;
import com.bosi.sim.paas.dao.model.ads.AdsOperateLog;
import com.bosi.sim.paas.admin.server.service.UmsOperateLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;

/**
 * 操作日志 服务层处理
 */
@Service
public class UmsOperateLogServiceImpl implements UmsOperateLogService {
    @Autowired
    private AdsOperateLogMapper operLogMapper;

    /**
     * 新增操作日志
     *
     * @param operLog 操作日志对象
     * @return 结果
     */
    @Override
    public void saveLog(OperateLogDTO operLog) {
        operLogMapper.insert(BeanUtil.copyProperties(operLog, AdsOperateLog.class));
    }

    /**
     * 查询系统操作日志集合
     *
     * @param operLog 操作日志对象
     * @return 操作日志集合
     */
    @Override
    public CommonPage<AdsOperateLog> page(Page<AdsOperateLog> ipage, AdsOperateLog operLog) {
        LambdaQueryWrapper<AdsOperateLog> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(operLog.getOperIp())) {
            queryWrapper.like(AdsOperateLog::getOperIp, operLog.getOperIp());
        }
        if (StringUtils.isNotEmpty(operLog.getTitle())) {
            queryWrapper.like(AdsOperateLog::getTitle, operLog.getTitle());
        }
        if (StringUtils.isNotNull(operLog.getWhetherSuccess())) {
            queryWrapper.eq(AdsOperateLog::getWhetherSuccess, operLog.getWhetherSuccess());
        }
        if (StringUtils.isNotEmpty(operLog.getOperName())) {
            queryWrapper.like(AdsOperateLog::getOperName, operLog.getOperName());
        }
        if (StringUtils.isNotEmpty(operLog.getQueryBeginTime())) {
            queryWrapper.ge(AdsOperateLog::getOperTime, operLog.getQueryBeginTime());
        }
        if (StringUtils.isNotEmpty(operLog.getQueryEndTime())) {
            queryWrapper.le(AdsOperateLog::getOperTime, operLog.getQueryEndTime());
        }
        IPage<AdsOperateLog> page = operLogMapper.selectPage(ipage, queryWrapper);
        return CommonPage.restPage(page);
    }

    /**
     * 批量删除系统操作日志
     *
     * @param operIds 需要删除的操作日志ID
     * @return 结果
     */
    @Override
    public int deleteOperLogByIds(String[] operIds) {
        return operLogMapper.deleteBatchIds(Arrays.asList(operIds));
    }

    /**
     * 查询操作日志详细
     *
     * @param operId 操作ID
     * @return 操作日志对象
     */
    @Override
    public AdsOperateLog selectOperLogById(String operId) {
        return operLogMapper.selectById(operId);
    }

}
