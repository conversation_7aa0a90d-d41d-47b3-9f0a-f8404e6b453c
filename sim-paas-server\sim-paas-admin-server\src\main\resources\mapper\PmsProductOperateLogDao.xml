<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bosi.sim.paas.admin.server.dao.PmsProductOperateLogDao">

	<insert id="insertList" useGeneratedKeys="true" keyProperty="id">
		<foreach collection="list" item="item" separator=";">
			insert into sds_product_operate_log
			<trim prefix="(" suffix=")" suffixOverrides=",">
				<if test="item.id != null">id,</if>
				<if test="item.productId != null">product_id,</if>
				<if test="item.operateType != null">operate_type,</if>
				<if test="item.oldProductInfo != null">old_product_info,</if>
				<if test="item.editProductInfo != null">edit_product_info,</if>
				<if test="item.operateMan != null">operate_man,</if>
			</trim>
			values
			<trim prefix="(" suffix=")" suffixOverrides=",">
				<if test="item.id != null">#{item.id},</if>
				<if test="item.productId != null">#{item.productId},</if>
				<if test="item.operateType != null">#{item.operateType},</if>
				<if test="item.oldProductInfo != null">#{item.oldProductInfo},</if>
				<if test="item.editProductInfo != null">#{item.editProductInfo},</if>
				<if test="item.operateMan != null">#{item.operateMan},</if>
			</trim>
		</foreach>
	</insert>

</mapper>
