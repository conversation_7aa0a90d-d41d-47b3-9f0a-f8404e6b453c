<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bosi.sim.paas.tenant.server.dao.TmsUserDao">

	<resultMap id="SysUserResult" type="com.bosi.sim.paas.dao.model.tds.TdsUser" >
		<id     property="id"       	column="id"           />
		<result property="distributorId"       column="distributor_id"      />
		<result property="userName"     column="user_name"    />
		<result property="nickName"     column="nick_name"    />
		<result property="email"        column="email"        />
		<result property="phone"  		column="phone"  	  />
		<result property="sex"          column="sex"          />
		<result property="avatar"       column="avatar"       />
		<result property="password"     column="password"     />
		<result property="whetherEnable"       column="whether_enable"       />
		<result property="whetherDelete"      column="whether_delete"     />
		<result property="loginIp"      column="login_ip"     />
		<result property="loginDate"    column="login_date"   />
		<result property="createBy"     column="create_by"    />
		<result property="createTime"   column="create_time"  />
		<result property="updateBy"     column="update_by"    />
		<result property="updateTime"   column="update_time"  />
		<result property="remark"       column="remark"       />
		<result property="distributorName"       column="distributor_name"       />
		<result property="roleId"       column="role_id"       />
	</resultMap>

	<sql id="selectUserVo">
        select
            u.*,
        	d.distributor_name,
        	r.id role_id
        from
            tms_user u
		left join
            tms_distributor d on u.distributor_id = d.id
		left join
            tms_user_role ur on u.id = ur.user_id
		left join
            tms_role r on r.id = ur.role_id
    </sql>

    <select id="selectUserList" resultMap="SysUserResult">
		select u.*
		from tms_user u
		left join tms_distributor d on u.distributor_id = d.id
		where u.whether_delete = false
		<if test="params.id != null">
			AND u.id = #{id}
		</if>
		<if test="params.userName != null and params.userName != ''">
			AND u.user_name like concat('%', #{params.userName}, '%')
		</if>
		<if test="params.whetherEnable != null">
			AND u.whether_enable = #{params.whetherEnable}
		</if>
		<if test="params.phone != null and params.phone != ''">
			AND u.phone like concat('%', #{params.phone}, '%')
		</if>
		<if test="params.queryBeginTime != null and params.queryBeginTime != ''"><!-- 开始时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.queryBeginTime},'%y%m%d')
		</if>
		<if test="params.queryEndTime != null and params.queryEndTime != ''"><!-- 结束时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.queryEndTime},'%y%m%d')
		</if>
		<if test="params.distributorId != null">
			AND u.distributor_id = #{params.distributorId}
		</if>
	</select>

	<select id="selectUserById" resultMap="SysUserResult">
		<include refid="selectUserVo"/>
		where u.id = #{userId} and u.whether_delete = false
	</select>

	<select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
	    <include refid="selectUserVo"/>
		where u.user_name = #{userName} and u.whether_delete = false
	</select>

	<select id="checkUserNameUnique" parameterType="String" resultMap="SysUserResult">
		select id, user_name from tms_user where user_name = #{userName} and whether_delete = false limit 1
	</select>

	<select id="checkPhoneUnique" parameterType="String" resultMap="SysUserResult">
		select id, phone from tms_user where phone = #{phone} and whether_delete = false limit 1
	</select>

	<select id="checkEmailUnique" parameterType="String" resultMap="SysUserResult">
		select id, email from tms_user where email = #{email} and whether_delete = false limit 1
	</select>

	<update id="updateUserAvatar">
 		update tms_user set avatar = #{avatar} where user_name = #{userName} and whether_delete = false
	</update>

	<update id="resetUserPwd">
 		update tms_user set password = #{password} where user_name = #{userName} and whether_delete = false
	</update>

</mapper>
