package com.bosi.sim.paas.admin.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.dao.mapper.sds.SdsMemberLevelMapper;
import com.bosi.sim.paas.dao.model.sds.SdsMemberLevel;
import com.bosi.sim.paas.admin.server.service.MmsMemberLevelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 会员等级管理Service实现类
 */
@Service
public class MmsMemberLevelServiceImpl implements MmsMemberLevelService {
    @Autowired
    private SdsMemberLevelMapper memberLevelMapper;

    @Override
    public List<SdsMemberLevel> list(SdsMemberLevel umsMemberLevel) {
        LambdaQueryWrapper<SdsMemberLevel> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotNull(umsMemberLevel.getWhetherDefault())) {
            queryWrapper.eq(SdsMemberLevel::getWhetherDefault, umsMemberLevel.getWhetherDefault());
        }
        return memberLevelMapper.selectList(queryWrapper);
    }
}
