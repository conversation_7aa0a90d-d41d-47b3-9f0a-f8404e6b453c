package com.bosi.sim.paas.tenant.server.controller;

import com.alibaba.fastjson2.JSONObject;
import com.bosi.sim.paas.common.core.exception.CommonResult;
import com.bosi.sim.paas.common.security.auth.CurrentAuthorization;
import com.bosi.sim.paas.dao.model.tds.TdsMenu;
import com.bosi.sim.paas.tenant.server.service.TmsMenuService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 菜单信息
 */
@RestController
@Api(tags = "TmsMenuController", value = "后台菜单管理")
@RequestMapping("/tms/menu")
public class TmsMenuController {
    @Autowired
    private TmsMenuService menuService;

    /**
     * 获取菜单下拉树列表
     */
    @GetMapping("/listMenuAll")
    public CommonResult listMenuAll() {
        List<TdsMenu> menus = menuService.selectAll();
        return CommonResult.success(menuService.buildMenuTreeSelect(menus));
    }

    /**
     * 加载对应角色菜单列表树
     */
    @GetMapping("/roleMenuTreeselect/{roleId}")
    public CommonResult roleMenuTreeselect(@PathVariable("roleId") String roleId) {
        List<TdsMenu> menus = menuService.selectAll();
        JSONObject ajax = new JSONObject();
        ajax.put("checkedKeys", menuService.selectMenuListByRoleId(roleId));
        ajax.put("menus", menuService.buildMenuTreeSelect(menus));
        return CommonResult.success(ajax);
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public CommonResult getRouters() {
        String userId = CurrentAuthorization.getUserId();
        List<TdsMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return CommonResult.success(menuService.buildMenus(menus));
    }
}
