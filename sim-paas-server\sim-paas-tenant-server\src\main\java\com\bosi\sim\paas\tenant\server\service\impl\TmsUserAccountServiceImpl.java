package com.bosi.sim.paas.tenant.server.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.dao.model.tds.TdsTenantAccount;
import com.bosi.sim.paas.tenant.server.dao.TmsUserAccountDao;
import com.bosi.sim.paas.tenant.server.service.TmsUserAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 分销商管理 服务实现
 */
@Service
public class TmsUserAccountServiceImpl implements TmsUserAccountService {

    @Autowired
    private TmsUserAccountDao userAccountDao;

    @Override
    public CommonPage<TdsTenantAccount> page(Page<TdsTenantAccount> page, TdsTenantAccount userAccount) {
        return CommonPage.restPage(userAccountDao.page(page, userAccount));
    }
}
