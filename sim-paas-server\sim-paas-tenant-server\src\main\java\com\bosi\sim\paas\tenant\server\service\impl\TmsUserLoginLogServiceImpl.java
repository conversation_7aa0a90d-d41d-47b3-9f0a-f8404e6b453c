package com.bosi.sim.paas.tenant.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.dao.mapper.tms.TmsUserLoginLogMapper;
import com.bosi.sim.paas.dao.model.tds.TdsUserLoginLog;
import com.bosi.sim.paas.tenant.server.service.TmsUserLoginLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 操作日志 服务层处理
 */
@Service
public class TmsUserLoginLogServiceImpl implements TmsUserLoginLogService {
    @Autowired
    private TmsUserLoginLogMapper userLoginLogMapper;

    @Override
    public CommonPage<TdsUserLoginLog> page(Page<TdsUserLoginLog> ipage, TdsUserLoginLog userLoginLog) {
        LambdaQueryWrapper<TdsUserLoginLog> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(userLoginLog.getIpaddr())) {
            queryWrapper.like(TdsUserLoginLog::getIpaddr, userLoginLog.getIpaddr());
        }
        if (StringUtils.isNotNull(userLoginLog.getWhetherSuccess())) {
            queryWrapper.eq(TdsUserLoginLog::getWhetherSuccess, userLoginLog.getWhetherSuccess());
        }
        if (StringUtils.isNotEmpty(userLoginLog.getLoginName())) {
            queryWrapper.like(TdsUserLoginLog::getLoginName, userLoginLog.getLoginName());
        }
        if (StringUtils.isNotEmpty(userLoginLog.getQueryBeginTime())) {
            queryWrapper.ge(TdsUserLoginLog::getLoginTime, userLoginLog.getQueryBeginTime());
        }
        if (StringUtils.isNotEmpty(userLoginLog.getQueryEndTime())) {
            queryWrapper.le(TdsUserLoginLog::getLoginTime, userLoginLog.getQueryEndTime());
        }
        if (StringUtils.isNotNull(userLoginLog.getDistributorId())) {
            queryWrapper.eq(TdsUserLoginLog::getDistributorId, userLoginLog.getDistributorId());
        }
        IPage<TdsUserLoginLog> page = userLoginLogMapper.selectPage(ipage, queryWrapper);
        return CommonPage.restPage(page);
    }

    @Override
    public TdsUserLoginLog getById(String id) {
        return userLoginLogMapper.selectById(id);
    }

}
