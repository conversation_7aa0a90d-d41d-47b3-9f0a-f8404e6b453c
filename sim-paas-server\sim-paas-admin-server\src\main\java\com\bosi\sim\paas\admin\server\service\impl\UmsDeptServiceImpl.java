package com.bosi.sim.paas.admin.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bosi.sim.paas.common.core.api.BizCode;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.Convert;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.dao.mapper.ads.AdsDeptMapper;
import com.bosi.sim.paas.dao.mapper.ads.AdsRoleMapper;
import com.bosi.sim.paas.dao.model.ads.AdsDept;
import com.bosi.sim.paas.dao.model.ads.AdsRole;
import com.bosi.sim.paas.dao.pojo.vo.TreeSelectVo;
import com.bosi.sim.paas.admin.server.dao.UmsDeptDao;
import com.bosi.sim.paas.admin.server.service.UmsDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 部门管理 服务实现
 */
@Service
public class UmsDeptServiceImpl implements UmsDeptService {
    @Autowired
    private AdsDeptMapper deptMapper;

    @Autowired
    private UmsDeptDao deptDao;

    @Autowired
    private AdsRoleMapper roleMapper;

    @Override
    public List<AdsDept> list(AdsDept umsDept) {
        LambdaQueryWrapper<AdsDept> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotNull(umsDept.getWhetherEnable())) {
            wrapper.eq(AdsDept::getWhetherEnable, umsDept.getWhetherEnable());
        }
        if (StringUtils.isNotEmpty(umsDept.getDeptName())) {
            wrapper.like(AdsDept::getDeptName, umsDept.getDeptName());
        }
        return deptMapper.selectList(wrapper);
    }

    /**
     * 查询部门树结构信息
     *
     * @param dept 部门信息
     * @return 部门树信息集合
     */
    @Override
    public List<TreeSelectVo> selectDeptTreeList(AdsDept dept) {
        LambdaQueryWrapper<AdsDept> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotNull(dept.getId())) {
            queryWrapper.eq(AdsDept::getId, dept.getId());
        }
        if (StringUtils.isNotNull(dept.getParentId())) {
            queryWrapper.eq(AdsDept::getParentId, dept.getParentId());
        }
        if (StringUtils.isNotNull(dept.getWhetherEnable())) {
            queryWrapper.eq(AdsDept::getWhetherEnable, dept.getWhetherEnable());
        }
        if (StringUtils.isNotEmpty(dept.getDeptName())) {
            queryWrapper.like(AdsDept::getDeptName, dept.getDeptName());
        }
        List<AdsDept> depts = deptMapper.selectList(queryWrapper);
        return buildDeptTreeSelect(depts);
    }

    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    @Override
    public List<AdsDept> buildDeptTree(List<AdsDept> depts) {
        List<AdsDept> returnList = new ArrayList<>();
        List<String> tempList = depts.stream().map(AdsDept::getId).collect(Collectors.toList());
        for (AdsDept dept : depts) {
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dept.getParentId())) {
                recursionFn(depts, dept);
                returnList.add(dept);
            }
        }
        if (returnList.isEmpty()) {
            returnList = depts;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelectVo> buildDeptTreeSelect(List<AdsDept> depts) {
        List<AdsDept> deptTrees = buildDeptTree(depts);
        return deptTrees.stream().map(TreeSelectVo::new).collect(Collectors.toList());
    }

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    @Override
    public List<String> selectDeptListByRoleId(String roleId) {
        AdsRole role = roleMapper.selectOne(new LambdaQueryWrapper<AdsRole>().eq(AdsRole::getId, roleId));
        return deptDao.selectDeptListByRoleId(roleId, role.getWhetherDeptStrictly());
    }

    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    @Override
    public AdsDept selectDeptById(String deptId) {
        return deptMapper.selectById(deptId);
    }

    /**
     * 根据ID查询所有子部门（正常状态）
     *
     * @param deptId 部门ID
     * @return 子部门数
     */
    @Override
    public int selectNormalChildrenDeptById(String deptId) {
        return deptDao.selectNormalChildrenDeptById(deptId);
    }

    /**
     * 是否存在子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    @Override
    public boolean hasChildByDeptId(String deptId) {
        int result = deptDao.hasChildByDeptId(deptId);
        return result > 0;
    }

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    @Override
    public boolean checkDeptExistUser(String deptId) {
        int result = deptDao.checkDeptExistUser(deptId);
        return result > 0;
    }

    /**
     * 校验部门名称是否唯一
     *
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public boolean checkDeptNameUnique(AdsDept dept) {
        String deptId = StringUtils.isNull(dept.getId()) ? "" : dept.getId();
        AdsDept info = deptDao.checkDeptNameUnique(dept.getDeptName(), dept.getParentId());
        return StringUtils.isNull(info) || info.getId().equals(deptId);
    }

    /**
     * 新增保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public int insertDept(AdsDept dept) {
        AdsDept info = deptMapper.selectById(dept.getParentId());
        // 如果父节点不为正常状态,则不允许新增子节点
        if (!info.getWhetherEnable()) {
            throw BizException.build(BizCode.DEPT_DISABLE);
        }
        dept.setAncestors(info.getAncestors() + "," + dept.getParentId());
        return deptMapper.insert(dept);
    }

    /**
     * 修改保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public int updateDept(AdsDept dept) {
        AdsDept newParentDept = deptMapper.selectById(dept.getParentId());
        AdsDept oldDept = deptMapper.selectById(dept.getId());
        if (StringUtils.isNotNull(newParentDept) && StringUtils.isNotNull(oldDept)) {
            String newAncestors = newParentDept.getAncestors() + "," + newParentDept.getId();
            String oldAncestors = oldDept.getAncestors();
            dept.setAncestors(newAncestors);
            updateDeptChildren(dept.getId(), newAncestors, oldAncestors);
        }
        int result = deptMapper.updateById(dept);
        if (dept.getWhetherEnable() && StringUtils.isNotEmpty(dept.getAncestors())
                && !StringUtils.equals("0", dept.getAncestors())) {
            // 如果该部门是启用状态，则启用该部门的所有上级部门
            updateParentDeptStatusNormal(dept);
        }
        return result;
    }

    /**
     * 修改该部门的父级部门状态
     *
     * @param dept 当前部门
     */
    private void updateParentDeptStatusNormal(AdsDept dept) {
        String ancestors = dept.getAncestors();
        String[] deptIds = Convert.toStrArray(ancestors);
        deptDao.updateDeptStatusNormal(deptIds);
    }

    /**
     * 修改子元素关系
     *
     * @param deptId       被修改的部门ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateDeptChildren(String deptId, String newAncestors, String oldAncestors) {
        List<AdsDept> children = deptDao.selectChildrenDeptById(deptId);
        for (AdsDept child : children) {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (children.size() > 0) {
            deptDao.updateDeptChildren(children);
        }
    }

    /**
     * 删除部门管理信息
     *
     * @param deptId 部门ID
     * @return 结果
     */
    @Override
    public int deleteDeptById(String deptId) {
        return deptMapper.deleteById(deptId);
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<AdsDept> list, AdsDept t) {
        // 得到子节点列表
        List<AdsDept> childList = getChildList(list, t);
        t.setChildren(childList);
        for (AdsDept tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<AdsDept> getChildList(List<AdsDept> list, AdsDept t) {
        List<AdsDept> tlist = new ArrayList<>();
        for (AdsDept n : list) {
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().equals(t.getId())) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<AdsDept> list, AdsDept t) {
        return getChildList(list, t).size() > 0;
    }
}
