package com.bosi.sim.paas.dao.model.sds;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("sds_member_login_log")
public class SdsMemberLoginLog extends BaseEntity {
    private String memberId;

    private String ip;

    @ApiModelProperty(value = "登录类型：1->登入;2->登出")
    private Integer loginType;

    private String loginAddress;

    private String loginDevice;


}
