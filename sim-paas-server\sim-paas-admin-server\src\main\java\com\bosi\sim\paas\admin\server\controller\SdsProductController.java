package com.bosi.sim.paas.admin.server.controller;

import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.exception.CommonResult;
import com.bosi.sim.paas.common.core.utils.PageUtil;
import com.bosi.sim.paas.dao.model.sds.SdsProduct;
import com.bosi.sim.paas.admin.server.service.SdsProductService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 商品管理Controller
 */
@RestController
@Api(tags = "SdsProductController", value = "商品管理")
@RequestMapping("/sds/product")
public class SdsProductController {
    @Autowired
    private SdsProductService productService;

    @ApiOperation("查询商品")
    @GetMapping("/page")
    public CommonResult<CommonPage<SdsProduct>> page(SdsProduct productQueryParam,
                                                     @RequestParam(value = "pageSize") Integer pageSize,
                                                     @RequestParam(value = "pageNum") Integer pageNum) {
        CommonPage<SdsProduct> commonPage = productService.page(PageUtil.buildPage(pageNum, pageSize), productQueryParam);
        return CommonResult.success(commonPage);
    }

    @ApiOperation("根据商品名称或货号模糊查询")
    @GetMapping("/list")
    public CommonResult<List<SdsProduct>> simpleList(SdsProduct sdsProduct) {
        List<SdsProduct> productList = productService.list(sdsProduct);
        return CommonResult.success(productList);
    }

    @ApiOperation("创建商品")
    @PostMapping("/create")
    public CommonResult create(@RequestBody SdsProduct product) {
        productService.create(product);
        return CommonResult.success();
    }

    @ApiOperation("根据商品id获取商品编辑信息")
    @GetMapping("/updateInfo/{id}")
    public CommonResult<SdsProduct> getUpdateInfo(@PathVariable String id) {
        SdsProduct productResult = productService.getUpdateInfoById(id);
        return CommonResult.success(productResult);
    }

    @ApiOperation("更新商品基础信息")
    @PostMapping("/update/base")
    public CommonResult update(@RequestBody SdsProduct product) {
        productService.updateBase(product);
        return CommonResult.success();
    }

    @ApiOperation("更新商品营销信息")
    @PostMapping("/update/market")
    public CommonResult updateMarket(@RequestBody SdsProduct product) {
        productService.updateMarket(product);
        return CommonResult.success();
    }

    @ApiOperation("更新商品库存信息")
    @PostMapping("/update/stock")
    public CommonResult updateStock(@RequestBody SdsProduct product) {
        productService.updateStock(product);
        return CommonResult.success();
    }

    @ApiOperation("上架商品")
    @PostMapping("/update/batch/publish/{ids}")
    public CommonResult updateBatchPublish(@PathVariable String[] ids) {
        productService.updateBatchPublish(ids);
        return CommonResult.success();
    }

    @ApiOperation("下架商品")
    @PostMapping("/update/batch/unpublish/{ids}")
    public CommonResult updateBatchUnpublish(@PathVariable String[] ids) {
        productService.updateBatchUnpublish(ids);
        return CommonResult.success();
    }

    @ApiOperation("修改删除状态")
    @DeleteMapping("/delete/{ids}")
    public CommonResult deleteByIds(@PathVariable String[] ids) {
        productService.deleteByIds(ids);
        return CommonResult.success();
    }

}
