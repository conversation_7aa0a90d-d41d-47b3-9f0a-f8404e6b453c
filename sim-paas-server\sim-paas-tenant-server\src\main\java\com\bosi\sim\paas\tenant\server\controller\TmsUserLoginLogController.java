package com.bosi.sim.paas.tenant.server.controller;

import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.exception.CommonResult;
import com.bosi.sim.paas.common.core.utils.PageUtil;
import com.bosi.sim.paas.common.optlog.annotation.OperateLog;
import com.bosi.sim.paas.common.security.annotation.RequiresPermissions;
import com.bosi.sim.paas.common.security.auth.CurrentAuthorization;
import com.bosi.sim.paas.dao.model.tds.TdsUserLoginLog;
import com.bosi.sim.paas.tenant.server.enums.DistributorTokenExtendKeyEnum;
import com.bosi.sim.paas.tenant.server.service.TmsUserLoginLogService;
import com.bosi.sim.paas.tenant.server.service.TmsUserLoginService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 登录日志管理
 */
@RestController
@Api(tags = "TmsLoginLogController", value = "登录日志管理")
@RequestMapping("/tms/userLoginLog")
public class TmsUserLoginLogController {
    @Autowired
    private TmsUserLoginLogService userLoginLogService;

    @Autowired
    private TmsUserLoginService userLoginService;


    @RequiresPermissions("tms:userLoginLog:unlock")
    @OperateLog("账户解锁")
    @GetMapping("/unlock/{userName}")
    public CommonResult unlock(@PathVariable("userName") String userName) {
        userLoginService.deleteLock(userName);
        return CommonResult.success();
    }

    @RequiresPermissions("tms:userLoginLog:page")
    @GetMapping("/page")
    public CommonResult page(@RequestParam(value = "pageNum") Integer pageNum,
                             @RequestParam(value = "pageSize") Integer pageSize, TdsUserLoginLog userLoginLog) {
        userLoginLog.setDistributorId(CurrentAuthorization.getStringFromExtendJson(DistributorTokenExtendKeyEnum.DISTRIBUTORID_KEY.getKey()));
        CommonPage<TdsUserLoginLog> page = userLoginLogService.page(PageUtil.buildPage(pageNum, pageSize), userLoginLog);
        return CommonResult.success(page);
    }

}
