package com.bosi.sim.paas.tenant.server.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.dao.model.tds.TdsRole;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 后台角色管理自定义Dao
 */
public interface TmsRoleDao {
    /**
     * 根据条件分页查询角色数据
     */
    Page<TdsRole> selectRoleList(Page<TdsRole> iPage, @Param("params") TdsRole umsRole);

    /**
     * 根据用户ID查询角色
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<TdsRole> selectRolePermissionByUserId(String userId);

    /**
     * 通过角色ID查询角色
     *
     * @param roleId 角色ID
     * @return 角色对象信息
     */
    TdsRole selectRoleById(String roleId);

    /**
     * 根据用户ID查询角色
     *
     * @param userName 用户名
     * @return 角色列表
     */
    TdsRole selectRolesByUserName(String userName);

    /**
     * 校验角色名称是否唯一
     *
     * @param roleName 角色名称
     * @return 角色信息
     */
    TdsRole checkRoleNameUnique(@Param("roleName") String roleName, @Param("distributorId") String distributorId);

}
