package com.bosi.sim.paas.tenant.server.service.impl;

import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.dao.enums.ComponentEnum;
import com.bosi.sim.paas.dao.enums.LinkPreEnum;
import com.bosi.sim.paas.dao.enums.MenuTypeEnum;
import com.bosi.sim.paas.dao.mapper.tms.TmsUserMapper;
import com.bosi.sim.paas.dao.model.tds.TdsMenu;
import com.bosi.sim.paas.dao.model.tds.TdsUser;
import com.bosi.sim.paas.dao.pojo.vo.MetaVo;
import com.bosi.sim.paas.dao.pojo.vo.RouterVo;
import com.bosi.sim.paas.dao.pojo.vo.TreeSelectVo;
import com.bosi.sim.paas.tenant.server.dao.TmsMenuDao;
import com.bosi.sim.paas.tenant.server.service.TmsMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 菜单 业务层处理
 */
@Service
public class TmsMenuServiceImpl implements TmsMenuService {

    @Autowired
    private TmsUserMapper userMapper;

    @Autowired
    private TmsMenuDao menuDao;

    /**
     * 根据用户查询系统菜单列表
     *
     * @return 菜单列表
     */
    @Override
    public List<TdsMenu> selectAll() {
        return menuDao.selectMenuList(new TdsMenu());
    }


    /**
     * 获取菜单数据权限
     *
     * @return 菜单权限信息
     */
    @Override
    public Set<String> getMenuPermission(TdsUser user) {
        Set<String> perms = new HashSet<String>();
        // 管理员拥有所有权限
        if (user.getWhetherAdmin()) {
            perms.add("*:*:*");
        } else {
            perms.addAll(this.selectMenuPermsByUserId(user.getId()));
        }
        return perms;
    }

    @Override
    public Set<String> selectMenuPermsByUserId(String userId) {
        List<String> perms = menuDao.selectMenuPermsByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (String perm : perms) {
            if (StringUtils.isNotEmpty(perm)) {
                permsSet.addAll(Arrays.asList(perm.trim().split(",")));
            }
        }
        return permsSet;
    }

    /**
     * 根据用户ID查询菜单
     *
     * @param userId 用户名称
     * @return 菜单列表
     */
    @Override
    public List<TdsMenu> selectMenuTreeByUserId(String userId) {
        TdsUser tmsUser = userMapper.selectById(userId);
        List<TdsMenu> menus;
        // 管理员显示所有菜单信息
        if (tmsUser.getWhetherAdmin()) {
            menus = menuDao.selectMenuTreeAll();
        } else {
            menus = menuDao.selectMenuTreeByUserId(userId);
        }
        return getChildPerms(menus, "0");
    }

    /**
     * 根据角色ID查询菜单树信息
     *
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    @Override
    public List<TdsMenu> selectMenuListByRoleId(String roleId) {
        return menuDao.selectMenuListByRoleId(roleId);
    }

    /**
     * 构建前端路由所需要的菜单
     *
     * @param menus 菜单列表
     * @return 路由列表
     */
    @Override
    public List<RouterVo> buildMenus(List<TdsMenu> menus) {
        List<RouterVo> routers = new LinkedList<>();
        for (TdsMenu menu : menus) {
            RouterVo router = new RouterVo();
            router.setHidden(!menu.getWhetherVisible());
            router.setName(getRouteName(menu));
            router.setPath(getRouterPath(menu));
            router.setComponent(getComponent(menu));
            router.setQuery(menu.getQuery());
            router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), menu.getWhetherCache(), menu.getPath()));
            List<TdsMenu> cMenus = menu.getChildren();
            if (StringUtils.isNotEmpty(cMenus) && MenuTypeEnum.CATALOG.getValue().equals(menu.getMenuType())) {
                router.setAlwaysShow(true);
                router.setRedirect("noRedirect");
                router.setChildren(buildMenus(cMenus));
            } else if (isMenuFrame(menu)) {
                router.setMeta(null);
                List<RouterVo> childrenList = new ArrayList<>();
                RouterVo children = new RouterVo();
                children.setPath(menu.getPath());
                children.setComponent(menu.getComponent());
                children.setName(StringUtils.capitalize(menu.getPath()));
                children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), menu.getWhetherCache(), menu.getPath()));
                children.setQuery(menu.getQuery());
                childrenList.add(children);
                router.setChildren(childrenList);
            } else if (menu.getParentId().equals("0") && isInnerLink(menu)) {
                router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon()));
                router.setPath("/");
                List<RouterVo> childrenList = new ArrayList<>();
                RouterVo children = new RouterVo();
                String routerPath = innerLinkReplaceEach(menu.getPath());
                children.setPath(routerPath);
                children.setComponent(ComponentEnum.InnerLink.name());
                children.setName(StringUtils.capitalize(routerPath));
                children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), menu.getPath()));
                childrenList.add(children);
                router.setChildren(childrenList);
            }
            routers.add(router);
        }
        return routers;
    }

    /**
     * 构建前端所需要树结构
     *
     * @param menus 菜单列表
     * @return 树结构列表
     */
    @Override
    public List<TdsMenu> buildMenuTree(List<TdsMenu> menus) {
        List<TdsMenu> returnList = new ArrayList<>();
        List<String> tempList = menus.stream().map(TdsMenu::getId).collect(Collectors.toList());
        for (Iterator<TdsMenu> iterator = menus.iterator(); iterator.hasNext(); ) {
            TdsMenu menu = iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(menu.getParentId())) {
                recursionFn(menus, menu);
                returnList.add(menu);
            }
        }
        if (returnList.isEmpty()) {
            returnList = menus;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param menus 菜单列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelectVo> buildMenuTreeSelect(List<TdsMenu> menus) {
        List<TdsMenu> menuTrees = buildMenuTree(menus);
        return menuTrees.stream().map(TreeSelectVo::new).collect(Collectors.toList());
    }

    /**
     * 获取路由名称
     *
     * @param menu 菜单信息
     * @return 路由名称
     */
    public String getRouteName(TdsMenu menu) {
        String routerName = StringUtils.capitalize(menu.getPath());
        // 非外链并且是一级目录（类型为目录）
        if (isMenuFrame(menu)) {
            routerName = StringUtils.EMPTY;
        }
        return routerName;
    }

    /**
     * 获取路由地址
     *
     * @param menu 菜单信息
     * @return 路由地址
     */
    public String getRouterPath(TdsMenu menu) {
        String routerPath = menu.getPath();
        // 内链打开外网方式
        if (!menu.getParentId().equals("0") && isInnerLink(menu)) {
            routerPath = innerLinkReplaceEach(routerPath);
        }
        // 非外链并且是一级目录（类型为目录）
        if (menu.getParentId().equals("0") && MenuTypeEnum.CATALOG.getValue().equals(menu.getMenuType())
                && !menu.getWhetherFrame()) {
            routerPath = "/" + menu.getPath();
        }
        // 非外链并且是一级目录（类型为菜单）
        else if (isMenuFrame(menu)) {
            routerPath = "/";
        }
        return routerPath;
    }

    /**
     * 获取组件信息
     *
     * @param menu 菜单信息
     * @return 组件信息
     */
    public String getComponent(TdsMenu menu) {
        String component = ComponentEnum.Layout.name();
        if (StringUtils.isNotEmpty(menu.getComponent()) && !isMenuFrame(menu)) {
            component = menu.getComponent();
        } else if (StringUtils.isEmpty(menu.getComponent()) && !menu.getParentId().equals("0") && isInnerLink(menu)) {
            component = ComponentEnum.InnerLink.name();
        } else if (StringUtils.isEmpty(menu.getComponent()) && isParentView(menu)) {
            component = ComponentEnum.ParentView.name();
        }
        return component;
    }

    /**
     * 是否为菜单内部跳转
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isMenuFrame(TdsMenu menu) {
        return menu.getParentId().equals("0") && MenuTypeEnum.MENU.getValue().equals(menu.getMenuType())
                && menu.getWhetherFrame();
    }

    /**
     * 是否为内链组件
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isInnerLink(TdsMenu menu) {
        return menu.getWhetherFrame() && StringUtils.ishttp(menu.getPath());
    }

    /**
     * 是否为parent_view组件
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isParentView(TdsMenu menu) {
        return !menu.getParentId().equals("0") && MenuTypeEnum.MENU.getValue().equals(menu.getMenuType());
    }

    /**
     * 根据父节点的ID获取所有子节点
     *
     * @param list     分类表
     * @param parentId 传入的父节点ID
     * @return String
     */
    public List<TdsMenu> getChildPerms(List<TdsMenu> list, String parentId) {
        List<TdsMenu> returnList = new ArrayList<>();
        for (Iterator<TdsMenu> iterator = list.iterator(); iterator.hasNext(); ) {
            TdsMenu t = iterator.next();
            // 一、根据传入的某个父节点ID,遍历该父节点的所有子节点
            if (t.getParentId().equals(parentId)) {
                recursionFn(list, t);
                returnList.add(t);
            }
        }
        return returnList;
    }

    /**
     * 递归列表
     *
     * @param list 分类表
     * @param t    子节点
     */
    private void recursionFn(List<TdsMenu> list, TdsMenu t) {
        // 得到子节点列表
        List<TdsMenu> childList = getChildList(list, t);
        t.setChildren(childList);
        for (TdsMenu tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<TdsMenu> getChildList(List<TdsMenu> list, TdsMenu t) {
        List<TdsMenu> tlist = new ArrayList<TdsMenu>();
        Iterator<TdsMenu> it = list.iterator();
        while (it.hasNext()) {
            TdsMenu n = it.next();
            if (n.getParentId().equals(t.getId())) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<TdsMenu> list, TdsMenu t) {
        return getChildList(list, t).size() > 0;
    }

    /**
     * 内链域名特殊字符替换
     *
     * @return 替换后的内链域名
     */
    public String innerLinkReplaceEach(String path) {
        return StringUtils.replaceEach(path, new String[]{LinkPreEnum.HTTP.getValue(), LinkPreEnum.HTTPS.getValue(), LinkPreEnum.WWW.getValue(),
                        ".", ":"},
                new String[]{"", "", "", "/", "/"});
    }
}
