package com.bosi.sim.paas.tenant.server.controller;

import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.exception.CommonResult;
import com.bosi.sim.paas.common.core.utils.PageUtil;
import com.bosi.sim.paas.common.security.annotation.RequiresPermissions;
import com.bosi.sim.paas.dao.model.tds.TdsTenantAccountMovement;
import com.bosi.sim.paas.tenant.server.service.TmsUserAccountMovementService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@RestController
@Api(tags = "TmsUserAccountMovementController", value = "分销账户账动管理")
@RequestMapping("/tms/user/account/movement")
public class TmsUserAccountMovementController {

    @Autowired
    private TmsUserAccountMovementService userAccountMovementService;

    @RequiresPermissions("tms:user:account:movement:page")
    @GetMapping("/page")
    public CommonResult page(TdsTenantAccountMovement tmsUserAccountMovement,
                             @RequestParam(value = "pageSize") Integer pageSize,
                             @RequestParam(value = "pageNum") Integer pageNum) {
        CommonPage<TdsTenantAccountMovement> page = userAccountMovementService.page(PageUtil.buildPage(pageNum, pageSize), tmsUserAccountMovement);
        return CommonResult.success(page);
    }

}
