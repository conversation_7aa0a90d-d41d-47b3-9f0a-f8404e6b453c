{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/app/%28admin%29/system/users/page.tsx"], "sourcesContent": ["'use client';\n\nimport { roleService, userService } from '@/services';\nimport { Role, User, UserCreateInput, UserUpdateInput } from '@/types';\nimport { DeleteOutlined, EditOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons';\nimport { Avatar, Button, Form, Input, Modal, Popconfirm, Select, Space, Switch, Table, Tag, message } from 'antd';\nimport { ColumnsType } from 'antd/es/table';\nimport { useEffect, useState } from 'react';\n\nexport default function UsersPage() {\n  const [users, setUsers] = useState<User[]>([]);\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize, setPageSize] = useState(10);\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isEditing, setIsEditing] = useState(false);\n  const [currentUser, setCurrentUser] = useState<User | null>(null);\n  const [form] = Form.useForm();\n\n  // 获取用户列表\n  const fetchUsers = async () => {\n    try {\n      setLoading(true);\n      const response = await userService.getUsers({ page: currentPage, pageSize });\n      setUsers(response.data);\n      setTotal(response.total);\n    } catch (error) {\n      console.error('获取用户列表失败:', error);\n      message.error('获取用户列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取角色列表\n  const fetchRoles = async () => {\n    try {\n      const roles = await roleService.getAllRoles();\n      setRoles(roles);\n    } catch (error) {\n      console.error('获取角色列表失败:', error);\n      message.error('获取角色列表失败');\n    }\n  };\n\n  // 初始化\n  useEffect(() => {\n    fetchUsers();\n    fetchRoles();\n  }, [currentPage, pageSize]);\n\n  // 处理分页变化\n  const handleTableChange = (pagination: any) => {\n    setCurrentPage(pagination.current);\n    setPageSize(pagination.pageSize);\n  };\n\n  // 打开创建用户模态框\n  const showCreateModal = () => {\n    setIsEditing(false);\n    setCurrentUser(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  // 打开编辑用户模态框\n  const showEditModal = (user: User) => {\n    setIsEditing(true);\n    setCurrentUser(user);\n    form.setFieldsValue({\n      ...user,\n      status: user.status === 'active',\n    });\n    setIsModalVisible(true);\n  };\n\n  // 关闭模态框\n  const handleCancel = () => {\n    setIsModalVisible(false);\n    form.resetFields();\n  };\n\n  // 处理表单提交\n  const handleSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n\n      // 转换状态值\n      const userData = {\n        ...values,\n        status: values.status ? 'active' : 'inactive',\n      };\n\n      if (isEditing && currentUser) {\n        // 更新用户\n        await userService.updateUser({\n          id: currentUser.id,\n          ...userData,\n        } as UserUpdateInput);\n        message.success('用户更新成功');\n      } else {\n        // 创建用户\n        await userService.createUser(userData as UserCreateInput);\n        message.success('用户创建成功');\n      }\n\n      setIsModalVisible(false);\n      fetchUsers();\n    } catch (error) {\n      console.error('提交表单失败:', error);\n      message.error('操作失败，请重试');\n    }\n  };\n\n  // 处理删除用户\n  const handleDelete = async (id: string) => {\n    try {\n      await userService.deleteUser(id);\n      message.success('用户删除成功');\n      fetchUsers();\n    } catch (error) {\n      console.error('删除用户失败:', error);\n      message.error('删除用户失败');\n    }\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<User> = [\n    {\n      title: '用户名',\n      dataIndex: 'username',\n      key: 'username',\n      render: (text, record) => (\n        <Space>\n          <Avatar src={record.avatar} size=\"small\" />\n          {text}\n        </Space>\n      ),\n    },\n    {\n      title: '姓名',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '邮箱',\n      dataIndex: 'email',\n      key: 'email',\n    },\n    {\n      title: '角色',\n      dataIndex: 'roleIds',\n      key: 'roleIds',\n      render: (roleIds: string[]) => (\n        <>\n          {roleIds.map(roleId => {\n            const role = roles.find(r => r.id === roleId);\n            return role ? <Tag key={roleId}>{role.name}</Tag> : null;\n          })}\n        </>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status: string) => (\n        <Tag color={status === 'active' ? 'green' : 'red'}>\n          {status === 'active' ? '启用' : '禁用'}\n        </Tag>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space size=\"middle\">\n          <Button\n            type=\"text\"\n            icon={<EditOutlined />}\n            onClick={() => showEditModal(record)}\n          />\n          <Popconfirm\n            title=\"确定要删除此用户吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button\n              type=\"text\"\n              danger\n              icon={<DeleteOutlined />}\n            />\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <>\n      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>\n        <h1>用户管理</h1>\n        <Button type=\"primary\" icon={<PlusOutlined />} onClick={showCreateModal}>\n          新增用户\n        </Button>\n      </div>\n\n      <div style={{ marginBottom: 16 }}>\n        <Input.Search\n          placeholder=\"搜索用户\"\n          allowClear\n          enterButton={<SearchOutlined />}\n          onSearch={(value) => console.log(value)}\n          style={{ width: 300 }}\n        />\n      </div>\n\n      <Table\n        columns={columns}\n        dataSource={users}\n        rowKey=\"id\"\n        loading={loading}\n        pagination={{\n          current: currentPage,\n          pageSize: pageSize,\n          total: total,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total) => `共 ${total} 条记录`,\n        }}\n        onChange={handleTableChange}\n      />\n\n      <Modal\n        title={isEditing ? '编辑用户' : '新增用户'}\n        open={isModalVisible}\n        onOk={handleSubmit}\n        onCancel={handleCancel}\n        maskClosable={false}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n        >\n          {!isEditing && (\n            <Form.Item\n              name=\"username\"\n              label=\"用户名\"\n              rules={[{ required: true, message: '请输入用户名' }]}\n            >\n              <Input placeholder=\"请输入用户名\" />\n            </Form.Item>\n          )}\n\n          <Form.Item\n            name=\"name\"\n            label=\"姓名\"\n            rules={[{ required: true, message: '请输入姓名' }]}\n          >\n            <Input placeholder=\"请输入姓名\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"email\"\n            label=\"邮箱\"\n            rules={[\n              { required: true, message: '请输入邮箱' },\n              { type: 'email', message: '请输入有效的邮箱地址' },\n            ]}\n          >\n            <Input placeholder=\"请输入邮箱\" />\n          </Form.Item>\n\n          {!isEditing && (\n            <Form.Item\n              name=\"password\"\n              label=\"密码\"\n              rules={[{ required: true, message: '请输入密码' }]}\n            >\n              <Input.Password placeholder=\"请输入密码\" />\n            </Form.Item>\n          )}\n\n          <Form.Item\n            name=\"roleIds\"\n            label=\"角色\"\n            rules={[{ required: true, message: '请选择角色' }]}\n          >\n            <Select\n              mode=\"multiple\"\n              placeholder=\"请选择角色\"\n              options={roles.map(role => ({ label: role.name, value: role.id }))}\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"status\"\n            label=\"状态\"\n            valuePropName=\"checked\"\n            initialValue={true}\n          >\n            <Switch checkedChildren=\"启用\" unCheckedChildren=\"禁用\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </>\n  );\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;;;AAPA;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,SAAS;IACT,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,qIAAA,CAAA,cAAW,CAAC,QAAQ,CAAC;gBAAE,MAAM;gBAAa;YAAS;YAC1E,SAAS,SAAS,IAAI;YACtB,SAAS,SAAS,KAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,SAAS;IACT,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,QAAQ,MAAM,qIAAA,CAAA,cAAW,CAAC,WAAW;YAC3C,SAAS;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,MAAM;IACN,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;YACA;QACF;8BAAG;QAAC;QAAa;KAAS;IAE1B,SAAS;IACT,MAAM,oBAAoB,CAAC;QACzB,eAAe,WAAW,OAAO;QACjC,YAAY,WAAW,QAAQ;IACjC;IAEA,YAAY;IACZ,MAAM,kBAAkB;QACtB,aAAa;QACb,eAAe;QACf,KAAK,WAAW;QAChB,kBAAkB;IACpB;IAEA,YAAY;IACZ,MAAM,gBAAgB,CAAC;QACrB,aAAa;QACb,eAAe;QACf,KAAK,cAAc,CAAC;YAClB,GAAG,IAAI;YACP,QAAQ,KAAK,MAAM,KAAK;QAC1B;QACA,kBAAkB;IACpB;IAEA,QAAQ;IACR,MAAM,eAAe;QACnB,kBAAkB;QAClB,KAAK,WAAW;IAClB;IAEA,SAAS;IACT,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,SAAS,MAAM,KAAK,cAAc;YAExC,QAAQ;YACR,MAAM,WAAW;gBACf,GAAG,MAAM;gBACT,QAAQ,OAAO,MAAM,GAAG,WAAW;YACrC;YAEA,IAAI,aAAa,aAAa;gBAC5B,OAAO;gBACP,MAAM,qIAAA,CAAA,cAAW,CAAC,UAAU,CAAC;oBAC3B,IAAI,YAAY,EAAE;oBAClB,GAAG,QAAQ;gBACb;gBACA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB,OAAO;gBACL,OAAO;gBACP,MAAM,qIAAA,CAAA,cAAW,CAAC,UAAU,CAAC;gBAC7B,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB;YAEA,kBAAkB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,SAAS;IACT,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,qIAAA,CAAA,cAAW,CAAC,UAAU,CAAC;YAC7B,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,QAAQ;IACR,MAAM,UAA6B;QACjC;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,MAAM,uBACb,6LAAC,mMAAA,CAAA,QAAK;;sCACJ,6LAAC,qLAAA,CAAA,SAAM;4BAAC,KAAK,OAAO,MAAM;4BAAE,MAAK;;;;;;wBAChC;;;;;;;QAGP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,wBACP;8BACG,QAAQ,GAAG,CAAC,CAAA;wBACX,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;wBACtC,OAAO,qBAAO,6LAAC,+KAAA,CAAA,MAAG;sCAAe,KAAK,IAAI;2BAAlB;;;;mCAA4B;oBACtD;;QAGN;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,uBACP,6LAAC,+KAAA,CAAA,MAAG;oBAAC,OAAO,WAAW,WAAW,UAAU;8BACzC,WAAW,WAAW,OAAO;;;;;;QAGpC;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,6LAAC,mMAAA,CAAA,QAAK;oBAAC,MAAK;;sCACV,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4BACnB,SAAS,IAAM,cAAc;;;;;;sCAE/B,6LAAC,6LAAA,CAAA,aAAU;4BACT,OAAM;4BACN,WAAW,IAAM,aAAa,OAAO,EAAE;4BACvC,QAAO;4BACP,YAAW;sCAEX,cAAA,6LAAC,qMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,MAAM;gCACN,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;QAK/B;KACD;IAED,qBACE;;0BACE,6LAAC;gBAAI,OAAO;oBAAE,SAAS;oBAAQ,gBAAgB;oBAAiB,cAAc;gBAAG;;kCAC/E,6LAAC;kCAAG;;;;;;kCACJ,6LAAC,qMAAA,CAAA,SAAM;wBAAC,MAAK;wBAAU,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wBAAK,SAAS;kCAAiB;;;;;;;;;;;;0BAK3E,6LAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAG;0BAC7B,cAAA,6LAAC,mLAAA,CAAA,QAAK,CAAC,MAAM;oBACX,aAAY;oBACZ,UAAU;oBACV,2BAAa,6LAAC,yNAAA,CAAA,iBAAc;;;;;oBAC5B,UAAU,CAAC,QAAU,QAAQ,GAAG,CAAC;oBACjC,OAAO;wBAAE,OAAO;oBAAI;;;;;;;;;;;0BAIxB,6LAAC,mLAAA,CAAA,QAAK;gBACJ,SAAS;gBACT,YAAY;gBACZ,QAAO;gBACP,SAAS;gBACT,YAAY;oBACV,SAAS;oBACT,UAAU;oBACV,OAAO;oBACP,iBAAiB;oBACjB,iBAAiB;oBACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;gBACxC;gBACA,UAAU;;;;;;0BAGZ,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAO,YAAY,SAAS;gBAC5B,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,cAAc;0BAEd,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBACH,MAAM;oBACN,QAAO;;wBAEN,CAAC,2BACA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAS;6BAAE;sCAE9C,cAAA,6LAAC,mLAAA,CAAA,QAAK;gCAAC,aAAY;;;;;;;;;;;sCAIvB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAQ;6BAAE;sCAE7C,cAAA,6LAAC,mLAAA,CAAA,QAAK;gCAAC,aAAY;;;;;;;;;;;sCAGrB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCACL;oCAAE,UAAU;oCAAM,SAAS;gCAAQ;gCACnC;oCAAE,MAAM;oCAAS,SAAS;gCAAa;6BACxC;sCAED,cAAA,6LAAC,mLAAA,CAAA,QAAK;gCAAC,aAAY;;;;;;;;;;;wBAGpB,CAAC,2BACA,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAQ;6BAAE;sCAE7C,cAAA,6LAAC,mLAAA,CAAA,QAAK,CAAC,QAAQ;gCAAC,aAAY;;;;;;;;;;;sCAIhC,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAQ;6BAAE;sCAE7C,cAAA,6LAAC,qLAAA,CAAA,SAAM;gCACL,MAAK;gCACL,aAAY;gCACZ,SAAS,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;wCAAE,OAAO,KAAK,IAAI;wCAAE,OAAO,KAAK,EAAE;oCAAC,CAAC;;;;;;;;;;;sCAIpE,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,eAAc;4BACd,cAAc;sCAEd,cAAA,6LAAC,qLAAA,CAAA,SAAM;gCAAC,iBAAgB;gCAAK,mBAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAKxD;GA5SqB;;QAUP,iLAAA,CAAA,OAAI,CAAC;;;KAVE", "debugId": null}}]}