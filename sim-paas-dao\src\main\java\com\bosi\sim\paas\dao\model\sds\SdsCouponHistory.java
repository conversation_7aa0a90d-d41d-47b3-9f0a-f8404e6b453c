package com.bosi.sim.paas.dao.model.sds;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("sms_coupon_history")
public class SdsCouponHistory extends BaseEntity {
    private String couponId;

    private String memberId;

    private String couponCode;

    @ApiModelProperty(value = "领取人昵�?)
    private String memberNickname;

    @ApiModelProperty(value = "获取类型�?->后台赠送；1->主动获取")
    private Integer getType;

    @ApiModelProperty(value = "使用状态：0->未使用；1->已使用；2->已过�?)
    private Integer useStatus;

    @ApiModelProperty(value = "使用时间")
    private Date useTime;

    @ApiModelProperty(value = "订单编号")
    private String orderId;

    @ApiModelProperty(value = "订单号码")
    private String orderSn;

    @ApiModelProperty("相关优惠券信�?)
    @TableField(exist = false)
    private SmsCoupon coupon;

}
