package com.bosi.sim.paas.dao.model.sds;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("sds_coupon_history")
public class SdsCouponHistory extends BaseEntity {
    private String couponId;

    private String memberId;

    private String couponCode;

    private String memberNickname;

    private Integer getType;

    private Integer useStatus;

    @ApiModelProperty(value = "使用时间")
    private Date useTime;

    @ApiModelProperty(value = "订单编号")
    private String orderId;

    @ApiModelProperty(value = "订单号码")
    private String orderSn;

    @TableField(exist = false)
    private SmsCoupon coupon;

}
