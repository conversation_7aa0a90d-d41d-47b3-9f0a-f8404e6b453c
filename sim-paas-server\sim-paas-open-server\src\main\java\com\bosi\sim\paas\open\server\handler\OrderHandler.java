package com.bosi.sim.paas.open.server.handler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bosi.sim.paas.common.core.api.BizCode;
import com.bosi.sim.paas.common.core.api.SystemCode;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.dao.enums.pms.PmsPromotionTypeEnum;
import com.bosi.sim.paas.dao.mapper.mms.MmsMemberReceiveAddressMapper;
import com.bosi.sim.paas.dao.mapper.oms.OmsCartItemMapper;
import com.bosi.sim.paas.dao.mapper.pms.PmsProductFullReductionMapper;
import com.bosi.sim.paas.dao.mapper.pms.PmsProductLadderMapper;
import com.bosi.sim.paas.dao.mapper.pms.PmsProductSkuMapper;
import com.bosi.sim.paas.dao.model.sds.SdsMemberReceiveAddress;
import com.bosi.sim.paas.dao.model.sds.OmsCartItem;
import com.bosi.sim.paas.dao.model.sds.PmsProduct;
import com.bosi.sim.paas.dao.model.sds.PmsProductFullReduction;
import com.bosi.sim.paas.dao.model.sds.PmsProductLadder;
import com.bosi.sim.paas.dao.model.sds.PmsProductSku;
import com.bosi.sim.paas.dao.model.sds.SmsCouponHistory;
import com.bosi.sim.paas.open.server.dao.PmsProductDao;
import com.bosi.sim.paas.open.server.dao.SmsCouponHistoryDao;
import com.bosi.sim.paas.open.server.domain.dto.CartPromotionItemDto;
import com.bosi.sim.paas.open.server.domain.param.CartItemCreateParam;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 促销管理Service实现类
 */
@Service
public class OrderHandler {

    @Autowired
    private MmsMemberReceiveAddressMapper memberReceiveAddressMapper;

    @Autowired
    private SmsCouponHistoryDao couponHistoryDao;

    @Autowired
    private PmsProductLadderMapper pmsProductLadderMapper;

    @Autowired
    private PmsProductFullReductionMapper pmsProductFullReductionMapper;

    @Autowired
    private PmsProductSkuMapper pmsProductSkuMapper;

    @Autowired
    private PmsProductDao pmsProductDao;

    @Autowired
    private OmsCartItemMapper omsCartItemMapper;


    public List<CartPromotionItemDto> calcCartPromotion(String memberId, List<CartItemCreateParam> cartItemList) {
        //1.查询所有商品的优惠相关信息
        List<String> productIdList = Lists.transform(cartItemList, CartItemCreateParam::getProductId);
        List<String> productSkuIdList = Lists.transform(cartItemList, CartItemCreateParam::getProductSkuId);
        LambdaQueryWrapper<PmsProductLadder> ladderWrapper = new LambdaQueryWrapper<>();
        ladderWrapper.in(PmsProductLadder::getProductId, productIdList);
        List<PmsProductLadder> ladderList = pmsProductLadderMapper.selectList(ladderWrapper);
        Map<String, List<PmsProductLadder>> ladderListMap = Maps.newHashMap();
        if (CollUtil.isNotEmpty(ladderList)) {
            ladderList.forEach(pmsProductLadder -> {
                List<PmsProductLadder> productLadderList = ladderListMap.get(pmsProductLadder.getProductId());
                if (CollUtil.isEmpty(productLadderList)) {
                    productLadderList = Lists.newArrayList();
                }
                productLadderList.add(pmsProductLadder);
                ladderListMap.put(pmsProductLadder.getProductId(), productLadderList);
            });
        }

        LambdaQueryWrapper<PmsProductFullReduction> fullWrapper = new LambdaQueryWrapper<>();
        ladderWrapper.in(PmsProductLadder::getProductId, productIdList);
        List<PmsProductFullReduction> fullReductionList = pmsProductFullReductionMapper.selectList(fullWrapper);

        Map<String, List<PmsProductFullReduction>> fullListMap = Maps.newHashMap();
        if (CollUtil.isNotEmpty(fullReductionList)) {
            fullReductionList.forEach(fullReduction -> {
                List<PmsProductFullReduction> reductionList = fullListMap.get(fullReduction.getProductId());
                if (CollUtil.isEmpty(reductionList)) {
                    reductionList = Lists.newArrayList();
                }
                reductionList.add(fullReduction);
                fullListMap.put(fullReduction.getProductId(), reductionList);
            });
        }

        //2.先根据productSkuId对CartItem进行分组，以spu为单位进行计算优惠
        LambdaQueryWrapper<PmsProductSku> skuWrapper = new LambdaQueryWrapper<>();
        skuWrapper.in(PmsProductSku::getId, productSkuIdList);
        List<PmsProductSku> skuList = pmsProductSkuMapper.selectList(skuWrapper);
        Map<String, PmsProductSku> skuMap = Maps.uniqueIndex(skuList, PmsProductSku::getId);
        List<PmsProduct> productList = pmsProductDao.listByIds(productIdList);
        Map<String, PmsProduct> productMap = Maps.uniqueIndex(productList, PmsProduct::getId);
        //3.根据商品促销类型计算商品促销优惠价格

        Map<String, List<CartItemCreateParam>> productCartMap = Maps.newHashMap();
        cartItemList.forEach(cartItem -> {
            List<CartItemCreateParam> omsCartItems = productCartMap.get(cartItem.getProductId());
            if (CollUtil.isEmpty(omsCartItems)) {
                omsCartItems = Lists.newArrayList();
            }
            omsCartItems.add(cartItem);
            productCartMap.put(cartItem.getProductId(), omsCartItems);
        });

        List<CartPromotionItemDto> cartPromotionItemList = new ArrayList<>();
        for (Map.Entry<String, List<CartItemCreateParam>> entry : productCartMap.entrySet()) {
            String productId = entry.getKey();
            List<CartItemCreateParam> itemList = entry.getValue();
            PmsProduct pmsProduct = productMap.get(productId);
            if (pmsProduct == null || !pmsProduct.getWhetherPublish()) {
                throw BizException.build(BizCode.ORDER_EXIST_ALREADY_UNPUBLISH_PRODUCT);
            }
            Integer promotionType = pmsProduct.getPromotionType();
            //打折优惠
            int count = getCartItemCount(itemList);
            PmsProductLadder ladder = getProductLadder(count, ladderListMap.get(productId));

            BigDecimal totalAmount = getCartItemAmount(itemList, skuMap);
            PmsProductFullReduction fullReduction = getProductFullReduction(totalAmount, fullListMap.get(productId));
            for (CartItemCreateParam item : itemList) {
                PmsProductSku skuStock = skuMap.get(item.getProductSkuId());
                if (skuStock == null) {
                    throw BizException.build(BizCode.ORDER_EXIST_ALREADY_UNPUBLISH_PRODUCT);
                }
                if (pmsProduct.getWhetherVirtually()) {
                    if (CollUtil.isEmpty(item.getCustomerDeviceUidList())) {
                        throw BizException.build(BizCode.ORDER_IMPROVE_DEVICE_UID);
                    }
                    if (item.getCustomerDeviceUidList().size() != item.getQuantity()) {
                        throw BizException.build(BizCode.ORDER_DEVICE_UID_NUMBER_MUST_EQUAL_QUANTITY);
                    }
                }
                String promsg = "无优惠";
                BigDecimal perReduceAmount = new BigDecimal("0");
                if (promotionType.equals(PmsPromotionTypeEnum.DISCOUNT.getPromotionType())) {
                    if (ladder != null) {
                        promsg = getLadderPromotionMessage(ladder);
                        perReduceAmount = skuStock.getPrice().subtract(ladder.getDiscount().multiply(skuStock.getPrice()));
                    }
                } else if (promotionType.equals(PmsPromotionTypeEnum.FULL_REDUCTION.getPromotionType())) {
                    if (fullReduction != null) {
                        promsg = getFullReductionPromotionMessage(fullReduction);
                        perReduceAmount = skuStock.getPrice().divide(totalAmount, RoundingMode.HALF_EVEN).multiply(fullReduction.getReducePrice());
                    }
                }
                cartPromotionItemList.add(buildCartPromotionItem(memberId, item, skuStock, promsg, perReduceAmount, pmsProduct));
            }
        }
        return cartPromotionItemList;
    }

    public List<SdsMemberReceiveAddress> listMemberReceiveAddressByMemberId(String memberId) {
        LambdaQueryWrapper<SdsMemberReceiveAddress> example = new LambdaQueryWrapper<>();
        example.eq(SdsMemberReceiveAddress::getMemberId, memberId);
        return memberReceiveAddressMapper.selectList(example);
    }

    public List<CartItemCreateParam> getCartItemCreateParamListByCartIds(String memberId, List<String> cartIds) {
        LambdaQueryWrapper<OmsCartItem> itemWrapper = new LambdaQueryWrapper<>();
        itemWrapper.in(OmsCartItem::getId, cartIds);
        itemWrapper.eq(OmsCartItem::getMemberId, memberId);
        List<OmsCartItem> cartItemList = omsCartItemMapper.selectList(itemWrapper);
        if (CollUtil.isEmpty(cartItemList)) {
            throw BizException.build(SystemCode.RESOURCE_NOTFOUND);
        }
        List<CartItemCreateParam> createParamList = Lists.newArrayList();
        cartItemList.forEach(item -> {
            CartItemCreateParam cartItemCreateParam = new CartItemCreateParam();
            cartItemCreateParam.setProductId(item.getProductId());
            cartItemCreateParam.setProductSkuId(item.getProductSkuId());
            cartItemCreateParam.setQuantity(item.getQuantity());
            cartItemCreateParam.setReferralCode(item.getReferralCode());
            if (StringUtils.isNotEmpty(item.getDeviceUidJson())) {
                cartItemCreateParam.setCustomerDeviceUidList(JSON.parseArray(item.getDeviceUidJson(), String.class));
            }
            createParamList.add(cartItemCreateParam);
        });
        return createParamList;
    }

    public List<SmsCouponHistory> listCartCoupon(List<CartPromotionItemDto> cartItemList, Integer type, String memberId) {
        Date now = new Date();
        //获取该用户所有优惠券
        List<SmsCouponHistory> allList = couponHistoryDao.getDetailList(memberId);
        //根据优惠券使用类型来判断优惠券是否可用
        List<SmsCouponHistory> enableList = new ArrayList<>();
        List<SmsCouponHistory> disableList = new ArrayList<>();
        for (SmsCouponHistory couponHistoryDetail : allList) {
            BigDecimal minPoint = couponHistoryDetail.getCoupon().getMinPoint();
            Date endTime = couponHistoryDetail.getCoupon().getEndTime();
            //0->全场通用
            //判断是否满足优惠起点
            //计算购物车商品的总价
            BigDecimal totalAmount = calcTotalAmount(cartItemList);
            if (now.before(endTime) && totalAmount.subtract(minPoint).intValue() >= 0) {
                enableList.add(couponHistoryDetail);
            } else {
                disableList.add(couponHistoryDetail);
            }
        }
        if (type.equals(1)) {
            return enableList;
        } else {
            return disableList;
        }
    }

    /**
     * 获取满减促销消息
     */
    private String getFullReductionPromotionMessage(PmsProductFullReduction fullReduction) {
        return "满减优惠：满" +
                fullReduction.getFullPrice() +
                "元，减" +
                fullReduction.getReducePrice() +
                "元";
    }

    /**
     * 对没满足优惠条件的商品进行处理
     */
    private CartPromotionItemDto buildCartPromotionItem(String memberId, CartItemCreateParam cartItemCreateParam, PmsProductSku skuStock,
                                                        String promotionMessage, BigDecimal perReduceAmount, PmsProduct product) {
        CartPromotionItemDto cartPromotionItem = new CartPromotionItemDto();
        cartPromotionItem.setPromotionMessage(promotionMessage);
        cartPromotionItem.setWhetherVirtually(product.getWhetherVirtually());
        cartPromotionItem.setPerReduceAmount(perReduceAmount);
        cartPromotionItem.setPerPrice(skuStock.getPrice());
        cartPromotionItem.setPerGiftPoint(product.getGiftPoint());
        cartPromotionItem.setPerGiftGrowth(product.getGiftGrowth());
        cartPromotionItem.setRealStock(skuStock.getStock() - skuStock.getLockStock());
        cartPromotionItem.setProductId(skuStock.getProductId());
        cartPromotionItem.setProductSkuId(skuStock.getId());
        cartPromotionItem.setMemberId(memberId);
        cartPromotionItem.setReferralCode(cartItemCreateParam.getReferralCode());
        cartPromotionItem.setQuantity(cartItemCreateParam.getQuantity());
        cartPromotionItem.setPic(product.getPic());
        cartPromotionItem.setProductSkuName(skuStock.getSkuName());
        cartPromotionItem.setProductName(product.getProductName());
        cartPromotionItem.setProductSubTitle(product.getSubTitle());
        cartPromotionItem.setProductSkuCode(skuStock.getSkuCode());
        cartPromotionItem.setProductSkuPic(skuStock.getPic());
        cartPromotionItem.setProductCategoryName(product.getCategoryName());
        cartPromotionItem.setProductBrandName(product.getBrandName());
        cartPromotionItem.setProductSn(product.getProductSn());
        cartPromotionItem.setProductAttr(skuStock.getSpData());
        cartPromotionItem.setCustomerDeviceUidList(cartItemCreateParam.getCustomerDeviceUidList());
        return cartPromotionItem;
    }

    private PmsProductFullReduction getProductFullReduction(BigDecimal totalAmount, List<PmsProductFullReduction> fullReductionList) {
        //按条件从高到低排序
        if (CollUtil.isEmpty(fullReductionList)) {
            return null;
        }
        fullReductionList.sort((o1, o2) -> o2.getFullPrice().subtract(o1.getFullPrice()).intValue());
        for (PmsProductFullReduction fullReduction : fullReductionList) {
            if (totalAmount.subtract(fullReduction.getFullPrice()).intValue() >= 0) {
                return fullReduction;
            }
        }
        return null;
    }

    /**
     * 获取打折优惠的促销信息
     */
    private String getLadderPromotionMessage(PmsProductLadder ladder) {
        return "打折优惠：满" +
                ladder.getCount() +
                "件，打" +
                ladder.getDiscount().multiply(new BigDecimal(10)) +
                "折";
    }

    /**
     * 根据购买商品数量获取满足条件的打折优惠策略
     */
    private PmsProductLadder getProductLadder(int count, List<PmsProductLadder> productLadderList) {
        //按数量从大到小排序
        if (CollUtil.isEmpty(productLadderList)) {
            return null;
        }
        productLadderList.sort((o1, o2) -> o2.getCount() - o1.getCount());
        for (PmsProductLadder productLadder : productLadderList) {
            if (count >= productLadder.getCount()) {
                return productLadder;
            }
        }
        return null;
    }

    /**
     * 获取购物车中指定商品的数量
     */
    private int getCartItemCount(List<CartItemCreateParam> itemList) {
        int count = 0;
        for (CartItemCreateParam item : itemList) {
            count += item.getQuantity();
        }
        return count;
    }

    /**
     * 获取购物车中指定商品的总价
     */
    private BigDecimal getCartItemAmount(List<CartItemCreateParam> itemList, Map<String, PmsProductSku> skuMap) {
        BigDecimal amount = new BigDecimal(0);
        for (CartItemCreateParam item : itemList) {
            //计算出商品原价
            amount = amount.add(skuMap.get(item.getProductSkuId()).getPrice().multiply(new BigDecimal(item.getQuantity())));
        }
        return amount;
    }

    private BigDecimal calcTotalAmount(List<CartPromotionItemDto> cartItemList) {
        BigDecimal total = new BigDecimal("0");
        for (CartPromotionItemDto item : cartItemList) {
            BigDecimal realPrice = item.getPerPrice().subtract(item.getPerReduceAmount());
            total = total.add(realPrice.multiply(new BigDecimal(item.getQuantity())));
        }
        return total;
    }

}
