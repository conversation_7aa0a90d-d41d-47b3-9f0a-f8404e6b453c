package com.bosi.sim.paas.tenant.server.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.dao.model.tds.TdsNotifyRecord;

/**
 * 通知 业务层
 */
public interface TmsNotifyRecordService {

    CommonPage<TdsNotifyRecord> page(Page<TdsNotifyRecord> page, TdsNotifyRecord notifyRecord);

    int read(String id, String distributorId);

}
