{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/app/%28admin%29/system/roles/page.tsx"], "sourcesContent": ["'use client';\n\nimport { menuService, roleService } from '@/services';\nimport { Menu, Role, RoleCreateInput, RoleUpdateInput } from '@/types';\nimport { DeleteOutlined, EditOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons';\nimport { Button, Form, Input, Modal, Popconfirm, Space, Table, Tag, Tree, message } from 'antd';\nimport { ColumnsType } from 'antd/es/table';\nimport { DataNode } from 'antd/es/tree';\nimport { useEffect, useState } from 'react';\n\n// 将菜单数据转换为 Tree 组件所需的格式\nconst convertMenusToTreeData = (menus: Menu[]): DataNode[] => {\n  return menus.map(menu => ({\n    title: menu.name,\n    key: menu.id,\n    children: menu.children ? convertMenusToTreeData(menu.children) : undefined,\n  }));\n};\n\nexport default function RolesPage() {\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [menuTree, setMenuTree] = useState<Menu[]>([]);\n  const [treeData, setTreeData] = useState<DataNode[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize, setPageSize] = useState(10);\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isEditing, setIsEditing] = useState(false);\n  const [currentRole, setCurrentRole] = useState<Role | null>(null);\n  const [form] = Form.useForm();\n\n  // 获取角色列表\n  const fetchRoles = async () => {\n    try {\n      setLoading(true);\n      const response = await roleService.getRoles({ page: currentPage, pageSize });\n      setRoles(response.data);\n      setTotal(response.total);\n    } catch (error) {\n      console.error('获取角色列表失败:', error);\n      message.error('获取角色列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取菜单树\n  const fetchMenuTree = async () => {\n    try {\n      const menuTree = await menuService.getMenuTree();\n      setMenuTree(menuTree);\n      setTreeData(convertMenusToTreeData(menuTree));\n    } catch (error) {\n      console.error('获取菜单树失败:', error);\n      message.error('获取菜单树失败');\n    }\n  };\n\n  // 初始化\n  useEffect(() => {\n    fetchRoles();\n    fetchMenuTree();\n  }, [currentPage, pageSize]);\n\n  // 处理分页变化\n  const handleTableChange = (pagination: any) => {\n    setCurrentPage(pagination.current);\n    setPageSize(pagination.pageSize);\n  };\n\n  // 打开创建角色模态框\n  const showCreateModal = () => {\n    setIsEditing(false);\n    setCurrentRole(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  // 打开编辑角色模态框\n  const showEditModal = (role: Role) => {\n    setIsEditing(true);\n    setCurrentRole(role);\n    form.setFieldsValue({\n      ...role,\n    });\n    setIsModalVisible(true);\n  };\n\n  // 关闭模态框\n  const handleCancel = () => {\n    setIsModalVisible(false);\n    form.resetFields();\n  };\n\n  // 处理表单提交\n  const handleSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n      \n      if (isEditing && currentRole) {\n        // 更新角色\n        await roleService.updateRole({\n          id: currentRole.id,\n          ...values,\n        } as RoleUpdateInput);\n        message.success('角色更新成功');\n      } else {\n        // 创建角色\n        await roleService.createRole(values as RoleCreateInput);\n        message.success('角色创建成功');\n      }\n      \n      setIsModalVisible(false);\n      fetchRoles();\n    } catch (error) {\n      console.error('提交表单失败:', error);\n      message.error('操作失败，请重试');\n    }\n  };\n\n  // 处理删除角色\n  const handleDelete = async (id: string) => {\n    try {\n      await roleService.deleteRole(id);\n      message.success('角色删除成功');\n      fetchRoles();\n    } catch (error) {\n      console.error('删除角色失败:', error);\n      message.error('删除角色失败');\n    }\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<Role> = [\n    {\n      title: '角色名称',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '描述',\n      dataIndex: 'description',\n      key: 'description',\n    },\n    {\n      title: '菜单权限',\n      dataIndex: 'menuIds',\n      key: 'menuIds',\n      render: (menuIds: string[]) => (\n        <>\n          {menuIds.slice(0, 3).map(menuId => {\n            // 递归查找菜单名称\n            const findMenuName = (menus: Menu[], id: string): string | undefined => {\n              for (const menu of menus) {\n                if (menu.id === id) {\n                  return menu.name;\n                }\n                if (menu.children) {\n                  const name = findMenuName(menu.children, id);\n                  if (name) return name;\n                }\n              }\n              return undefined;\n            };\n            \n            const menuName = findMenuName(menuTree, menuId);\n            return menuName ? <Tag key={menuId}>{menuName}</Tag> : null;\n          })}\n          {menuIds.length > 3 && <Tag>+{menuIds.length - 3}</Tag>}\n        </>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space size=\"middle\">\n          <Button \n            type=\"text\" \n            icon={<EditOutlined />} \n            onClick={() => showEditModal(record)}\n          />\n          <Popconfirm\n            title=\"确定要删除此角色吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button \n              type=\"text\" \n              danger \n              icon={<DeleteOutlined />} \n            />\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <>\n      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>\n        <h1>角色管理</h1>\n        <Button type=\"primary\" icon={<PlusOutlined />} onClick={showCreateModal}>\n          新增角色\n        </Button>\n      </div>\n      \n      <div style={{ marginBottom: 16 }}>\n        <Input.Search\n          placeholder=\"搜索角色\"\n          allowClear\n          enterButton={<SearchOutlined />}\n          onSearch={(value) => console.log(value)}\n          style={{ width: 300 }}\n        />\n      </div>\n      \n      <Table\n        columns={columns}\n        dataSource={roles}\n        rowKey=\"id\"\n        loading={loading}\n        pagination={{\n          current: currentPage,\n          pageSize: pageSize,\n          total: total,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total) => `共 ${total} 条记录`,\n        }}\n        onChange={handleTableChange}\n      />\n      \n      <Modal\n        title={isEditing ? '编辑角色' : '新增角色'}\n        open={isModalVisible}\n        onOk={handleSubmit}\n        onCancel={handleCancel}\n        maskClosable={false}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"角色名称\"\n            rules={[{ required: true, message: '请输入角色名称' }]}\n          >\n            <Input placeholder=\"请输入角色名称\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"description\"\n            label=\"描述\"\n            rules={[{ required: true, message: '请输入描述' }]}\n          >\n            <Input.TextArea placeholder=\"请输入描述\" rows={3} />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"menuIds\"\n            label=\"菜单权限\"\n            rules={[{ required: true, message: '请选择菜单权限' }]}\n          >\n            <Tree\n              checkable\n              treeData={treeData}\n              defaultExpandAll\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;;;AARA;;;;;AAUA,wBAAwB;AACxB,MAAM,yBAAyB,CAAC;IAC9B,OAAO,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;YACxB,OAAO,KAAK,IAAI;YAChB,KAAK,KAAK,EAAE;YACZ,UAAU,KAAK,QAAQ,GAAG,uBAAuB,KAAK,QAAQ,IAAI;QACpE,CAAC;AACH;AAEe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,SAAS;IACT,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,qIAAA,CAAA,cAAW,CAAC,QAAQ,CAAC;gBAAE,MAAM;gBAAa;YAAS;YAC1E,SAAS,SAAS,IAAI;YACtB,SAAS,SAAS,KAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,QAAQ;IACR,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,qIAAA,CAAA,cAAW,CAAC,WAAW;YAC9C,YAAY;YACZ,YAAY,uBAAuB;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,YAAY;YAC1B,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,MAAM;IACN,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;YACA;QACF;8BAAG;QAAC;QAAa;KAAS;IAE1B,SAAS;IACT,MAAM,oBAAoB,CAAC;QACzB,eAAe,WAAW,OAAO;QACjC,YAAY,WAAW,QAAQ;IACjC;IAEA,YAAY;IACZ,MAAM,kBAAkB;QACtB,aAAa;QACb,eAAe;QACf,KAAK,WAAW;QAChB,kBAAkB;IACpB;IAEA,YAAY;IACZ,MAAM,gBAAgB,CAAC;QACrB,aAAa;QACb,eAAe;QACf,KAAK,cAAc,CAAC;YAClB,GAAG,IAAI;QACT;QACA,kBAAkB;IACpB;IAEA,QAAQ;IACR,MAAM,eAAe;QACnB,kBAAkB;QAClB,KAAK,WAAW;IAClB;IAEA,SAAS;IACT,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,SAAS,MAAM,KAAK,cAAc;YAExC,IAAI,aAAa,aAAa;gBAC5B,OAAO;gBACP,MAAM,qIAAA,CAAA,cAAW,CAAC,UAAU,CAAC;oBAC3B,IAAI,YAAY,EAAE;oBAClB,GAAG,MAAM;gBACX;gBACA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB,OAAO;gBACL,OAAO;gBACP,MAAM,qIAAA,CAAA,cAAW,CAAC,UAAU,CAAC;gBAC7B,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB;YAEA,kBAAkB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,SAAS;IACT,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,qIAAA,CAAA,cAAW,CAAC,UAAU,CAAC;YAC7B,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,QAAQ;IACR,MAAM,UAA6B;QACjC;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,wBACP;;wBACG,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA;4BACvB,WAAW;4BACX,MAAM,eAAe,CAAC,OAAe;gCACnC,KAAK,MAAM,QAAQ,MAAO;oCACxB,IAAI,KAAK,EAAE,KAAK,IAAI;wCAClB,OAAO,KAAK,IAAI;oCAClB;oCACA,IAAI,KAAK,QAAQ,EAAE;wCACjB,MAAM,OAAO,aAAa,KAAK,QAAQ,EAAE;wCACzC,IAAI,MAAM,OAAO;oCACnB;gCACF;gCACA,OAAO;4BACT;4BAEA,MAAM,WAAW,aAAa,UAAU;4BACxC,OAAO,yBAAW,6LAAC,+KAAA,CAAA,MAAG;0CAAe;+BAAT;;;;uCAA2B;wBACzD;wBACC,QAAQ,MAAM,GAAG,mBAAK,6LAAC,+KAAA,CAAA,MAAG;;gCAAC;gCAAE,QAAQ,MAAM,GAAG;;;;;;;;;QAGrD;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,6LAAC,mMAAA,CAAA,QAAK;oBAAC,MAAK;;sCACV,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4BACnB,SAAS,IAAM,cAAc;;;;;;sCAE/B,6LAAC,6LAAA,CAAA,aAAU;4BACT,OAAM;4BACN,WAAW,IAAM,aAAa,OAAO,EAAE;4BACvC,QAAO;4BACP,YAAW;sCAEX,cAAA,6LAAC,qMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,MAAM;gCACN,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;QAK/B;KACD;IAED,qBACE;;0BACE,6LAAC;gBAAI,OAAO;oBAAE,SAAS;oBAAQ,gBAAgB;oBAAiB,cAAc;gBAAG;;kCAC/E,6LAAC;kCAAG;;;;;;kCACJ,6LAAC,qMAAA,CAAA,SAAM;wBAAC,MAAK;wBAAU,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wBAAK,SAAS;kCAAiB;;;;;;;;;;;;0BAK3E,6LAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAG;0BAC7B,cAAA,6LAAC,mLAAA,CAAA,QAAK,CAAC,MAAM;oBACX,aAAY;oBACZ,UAAU;oBACV,2BAAa,6LAAC,yNAAA,CAAA,iBAAc;;;;;oBAC5B,UAAU,CAAC,QAAU,QAAQ,GAAG,CAAC;oBACjC,OAAO;wBAAE,OAAO;oBAAI;;;;;;;;;;;0BAIxB,6LAAC,mLAAA,CAAA,QAAK;gBACJ,SAAS;gBACT,YAAY;gBACZ,QAAO;gBACP,SAAS;gBACT,YAAY;oBACV,SAAS;oBACT,UAAU;oBACV,OAAO;oBACP,iBAAiB;oBACjB,iBAAiB;oBACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;gBACxC;gBACA,UAAU;;;;;;0BAGZ,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAO,YAAY,SAAS;gBAC5B,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,cAAc;gBACd,OAAO;0BAEP,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBACH,MAAM;oBACN,QAAO;;sCAEP,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,6LAAC,mLAAA,CAAA,QAAK;gCAAC,aAAY;;;;;;;;;;;sCAGrB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAQ;6BAAE;sCAE7C,cAAA,6LAAC,mLAAA,CAAA,QAAK,CAAC,QAAQ;gCAAC,aAAY;gCAAQ,MAAM;;;;;;;;;;;sCAG5C,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,6LAAC,iLAAA,CAAA,OAAI;gCACH,SAAS;gCACT,UAAU;gCACV,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;AAO9B;GAnQwB;;QAWP,iLAAA,CAAA,OAAI,CAAC;;;KAXE", "debugId": null}}]}