package com.bosi.sim.paas.admin.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.dao.mapper.ads.AdsJobMapper;
import com.bosi.sim.paas.dao.model.ads.AdsJob;
import com.bosi.sim.paas.admin.server.service.CmsJobService;
import com.bosi.sim.paas.admin.server.util.AbstractQuartzJob;
import com.bosi.sim.paas.admin.server.util.CronUtils;
import com.bosi.sim.paas.admin.server.util.ScheduleUtils;
import org.quartz.JobDataMap;
import org.quartz.JobKey;
import org.quartz.Scheduler;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * 定时任务调度信息 服务层
 */
@Service
public class CmsJobServiceImpl implements CmsJobService {
    @Autowired
    private Scheduler scheduler;

    @Autowired
    private AdsJobMapper jobMapper;

    /**
     * 项目启动时，初始化定时器 主要是防止手动修改数据库导致未同步到定时任务处理（注：不能手动修改数据库ID和任务组名，否则会导致脏数据）
     */
    @PostConstruct
    public void init() throws SchedulerException, BizException {
        scheduler.clear();
        List<AdsJob> jobList = jobMapper.selectList(new LambdaQueryWrapper<>());
        for (AdsJob job : jobList) {
            ScheduleUtils.createScheduleJob(scheduler, job);
        }
    }

    /**
     * 获取quartz调度器的计划任务列表
     *
     * @param job 调度信息
     * @return
     */
    @Override
    public CommonPage<AdsJob> page(Page<AdsJob> page, AdsJob job) {
        LambdaQueryWrapper<AdsJob> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(job.getJobName())) {
            queryWrapper.like(AdsJob::getJobName, job.getJobName());
        }
        if (StringUtils.isNotEmpty(job.getJobGroup())) {
            queryWrapper.like(AdsJob::getJobGroup, job.getJobGroup());
        }
        if (StringUtils.isNotNull(job.getWhetherEnable())) {
            queryWrapper.eq(AdsJob::getWhetherEnable, job.getWhetherEnable());
        }
        if (StringUtils.isNotEmpty(job.getInvokeTarget())) {
            queryWrapper.like(AdsJob::getInvokeTarget, job.getInvokeTarget());
        }
        return CommonPage.restPage(jobMapper.selectPage(page, queryWrapper));
    }

    /**
     * 通过调度任务ID查询调度信息
     *
     * @param jobId 调度任务ID
     * @return 调度任务对象信息
     */
    @Override
    public AdsJob selectJobById(String jobId) {
        return jobMapper.selectById(jobId);
    }

    /**
     * 暂停任务
     *
     * @param job 调度信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int pauseJob(AdsJob job) throws SchedulerException {
        String jobId = job.getId();
        String jobGroup = job.getJobGroup();
        job.setWhetherEnable(false);
        int rows = jobMapper.updateById(job);
        if (rows > 0) {
            scheduler.pauseJob(ScheduleUtils.getJobKey(jobId, jobGroup));
        }
        return rows;
    }

    /**
     * 恢复任务
     *
     * @param job 调度信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int resumeJob(AdsJob job) throws SchedulerException {
        String jobId = job.getId();
        String jobGroup = job.getJobGroup();
        job.setWhetherEnable(true);
        int rows = jobMapper.updateById(job);
        if (rows > 0) {
            scheduler.resumeJob(ScheduleUtils.getJobKey(jobId, jobGroup));
        }
        return rows;
    }

    /**
     * 删除任务后，所对应的trigger也将被删除
     *
     * @param job 调度信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteJob(AdsJob job) throws SchedulerException {
        String jobId = job.getId();
        String jobGroup = job.getJobGroup();
        int rows = jobMapper.deleteById(jobId);
        if (rows > 0) {
            scheduler.deleteJob(ScheduleUtils.getJobKey(jobId, jobGroup));
        }
        return rows;
    }

    /**
     * 批量删除调度信息
     *
     * @param jobIds 需要删除的任务ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteJobByIds(List<String> jobIds) throws SchedulerException {
        for (String jobId : jobIds) {
            AdsJob job = jobMapper.selectById(jobId);
            deleteJob(job);
        }
    }

    /**
     * 任务调度状态修改
     *
     * @param job 调度信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int changeStatus(AdsJob job) throws SchedulerException {
        int rows;
        if (job.getWhetherEnable()) {
            rows = resumeJob(job);
        } else {
            rows = pauseJob(job);
        }
        return rows;
    }

    /**
     * 立即运行任务
     *
     * @param job 调度信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean run(AdsJob job) throws SchedulerException {
        boolean result = false;
        String jobId = job.getId();
        String jobGroup = job.getJobGroup();
        AdsJob properties = selectJobById(jobId);
        // 参数
        JobDataMap dataMap = new JobDataMap();
        dataMap.put(AbstractQuartzJob.TASK_PROPERTIES, properties);
        JobKey jobKey = ScheduleUtils.getJobKey(jobId, jobGroup);
        if (scheduler.checkExists(jobKey)) {
            result = true;
            scheduler.triggerJob(jobKey, dataMap);
        }
        return result;
    }

    /**
     * 新增任务
     *
     * @param job 调度信息 调度信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertJob(AdsJob job) throws SchedulerException, BizException {
        job.setWhetherEnable(false);
        int rows = jobMapper.insert(job);
        if (rows > 0) {
            ScheduleUtils.createScheduleJob(scheduler, job);
        }
        return rows;
    }

    /**
     * 更新任务的时间表达式
     *
     * @param job 调度信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateJob(AdsJob job) throws SchedulerException, BizException {
        AdsJob properties = selectJobById(job.getId());
        int rows = jobMapper.updateById(job);
        if (rows > 0) {
            updateSchedulerJob(job, properties.getJobGroup());
        }
        return rows;
    }

    /**
     * 更新任务
     *
     * @param job      任务对象
     * @param jobGroup 任务组名
     */
    public void updateSchedulerJob(AdsJob job, String jobGroup) throws SchedulerException, BizException {
        String jobId = job.getId();
        // 判断是否存在
        JobKey jobKey = ScheduleUtils.getJobKey(jobId, jobGroup);
        if (scheduler.checkExists(jobKey)) {
            // 防止创建时存在数据问题 先移除，然后在执行创建操作
            scheduler.deleteJob(jobKey);
        }
        ScheduleUtils.createScheduleJob(scheduler, job);
    }

    /**
     * 校验cron表达式是否有效
     *
     * @param cronExpression 表达式
     * @return 结果
     */
    @Override
    public boolean checkCronExpressionIsValid(String cronExpression) {
        return CronUtils.isValid(cronExpression);
    }
}
