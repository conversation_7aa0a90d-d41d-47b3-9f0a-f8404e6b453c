package com.bosi.sim.paas.admin.server.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.dao.model.tds.TdsUser;
import org.apache.ibatis.annotations.Param;

/**
 * 用户表 数据层
 */
public interface TmsUserDao {
    /**
     * 根据条件分页查询用户列表
     *
     * @param sysUser 用户信息
     * @return 用户信息集合信息
     */
    Page<TdsUser> selectUserList(Page<TdsUser> page, @Param("params") TdsUser sysUser);

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    TdsUser selectUserByUserName(String userName);

    TdsUser selectUserById(String id);

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    int resetUserPwd(@Param("userName") String userName, @Param("password") String password);

    /**
     * 校验用户名称是否唯一
     *
     * @param userName 用户名称
     * @return 结果
     */
    TdsUser checkUserNameUnique(String userName);

    /**
     * 校验手机号码是否唯一
     *
     * @param phone 手机号码
     * @return 结果
     */
    TdsUser checkPhoneUnique(String phone);

    /**
     * 校验email是否唯一
     *
     * @param email 用户邮箱
     * @return 结果
     */
    TdsUser checkEmailUnique(String email);
}
