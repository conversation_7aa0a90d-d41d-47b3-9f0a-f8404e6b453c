<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bosi.sim.paas.open.server.dao.OmsCartItemDao">

    <select id="listByMemberId" resultType="com.bosi.sim.paas.dao.model.sds.OmsCartItem">
        select
            ci.*,
            ss.price,ss.pic,ss.sku_code,ss.sp_data,ss.sku_name,
            p.product_name,p.sub_title,p.category_id,p.brand_id,p.product_sn,
            b.name as brand_name,
            pc.name as category_name
        from
            oms_cart_item ci
        left join
            sds_product p on p.id = ci.product_id
        left join
            sds_product_sku ss on ss.id = ci.product_sku_id
        left join
            sds_brand b on b.id = p.brand_id
        left join
            sds_category pc on pc.id = p.category_id
        where
            ci.whether_delete = false
        and
            ci.member_id = #{memberId}
    </select>

</mapper>
