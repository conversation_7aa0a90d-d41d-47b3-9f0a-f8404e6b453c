package com.bosi.sim.paas.dao.model.sds;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import com.bosi.sim.paas.dao.model.sds.jsonclz.ReceiverDetailJsonClass;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@TableName("sds_order")
public class SdsOrder extends BaseEntity {
    @ApiModelProperty(value = "订单id")
    private String memberId;

    private String couponId;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    @ApiModelProperty(value = "用户帐号")
    private String memberUsername;

    @ApiModelProperty(value = "订单总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "应付金额（实际支付金额）")
    private BigDecimal totalPayAmount;

    @ApiModelProperty(value = "促销优惠金额（促销价、满减、阶梯价）")
    private BigDecimal totalPromotionAmount;

    @ApiModelProperty(value = "优惠券抵扣金额")
    private BigDecimal totalCouponAmount;

    @ApiModelProperty(value = "支付方式")
    private Integer payType;

    @ApiModelProperty(value = "订单来源：0->PC订单；1->app订单")
    private Integer sourceType;

    @ApiModelProperty(value = "订单状态：0->待付款；1->待发货；2->已发货；3->已完成；4->已退款；5->无效订单")
    private Integer status;

    @ApiModelProperty(value = "获得的积分")
    private Integer totalGiftPoint;

    @ApiModelProperty(value = "获得的成长值")
    private Integer totalGiftGrowth;

    @ApiModelProperty(value = "活动信息")
    private String promotionInfo;

    private String receiverDetailJson;

    @ApiModelProperty(value = "订单备注")
    private String note;

    @ApiModelProperty(value = "确认收货状态：0->未确认；1->已确认")
    private Boolean whetherConfirm;

    @ApiModelProperty(value = "支付时间")
    private Date paymentTime;

    @ApiModelProperty(value = "发货时间")
    private Date deliveryTime;

    @ApiModelProperty(value = "评价时间")
    private Date commentTime;

    @ApiModelProperty(value = "确认收货时间")
    private Date confirmTime;

    @ApiModelProperty("订单商品列表")
    @TableField(exist = false)
    private List<SdsOrderItem> orderItemList;

    private String partnerOrderId;

    @TableField(exist = false)
    private ReceiverDetailJsonClass receiverDetailJsonClass;

    @ApiModelProperty("发货记录")
    @TableField(exist = false)
    private List<SdsOrderDelivery> orderDeliveryList;

    @TableField(exist = false)
    private String deviceProfileUrl;

}
