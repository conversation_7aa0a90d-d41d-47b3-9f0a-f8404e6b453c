package com.bosi.sim.paas.admin.server.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.dao.model.tds.TdsRole;

import java.util.List;

/**
 * 角色业务层
 */
public interface TmsRoleService {
    /**
     * 根据条件分页查询角色数据
     *
     * @return 角色数据集合信息
     */
    CommonPage<TdsRole> page(Page<TdsRole> page, TdsRole role);

    /**
     * 查询所有角色
     *
     * @return 角色列表
     */
    List<TdsRole> list(TdsRole role);

    /**
     * 通过角色ID查询角色
     *
     * @param roleId 角色ID
     * @return 角色对象信息
     */
    TdsRole selectRoleById(String roleId);

    /**
     * 新增保存角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
    int insertRole(TdsRole role);

    /**
     * 修改保存角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
    int updateRole(TdsRole role);

    int updateRoleStatus(TdsRole role);

    /**
     * 批量删除角色信息
     *
     * @param roleIds 需要删除的角色ID
     * @return 结果
     */
    int deleteRoleByIds(String[] roleIds);

    boolean checkRoleNameUnique(String roleId, String roleName, String distributorId);

}
