<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bosi.sim.paas.open.server.dao.PmsProductDao">
    <resultMap id="CartProductMap" type="com.bosi.sim.paas.dao.model.sds.PmsProduct">
        <id column="id" property="id" />
        <result column="brand_id" property="brandId" />
        <result column="product_category_id" property="categoryId" />
        <result column="product_attribute_category_id" property="attributeId" />
        <result column="product_name" property="productName" />
        <result column="pic" property="pic" />
        <result column="product_sn" property="productSn" />
        <result column="whether_publish" property="whetherPublish" />
        <result column="sort" property="sort" />
        <result column="gift_growth" property="giftGrowth" />
        <result column="gift_point" property="giftPoint" />
        <result column="sub_title" property="subTitle" />
        <result column="unit" property="unit" />
        <result column="weight" property="weight" />
        <result column="service_ids" property="serviceIds" />
        <result column="keywords" property="keywords" />
        <result column="note" property="note" />
        <result column="album_pics" property="albumPics" />
        <result column="detail_title" property="detailTitle" />
        <result column="detail_desc" property="detailDesc" />
        <result column="detail_html" property="detailHtml" />
        <result column="detail_mobile_html" property="detailMobileHtml" />
        <result column="whether_virtually" property="whetherVirtually" />
        <result column="promotion_type" property="promotionType" />
        <result column="promotion_start_time" property="promotionStartTime" />
        <result column="promotion_end_time" property="promotionEndTime" />
        <result column="promotion_per_member_limit" property="promotionPerMemberLimit" />
        <collection property="productAttributeTypeList" columnPrefix="attr_" javaType="java.util.List" resultMap="PmsProductAttributeResultMap"/>
        <collection property="productSkuList" columnPrefix="sku_" javaType="java.util.List" resultMap="PmsProductSkuResultMap"/>
    </resultMap>

    <resultMap id="PmsProductAttributeResultMap" type="com.bosi.sim.paas.dao.model.sds.PmsAttributeType">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="select_type" property="selectType" />
        <result column="input_list" property="inputList" />
        <result column="sort" property="sort" />
    </resultMap>

    <resultMap id="PmsProductSkuResultMap" type="com.bosi.sim.paas.dao.model.sds.PmsProductSku">
        <id column="id" property="id" />
        <result column="product_id" property="productId" />
        <result column="sku_name" property="skuName" />
        <result column="sku_code" property="skuCode" />
        <result column="price" property="price" />
        <result column="stock" property="stock" />
        <result column="low_stock" property="lowStock" />
        <result column="pic" property="pic" />
        <result column="sale" property="sale" />
        <result column="original_price" property="originalPrice" />
        <result column="lock_stock" property="lockStock" />
        <result column="sp_data" property="spData" />
    </resultMap>

    <resultMap id="PmsProductLadderResultMap" type="com.bosi.sim.paas.dao.model.sds.PmsProductLadder">
        <id column="id" property="id" />
        <result column="product_id" property="productId" />
        <result column="count" property="count" />
        <result column="discount" property="discount" />
    </resultMap>

    <resultMap id="PmsProductFullReductionResultMap" type="com.bosi.sim.paas.dao.model.sds.PmsProductFullReduction">
        <id column="id" property="id" />
        <result column="product_id" property="productId" />
        <result column="full_price" property="fullPrice" />
        <result column="reduce_price" property="reducePrice" />
    </resultMap>

    <select id="getCartProduct" resultMap="CartProductMap">
        select
            p.id,
            p.product_name,
            p.sub_title,
            pa.id attr_id,
            pa.name attr_name,
            ps.id sku_id,
            ps.sku_name sku_sku_name,
            ps.product_id sku_product_id,
            ps.sku_name sku_sku_name,
            ps.sku_code sku_sku_code,
            ps.price sku_price,
            ps.stock sku_stock,
            ps.sp_data sku_sp_data,
            ps.original_price sku_original_price,
            ps.pic sku_pic
        from
            sds_product p
        left join
            sds_attribute_type pa on p.attribute_id = pa.id
        left join
            sds_product_sku ps on p.id = ps.product_id
        where
            p.id = #{id}
        and
            p.whether_delete = false
        and
            pa.classify = 1
        order by pa.sort desc
    </select>

    <select id="page" resultType="com.bosi.sim.paas.dao.model.sds.PmsProduct">
        select r.* from
        (
            select
                p.id,
                p.product_name,
                p.sub_title,
                p.promotion_type,
                p.original_price original_price,
                p.price price,
                p.pic,
                p.whether_virtually,
                p.create_time,
                (select sum(sale) from sds_product_sku as ps where ps.product_id = p.id) total_sale
            from
                sds_product p
            <where>
                <if test="true">
                    and p.whether_delete = false
                </if>
                <if test="params.whetherPublish != null">
                    and p.whether_publish=#{params.whetherPublish}
                </if>
                <if test="params.productName != null and params.productName!= ''">
                    and p.product_name like concat('%', #{params.productName}, '%')
                </if>
                <if test="params.whetherVirtually != null">
                    and p.whether_virtually=#{params.whetherVirtually}
                </if>
                <if test="params.categoryId != null">
                    and p.category_id=#{params.categoryId}
                </if>
                <if test="params.brandId != null">
                    and p.brand_id=#{params.brandId}
                </if>
            </where>
        ) as r
    </select>

    <select id="detail" resultType="com.bosi.sim.paas.dao.model.sds.PmsProduct">
        select
            p.*,
            (select sum(stock) from sds_product_sku as ps where ps.product_id = p.id) total_stock,
            (select sum(sale) from sds_product_sku as ps where ps.product_id = p.id) total_sale
        from
            sds_product p
        where
            p.whether_delete = false
        and
            p.id = #{id}
    </select>

    <select id="listByIds" resultType="com.bosi.sim.paas.dao.model.sds.PmsProduct">
        select
            p.*,c.name category_name,b.name brand_name
        from
            sds_product p
        left join
            sds_category c on p.category_id = c.id
        left join
            sds_brand b on p.brand_id = b.id
        where
            p.whether_delete = false
        and
            p.id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>
