package com.bosi.sim.paas.admin.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.utils.IdUtils;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.dao.mapper.tms.TmsNotifyMapper;
import com.bosi.sim.paas.dao.mapper.tms.TmsNotifyRecordMapper;
import com.bosi.sim.paas.dao.model.tds.TdsTenant;
import com.bosi.sim.paas.dao.model.tds.TmsNotify;
import com.bosi.sim.paas.dao.model.tds.TdsNotifyRecord;
import com.bosi.sim.paas.admin.server.dao.TmsNotifyRecordDao;
import com.bosi.sim.paas.admin.server.service.TmsNotifyService;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

@Service
public class TmsNotifyServiceImpl implements TmsNotifyService {

    @Autowired
    private TmsNotifyMapper tmsNotifyMapper;

    @Autowired
    private TmsNotifyRecordDao tmsNotifyRecordDao;

    @Autowired
    private TmsNotifyRecordMapper tmsNotifyRecordMapper;

    @Override
    public CommonPage<TmsNotify> page(Page<TmsNotify> page, TmsNotify notify) {
        LambdaQueryWrapper<TmsNotify> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(notify.getQuerySearchValue())) {
            queryWrapper.like(TmsNotify::getContent, notify.getQuerySearchValue()).or().like(TmsNotify::getTitle, notify.getQuerySearchValue());
        }
        return CommonPage.restPage(tmsNotifyMapper.selectPage(page, queryWrapper));
    }

    @Override
    public TmsNotify getById(String id) {
        return tmsNotifyMapper.selectById(id);
    }

    @Override
    @Transactional
    public int insert(TmsNotify notify) {
        int result = tmsNotifyMapper.insert(notify);
        List<TdsNotifyRecord> tmsNotifyRecordList = Lists.newArrayList();
        notify.getDistributorIds().forEach(disId -> {
            TdsNotifyRecord tmsNotifyRecord = new TdsNotifyRecord();
            tmsNotifyRecord.setId(IdUtils.fastSimpleUUID());
            tmsNotifyRecord.setNotifyId(notify.getId());
            tmsNotifyRecord.setDistributorId(disId);
            tmsNotifyRecord.setWhetherRead(false);
            tmsNotifyRecordList.add(tmsNotifyRecord);

        });
        tmsNotifyRecordDao.insertList(tmsNotifyRecordList);
        return result;
    }

    @Override
    public int update(TmsNotify notify) {
        return tmsNotifyMapper.updateById(notify);
    }

    @Override
    public int deleteByIds(String[] ids) {
        int result = tmsNotifyMapper.deleteBatchIds(Arrays.asList(ids));
        LambdaQueryWrapper<TdsNotifyRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(TdsNotifyRecord::getNotifyId, ids);
        tmsNotifyRecordMapper.delete(wrapper);
        return result;
    }

    @Override
    public CommonPage<TdsNotifyRecord> pageRecord(Page<TdsNotifyRecord> page, TdsNotifyRecord notifyRecord) {
        return CommonPage.restPage(tmsNotifyRecordDao.page(page, notifyRecord));
    }

    @Override
    public CommonPage<TdsTenant> pageDistributorForNotify(Page<TdsTenant> page, TdsTenant distributor) {
        return CommonPage.restPage(tmsNotifyRecordDao.pageDistributorForNotify(page, distributor));
    }

    @Override
    public int insertRecord(TdsNotifyRecord notifyRecord) {
        return tmsNotifyRecordMapper.insert(notifyRecord);
    }

    @Override
    public int deleteRecordByIds(String[] ids) {
        return tmsNotifyRecordMapper.deleteBatchIds(Arrays.asList(ids));
    }
}
