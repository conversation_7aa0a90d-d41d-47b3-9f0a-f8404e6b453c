package com.bosi.sim.paas.admin.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.dao.model.sds.SdsMember;
import com.bosi.sim.paas.dao.model.sds.SdsMemberGrowthHistory;
import com.bosi.sim.paas.dao.model.sds.SdsMemberPointHistory;
import com.bosi.sim.paas.dao.model.sds.SdsMemberReceiveAddress;
import com.bosi.sim.paas.dao.model.sds.SmsCouponHistory;

import java.util.List;

/**
 * 会员管理Service
 */
public interface MmsMemberService {

    CommonPage<SdsMember> page(Page<SdsMember> ipage, SdsMember member);

    SdsMember detail(String id);

    int updateStatus(SdsMember member);

    CommonPage<SdsMemberGrowthHistory> pageGrowth(Page<SdsMemberGrowthHistory> ipage, SdsMemberGrowthHistory memberGrowthHistory);

    CommonPage<SdsMemberPointHistory> pagePoint(Page<SdsMemberPointHistory> ipage, SdsMemberPointHistory memberPointHistory);

    CommonPage<SmsCouponHistory> pageCoupon(Page<SmsCouponHistory> ipage, SmsCouponHistory couponHistory);

    List<SdsMemberReceiveAddress> listAddressByMemberId(String memberId);

}
