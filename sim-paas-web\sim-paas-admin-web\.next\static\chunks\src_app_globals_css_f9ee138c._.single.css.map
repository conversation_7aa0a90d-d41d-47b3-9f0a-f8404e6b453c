{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/globals.css"], "sourcesContent": ["/*! tailwindcss v4.1.5 | MIT License | https://tailwindcss.com */\n@layer properties;\n@layer theme, base, components, utilities;\n@layer theme {\n  :root, :host {\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\n    --default-font-family: var(--font-geist-sans);\n    --default-mono-font-family: var(--font-geist-mono);\n  }\n}\n@layer base {\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\n    box-sizing: border-box;\n    margin: 0;\n    padding: 0;\n    border: 0 solid;\n  }\n  html, :host {\n    line-height: 1.5;\n    -webkit-text-size-adjust: 100%;\n    tab-size: 4;\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\");\n    font-feature-settings: var(--default-font-feature-settings, normal);\n    font-variation-settings: var(--default-font-variation-settings, normal);\n    -webkit-tap-highlight-color: transparent;\n  }\n  hr {\n    height: 0;\n    color: inherit;\n    border-top-width: 1px;\n  }\n  abbr:where([title]) {\n    -webkit-text-decoration: underline dotted;\n    text-decoration: underline dotted;\n  }\n  h1, h2, h3, h4, h5, h6 {\n    font-size: inherit;\n    font-weight: inherit;\n  }\n  a {\n    color: inherit;\n    -webkit-text-decoration: inherit;\n    text-decoration: inherit;\n  }\n  b, strong {\n    font-weight: bolder;\n  }\n  code, kbd, samp, pre {\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace);\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\n    font-size: 1em;\n  }\n  small {\n    font-size: 80%;\n  }\n  sub, sup {\n    font-size: 75%;\n    line-height: 0;\n    position: relative;\n    vertical-align: baseline;\n  }\n  sub {\n    bottom: -0.25em;\n  }\n  sup {\n    top: -0.5em;\n  }\n  table {\n    text-indent: 0;\n    border-color: inherit;\n    border-collapse: collapse;\n  }\n  :-moz-focusring {\n    outline: auto;\n  }\n  progress {\n    vertical-align: baseline;\n  }\n  summary {\n    display: list-item;\n  }\n  ol, ul, menu {\n    list-style: none;\n  }\n  img, svg, video, canvas, audio, iframe, embed, object {\n    display: block;\n    vertical-align: middle;\n  }\n  img, video {\n    max-width: 100%;\n    height: auto;\n  }\n  button, input, select, optgroup, textarea, ::file-selector-button {\n    font: inherit;\n    font-feature-settings: inherit;\n    font-variation-settings: inherit;\n    letter-spacing: inherit;\n    color: inherit;\n    border-radius: 0;\n    background-color: transparent;\n    opacity: 1;\n  }\n  :where(select:is([multiple], [size])) optgroup {\n    font-weight: bolder;\n  }\n  :where(select:is([multiple], [size])) optgroup option {\n    padding-inline-start: 20px;\n  }\n  ::file-selector-button {\n    margin-inline-end: 4px;\n  }\n  ::placeholder {\n    opacity: 1;\n  }\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\n    ::placeholder {\n      color: currentcolor;\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, currentcolor 50%, transparent);\n      }\n    }\n  }\n  textarea {\n    resize: vertical;\n  }\n  ::-webkit-search-decoration {\n    -webkit-appearance: none;\n  }\n  ::-webkit-date-and-time-value {\n    min-height: 1lh;\n    text-align: inherit;\n  }\n  ::-webkit-datetime-edit {\n    display: inline-flex;\n  }\n  ::-webkit-datetime-edit-fields-wrapper {\n    padding: 0;\n  }\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\n    padding-block: 0;\n  }\n  :-moz-ui-invalid {\n    box-shadow: none;\n  }\n  button, input:where([type=\"button\"], [type=\"reset\"], [type=\"submit\"]), ::file-selector-button {\n    appearance: button;\n  }\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\n    height: auto;\n  }\n  [hidden]:where(:not([hidden=\"until-found\"])) {\n    display: none !important;\n  }\n}\n@layer utilities {\n  .absolute {\n    position: absolute;\n  }\n  .fixed {\n    position: fixed;\n  }\n  .relative {\n    position: relative;\n  }\n  .block {\n    display: block;\n  }\n  .flex {\n    display: flex;\n  }\n  .hidden {\n    display: none;\n  }\n  .inline {\n    display: inline;\n  }\n  .min-h-screen {\n    min-height: 100vh;\n  }\n  .items-center {\n    align-items: center;\n  }\n  .justify-center {\n    justify-content: center;\n  }\n  .antialiased {\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n  .ease-in-out {\n    --tw-ease: var(--ease-in-out);\n    transition-timing-function: var(--ease-in-out);\n  }\n}\n:root {\n  --background: #ffffff;\n  --foreground: #171717;\n  --font-size-base: 14px;\n  --font-size-sm: 12px;\n  --font-size-lg: 16px;\n  --font-size-xl: 18px;\n  --font-size-xxl: 24px;\n  --line-height-base: 1.5;\n  --border-radius-base: 4px;\n  --border-radius-sm: 2px;\n  --border-radius-lg: 8px;\n  --transition-duration: 0.2s;\n}\n@media (prefers-color-scheme: dark) {\n  :root {\n    --background: #0a0a0a;\n    --foreground: #ededed;\n  }\n}\nbody {\n  background: var(--background);\n  color: var(--foreground);\n  font-family: Arial, Helvetica, sans-serif;\n  font-size: var(--font-size-base);\n  line-height: var(--line-height-base);\n}\n.page-enter {\n  opacity: 0;\n}\n.page-enter-active {\n  opacity: 1;\n  transition: opacity 300ms;\n}\n.page-exit {\n  opacity: 1;\n}\n.page-exit-active {\n  opacity: 0;\n  transition: opacity 300ms;\n}\n.ant-layout-content {\n  transition: all 0.2s ease-in-out;\n}\nhtml {\n  scroll-behavior: smooth;\n}\n.ant-table,\n.ant-form,\n.ant-card,\n.ant-modal {\n  transition: opacity 0.2s ease-in-out;\n}\n.tabs-nav-container .ant-tabs-nav {\n  margin-bottom: 0;\n}\n.tabs-nav-container .ant-tabs-tab {\n  transition: all 0.3s;\n  padding: 8px 16px;\n}\n.tabs-nav-container .ant-tabs-tab-active {\n  background-color: #f0f2f5;\n  border-bottom-color: transparent !important;\n}\n.tabs-nav-container .ant-tabs-tab-btn {\n  transition: color 0.3s;\n}\n.cached-views-container {\n  position: relative;\n  min-height: 300px;\n  width: 100%;\n}\n.cached-view {\n  width: 100%;\n  height: 100%;\n}\n@keyframes fadeIn {\n  from {\n    opacity: 0.9;\n  }\n  to {\n    opacity: 1;\n  }\n}\n.ant-layout-content {\n  transition: none !important;\n}\n.ant-layout-content .ant-tabs-content {\n  height: 100%;\n  margin: 0;\n}\n::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 3px;\n}\n::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 3px;\n}\n::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n.ant-table {\n  font-size: var(--font-size-base);\n}\n.ant-table-small {\n  font-size: var(--font-size-sm);\n}\n.ant-form-item-label > label {\n  font-size: var(--font-size-base);\n}\n.ant-form-item-explain,\n.ant-form-item-extra {\n  font-size: var(--font-size-sm);\n}\n.ant-btn {\n  font-size: var(--font-size-base);\n}\n.ant-btn-sm {\n  font-size: var(--font-size-sm);\n}\n.ant-btn-lg {\n  font-size: var(--font-size-lg);\n}\n.ant-menu-item {\n  font-size: var(--font-size-base);\n}\nh1 {\n  font-size: var(--font-size-xxl);\n  font-weight: 500;\n  margin-bottom: 16px;\n}\nh2 {\n  font-size: var(--font-size-xl);\n  font-weight: 500;\n  margin-bottom: 14px;\n}\nh3 {\n  font-size: var(--font-size-lg);\n  font-weight: 500;\n  margin-bottom: 12px;\n}\n.ant-card-head-title {\n  font-size: var(--font-size-lg);\n}\n.ant-tabs-tab {\n  font-size: var(--font-size-base);\n}\n.ant-dropdown-menu-item {\n  font-size: var(--font-size-base);\n}\n.ant-modal-title {\n  font-size: var(--font-size-lg);\n}\n.ant-statistic-title {\n  font-size: var(--font-size-base);\n}\n.ant-statistic-content {\n  font-size: var(--font-size-xxl);\n}\n@property --tw-ease {\n  syntax: \"*\";\n  inherits: false;\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-ease: initial;\n    }\n  }\n}\n"], "names": [], "mappings": "AACA;EA2WE;IACE;;;;;;AA3WJ;EAEE;;;;;;;AAFF;EASE;;;;;;;EAMA;;;;;;;;;;EASA;;;;;;EAKA;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;EAUA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;IACE;;;;IAEE;MAAgD;;;;;;EAKpD;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;AArJF;;AAAA;EA0JE;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;;AAKF;;;;;;;;;;;;;;;AAcA;EACE;;;;;;AAKF;;;;;;;;AAOA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAOA;;;;AAGA;;;;AAMA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;;AAIA;;;;;;;;;;AAQA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA"}}]}