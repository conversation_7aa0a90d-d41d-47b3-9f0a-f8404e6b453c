<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bosi.sim.paas.admin.server.dao.PmsProductSkuDao">

    <select id="pageForReferralCode" resultType="com.bosi.sim.paas.dao.model.sds.PmsProductSku">
        select
            s.*,p.whether_publish
        from
            sds_product_sku s
        left join
            sds_product p on s.product_id = p.id
        <where>
            <if test="true">
                and p.whether_delete = false
            </if>
            <if test="params.querySearchValue != null and params.querySearchValue != ''">
                and (p.product_name like concat('%', #{params.querySearchValue}, '%') or p.product_sn like concat('%', #{params.querySearchValue}, '%'))
            </if>
        </where>
    </select>

    <insert id="insertList" useGeneratedKeys="true" keyProperty="id">
        <foreach collection="list" item="item" separator=";">
            insert into sds_product_sku
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">id,</if>
                <if test="item.productId != null">product_id,</if>
                <if test="item.skuName != null">sku_name,</if>
                <if test="item.skuCode != null">sku_code,</if>
                <if test="item.price != null">price,</if>
                <if test="item.stock != null">stock,</if>
                <if test="item.sort != null">sort,</if>
                <if test="item.lowStock != null">low_stock,</if>
                <if test="item.pic != null">pic,</if>
                <if test="item.sale != null">sale,</if>
                <if test="item.originalPrice != null">original_price,</if>
                <if test="item.lockStock != null">lock_stock,</if>
                <if test="item.spData != null">sp_data,</if>
            </trim>
            values
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">#{item.id},</if>
                <if test="item.productId != null">#{item.productId},</if>
                <if test="item.skuName != null">#{item.skuName},</if>
                <if test="item.skuCode != null">#{item.skuCode},</if>
                <if test="item.price != null">#{item.price},</if>
                <if test="item.stock != null">#{item.stock},</if>
                <if test="item.sort != null">#{item.sort},</if>
                <if test="item.lowStock != null">#{item.lowStock},</if>
                <if test="item.pic != null">#{item.pic},</if>
                <if test="item.sale != null">#{item.sale},</if>
                <if test="item.originalPrice != null">#{item.originalPrice},</if>
                <if test="item.lockStock != null">#{item.lockStock},</if>
                <if test="item.spData != null">#{item.spData},</if>
            </trim>
        </foreach>
    </insert>

    <update id="updateList">
        <foreach collection="list" item="item" separator=";">
            update sds_product_sku
            <set>
                <if test="item.productId != null">product_id=#{item.productId},</if>
                <if test="item.skuName != null">sku_name=#{item.skuName},</if>
                <if test="item.skuCode != null">sku_code=#{item.skuCode},</if>
                <if test="item.price != null">price=#{item.price},</if>
                <if test="item.stock != null">stock=#{item.stock},</if>
                <if test="item.sort != null">sort=#{item.sort},</if>
                <if test="item.lowStock != null">low_stock=#{item.lowStock},</if>
                <if test="item.pic != null">pic=#{item.pic},</if>
                <if test="item.sale != null">sale=#{item.sale},</if>
                <if test="item.originalPrice != null">original_price=#{item.originalPrice},</if>
                <if test="item.lockStock != null">lock_stock=#{item.lockStock},</if>
                <if test="item.spData != null">sp_data=#{item.spData},</if>
            </set>
           where id = #{item.id}
        </foreach>
    </update>
</mapper>
