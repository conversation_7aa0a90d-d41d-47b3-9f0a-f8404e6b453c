package com.bosi.sim.paas.dao.model.sds;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("sms_promotion")
public class SdsPromotion extends BaseEntity {
    @ApiModelProperty(value = "促销类型：0->没有促销；1->秒杀；2->特惠；3->团购")
    private Integer type;

    @ApiModelProperty(value = "促销名称")
    private String name;

    @ApiModelProperty(value = "促销开始时间")
    private Date startTime;

    @ApiModelProperty(value = "促销结束时间")
    private Date endTime;

    @ApiModelProperty(value = "促销状态")
    private Boolean status;

    @ApiModelProperty(value = "商品ID")
    private String productId;

    @ApiModelProperty(value = "促销价格")
    private BigDecimal promotionPrice;
}


