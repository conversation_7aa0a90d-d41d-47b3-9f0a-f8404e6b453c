package com.bosi.sim.paas.admin.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.dao.enums.sds.SdsReturnApplyStatusEnum;
import com.bosi.sim.paas.dao.mapper.oms.OmsOrderReturnApplyMapper;
import com.bosi.sim.paas.dao.model.sds.OmsOrderReturnApply;
import com.bosi.sim.paas.admin.server.dao.OmsOrderReturnApplyDao;
import com.bosi.sim.paas.admin.server.domain.OmsUpdateStatusParam;
import com.bosi.sim.paas.admin.server.service.OmsOrderReturnApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 订单退货管理Service实现类
 */
@Service
public class OmsOrderReturnApplyServiceImpl implements OmsOrderReturnApplyService {
    @Autowired
    private OmsOrderReturnApplyDao returnApplyDao;

    @Autowired
    private OmsOrderReturnApplyMapper returnApplyMapper;

    @Override
    public CommonPage<OmsOrderReturnApply> page(Page<OmsOrderReturnApply> page, OmsOrderReturnApply omsOrderReturnApply) {
        LambdaQueryWrapper<OmsOrderReturnApply> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotNull(omsOrderReturnApply.getId())) {
            queryWrapper.eq(OmsOrderReturnApply::getId, omsOrderReturnApply.getId());
        }
        if (StringUtils.isNotNull(omsOrderReturnApply.getStatus())) {
            queryWrapper.eq(OmsOrderReturnApply::getStatus, omsOrderReturnApply.getStatus());
        }
        if (StringUtils.isNotEmpty(omsOrderReturnApply.getHandleMan())) {
            queryWrapper.eq(OmsOrderReturnApply::getHandleMan, omsOrderReturnApply.getHandleMan());
        }
        if (StringUtils.isNotEmpty(omsOrderReturnApply.getQueryBeginTime())) {
            queryWrapper.ge(OmsOrderReturnApply::getCreateTime, omsOrderReturnApply.getQueryBeginTime());
        }
        if (StringUtils.isNotEmpty(omsOrderReturnApply.getQueryEndTime())) {
            queryWrapper.le(OmsOrderReturnApply::getCreateTime, omsOrderReturnApply.getQueryEndTime());
        }
        if (StringUtils.isNotNull(omsOrderReturnApply.getHandleTime())) {
            queryWrapper.like(OmsOrderReturnApply::getHandleTime, omsOrderReturnApply.getHandleTime());
        }
        if (StringUtils.isNotEmpty(omsOrderReturnApply.getQuerySearchValue())) {
            queryWrapper.like(OmsOrderReturnApply::getReturnName, omsOrderReturnApply.getQuerySearchValue()).or().like(OmsOrderReturnApply::getReturnPhone, omsOrderReturnApply.getQuerySearchValue());
        }
        return CommonPage.restPage(returnApplyMapper.selectPage(page, queryWrapper));
    }

    @Override
    public int delete(List<String> ids) {
        return returnApplyMapper.delete(new LambdaQueryWrapper<OmsOrderReturnApply>().in(OmsOrderReturnApply::getId, ids).eq(OmsOrderReturnApply::getStatus, SdsReturnApplyStatusEnum.APPLY_REFUSE.getReturnApplyStatus()));
    }

    @Override
    public int updateStatus(OmsUpdateStatusParam statusParam) {
        Integer status = statusParam.getStatus();
        String id = statusParam.getId();
        OmsOrderReturnApply returnApply = new OmsOrderReturnApply();
        if (status.equals(SdsReturnApplyStatusEnum.IN_RETURN.getReturnApplyStatus())) {
            //确认退货
            returnApply.setId(id);
            returnApply.setStatus(SdsReturnApplyStatusEnum.IN_RETURN.getReturnApplyStatus());
            returnApply.setReturnAmount(statusParam.getReturnAmount());
            returnApply.setCompanyAddressId(statusParam.getCompanyAddressId());
            returnApply.setHandleTime(new Date());
            returnApply.setHandleMan(statusParam.getHandleMan());
            returnApply.setHandleNote(statusParam.getHandleNote());
        } else if (status.equals(SdsReturnApplyStatusEnum.APPLY_OK.getReturnApplyStatus())) {
            //完成退货
            returnApply.setId(id);
            returnApply.setStatus(SdsReturnApplyStatusEnum.APPLY_OK.getReturnApplyStatus());
            returnApply.setReceiveTime(new Date());
            returnApply.setReceiveMan(statusParam.getReceiveMan());
            returnApply.setReceiveNote(statusParam.getReceiveNote());
        } else if (status.equals(SdsReturnApplyStatusEnum.APPLY_REFUSE.getReturnApplyStatus())) {
            //拒绝退货
            returnApply.setId(id);
            returnApply.setStatus(SdsReturnApplyStatusEnum.APPLY_REFUSE.getReturnApplyStatus());
            returnApply.setHandleTime(new Date());
            returnApply.setHandleMan(statusParam.getHandleMan());
            returnApply.setHandleNote(statusParam.getHandleNote());
        } else {
            return 0;
        }
        return returnApplyMapper.updateById(returnApply);
    }

    @Override
    public OmsOrderReturnApply getItem(String id) {
        return returnApplyDao.getDetail(id);
    }
}
