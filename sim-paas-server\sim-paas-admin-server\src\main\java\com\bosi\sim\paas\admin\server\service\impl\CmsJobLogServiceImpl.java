package com.bosi.sim.paas.admin.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.dao.mapper.ads.AdsJobLogMapper;
import com.bosi.sim.paas.dao.model.ads.AdsJobLog;
import com.bosi.sim.paas.admin.server.service.CmsJobLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 定时任务调度日志信息 服务层
 */
@Service
public class CmsJobLogServiceImpl implements CmsJobLogService {
    @Autowired
    private AdsJobLogMapper jobLogMapper;

    /**
     * 获取quartz调度器日志的计划任务
     *
     * @return 调度任务日志集合
     */
    @Override
    public CommonPage<AdsJobLog> page(Page<AdsJobLog> page, AdsJobLog jobLog) {
        LambdaQueryWrapper<AdsJobLog> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(jobLog.getJobName())) {
            queryWrapper.like(AdsJobLog::getJobName, jobLog.getJobName());
        }
        if (StringUtils.isNotEmpty(jobLog.getJobGroup())) {
            queryWrapper.like(AdsJobLog::getJobGroup, jobLog.getJobGroup());
        }
        if (StringUtils.isNotNull(jobLog.getWhetherSuccess())) {
            queryWrapper.eq(AdsJobLog::getWhetherSuccess, jobLog.getWhetherSuccess());
        }
        if (StringUtils.isNotEmpty(jobLog.getInvokeTarget())) {
            queryWrapper.like(AdsJobLog::getInvokeTarget, jobLog.getInvokeTarget());
        }
        if (StringUtils.isNotEmpty(jobLog.getQueryBeginTime())) {
            queryWrapper.gt(AdsJobLog::getCreateTime, jobLog.getQueryBeginTime());
        }
        if (StringUtils.isNotEmpty(jobLog.getQueryEndTime())) {
            queryWrapper.lt(AdsJobLog::getCreateTime, jobLog.getQueryEndTime());
        }
        return CommonPage.restPage(jobLogMapper.selectPage(page, queryWrapper));
    }

    /**
     * 通过调度任务日志ID查询调度信息
     *
     * @param jobLogId 调度任务日志ID
     * @return 调度任务日志对象信息
     */
    @Override
    public AdsJobLog selectJobLogById(String jobLogId) {
        return jobLogMapper.selectById(jobLogId);
    }

    /**
     * 新增任务日志
     *
     * @param jobLog 调度日志信息
     */
    @Override
    public void addJobLog(AdsJobLog jobLog) {
        jobLogMapper.insert(jobLog);
    }

    /**
     * 批量删除调度日志信息
     *
     * @param logIds 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteJobLogByIds(List<String> logIds) {
        return jobLogMapper.deleteBatchIds(logIds);
    }

    /**
     * 删除任务日志
     *
     * @param jobId 调度日志ID
     */
    @Override
    public int deleteJobLogById(String jobId) {
        return jobLogMapper.deleteById(jobId);
    }
}
