package com.bosi.sim.paas.dao.model.sds;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.esim.mall.dao.model.BaseEntity;
import com.bosi.esim.mall.dao.model.oms.jsonclz.DivideDetailJsonClass;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("oms_order_item")
public class OmsOrderItem extends BaseEntity {
    @ApiModelProperty(value = "订单id")
    private String orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    private String productId;

    private String productPic;

    private String productName;

    @ApiModelProperty(value = "商品分类名称")
    private String productCategoryName;

    private String productBrandName;

    private String productSn;

    @ApiModelProperty(value = "商品sku编号")
    private String productSkuId;

    private String productSkuName;

    @ApiModelProperty(value = "商品sku条码")
    private String productSkuCode;

    @ApiModelProperty(value = "商品sku图片")
    private String productSkuPic;

    @ApiModelProperty(value = "购买数量")
    private Integer productQuantity;

    @ApiModelProperty(value = "商品促销名称")
    private String promotionName;

    @ApiModelProperty(value = "是否虚拟商品")
    private Boolean whetherVirtually;

    @ApiModelProperty(value = "每个SKU销售价格")
    private BigDecimal perProductPrice;

    @ApiModelProperty(value = "每个SKU促销分解金额")
    private BigDecimal perPromotionAmount;

    @ApiModelProperty(value = "每个SKU优惠券优惠分解金额")
    private BigDecimal perCouponAmount;

    @ApiModelProperty(value = "每个SKU付款金额")
    private BigDecimal perRealAmount;

    private Integer perGiftPoint;

    private Integer perGiftGrowth;

    @ApiModelProperty(value = "商品销售属性:[{'key':'颜色','value':'颜色'},{'key':'容量','value':'4G'}]")
    private String productAttr;

    @ApiModelProperty(value = "分销总金额")
    private BigDecimal divideTotalAmount;

    private String divideDetailJson;

    private String deviceUidJson;

    private String divideReferralCode;

    @TableField(exist = false)
    private DivideDetailJsonClass divideDetailJsonClass;

    @TableField(exist = false)
    @ApiModelProperty(value = "已发货的数量")
    private Integer deliveriedProductQuantity;

    @TableField(exist = false)
    @ApiModelProperty(value = "待发货的数量")
    private Integer remainDeliveryQuantity;

    @TableField(exist = false)
    @ApiModelProperty(value = "本次发货的数量")
    private Integer currentDeliveryQuantity;

    @TableField(exist = false)
    @ApiModelProperty(value = "本次发货物流编号")
    private String currentDeliverySn;

    @TableField(exist = false)
    private String deviceProfileUrl;

}
