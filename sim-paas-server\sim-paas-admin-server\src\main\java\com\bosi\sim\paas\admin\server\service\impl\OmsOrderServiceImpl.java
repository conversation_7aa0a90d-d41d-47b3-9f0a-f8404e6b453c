package com.bosi.sim.paas.admin.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.BizCode;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.api.SystemCode;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.dao.enums.sds.SdsOrderDeliveryStatusEnum;
import com.bosi.sim.paas.dao.enums.sds.SdsOrderStatusEnum;
import com.bosi.sim.paas.dao.mapper.oms.OmsOrderDeliveryMapper;
import com.bosi.sim.paas.dao.mapper.oms.OmsOrderItemMapper;
import com.bosi.sim.paas.dao.mapper.oms.OmsOrderMapper;
import com.bosi.sim.paas.dao.model.sds.OmsOrder;
import com.bosi.sim.paas.dao.model.sds.OmsOrderDelivery;
import com.bosi.sim.paas.dao.model.sds.OmsOrderItem;
import com.bosi.sim.paas.dao.model.sds.jsonclz.DivideDetailJsonClass;
import com.bosi.sim.paas.dao.model.sds.jsonclz.ReceiverDetailJsonClass;
import com.bosi.sim.paas.admin.server.dao.OmsOrderDeliveryDao;
import com.bosi.sim.paas.admin.server.service.OmsOrderService;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 订单管理Service实现类
 */
@Service
public class OmsOrderServiceImpl implements OmsOrderService {
    @Autowired
    private OmsOrderMapper orderMapper;

    @Autowired
    private OmsOrderItemMapper orderItemMapper;

    @Autowired
    private OmsOrderDeliveryMapper orderDeliveryMapper;

    @Autowired
    private OmsOrderDeliveryDao orderDeliveryDao;

    @Override
    public CommonPage<OmsOrder> page(Page<OmsOrder> ipage, OmsOrder order) {
        LambdaQueryWrapper<OmsOrder> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(order.getOrderSn())) {
            queryWrapper.eq(OmsOrder::getOrderSn, order.getOrderSn());
        }
        if (StringUtils.isNotNull(order.getStatus())) {
            queryWrapper.eq(OmsOrder::getStatus, order.getStatus());
        }
        if (StringUtils.isNotNull(order.getSourceType())) {
            queryWrapper.eq(OmsOrder::getSourceType, order.getSourceType());
        }
        if (StringUtils.isNotEmpty(order.getQueryBeginTime())) {
            queryWrapper.ge(OmsOrder::getCreateTime, order.getQueryBeginTime());
        }
        if (StringUtils.isNotEmpty(order.getQueryEndTime())) {
            queryWrapper.le(OmsOrder::getCreateTime, order.getQueryEndTime());
        }
        IPage<OmsOrder> page = orderMapper.selectPage(ipage, queryWrapper);
        page.getRecords().forEach(omsOrder -> {
            if (StringUtils.isNotEmpty(omsOrder.getReceiverDetailJson())) {
                omsOrder.setReceiverDetailJsonClass(JSON.parseObject(omsOrder.getReceiverDetailJson(), ReceiverDetailJsonClass.class));
            }
        });
        return CommonPage.restPage(page);
    }

    @Override
    @Transactional
    public void delivery(OmsOrderItem omsOrderItem) {
        OmsOrder orderDetail = orderMapper.selectById(omsOrderItem.getOrderId());
        if (orderDetail == null) {
            throw BizException.build(SystemCode.RESOURCE_NOTFOUND);
        }
        if (!orderDetail.getStatus().equals(SdsOrderStatusEnum.WAITDELIVERY.getOrderStatus())) {
            throw BizException.build(BizCode.ORDER_STATUS_ERROR);
        }
        OmsOrderItem orderItem = orderItemMapper.selectById(omsOrderItem.getId());
        OmsOrderDelivery omsOrderDelivery = new OmsOrderDelivery();
        omsOrderDelivery.setProductSkuId(orderItem.getProductSkuId());
        omsOrderDelivery.setProductId(orderItem.getProductId());
        omsOrderDelivery.setOrderId(orderItem.getOrderId());
        omsOrderDelivery.setOrderItemId(orderItem.getId());
        omsOrderDelivery.setDeliveryNumber(omsOrderItem.getCurrentDeliveryQuantity());
        omsOrderDelivery.setDeliverySn(omsOrderItem.getCurrentDeliverySn());
        omsOrderDelivery.setDeliveryStatus(SdsOrderDeliveryStatusEnum.DELIVERED.getOrderDeliveryStatus());
        orderDeliveryMapper.insert(omsOrderDelivery);
        LambdaQueryWrapper<OmsOrderItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OmsOrderItem::getOrderId, omsOrderItem.getOrderId());
        List<OmsOrderItem> orderItemList = orderItemMapper.selectList(queryWrapper);
        if (isAllDelivered(orderDetail, orderItemList)) {
            orderDetail.setStatus(SdsOrderStatusEnum.DELIVERYED.getOrderStatus());
            orderDetail.setDeliveryTime(new Date());
            orderMapper.updateById(orderDetail);
        }
    }


    private boolean isAllDelivered(OmsOrder orderDetail, List<OmsOrderItem> orderItemList) {
        LambdaQueryWrapper<OmsOrderDelivery> deliveryLambdaQueryWrapper = new LambdaQueryWrapper<>();
        deliveryLambdaQueryWrapper.eq(OmsOrderDelivery::getOrderId, orderDetail.getId());
        List<OmsOrderDelivery> orderDeliveryList = orderDeliveryMapper.selectList(deliveryLambdaQueryWrapper);
        Map<String, Integer> deliveryNumberMap = Maps.newHashMap();
        orderDeliveryList.forEach(omsOrderDelivery -> {
            Integer deliveryNumber = deliveryNumberMap.get(omsOrderDelivery.getOrderItemId());
            if (deliveryNumber == null) {
                deliveryNumber = 0;
            }
            deliveryNumberMap.put(omsOrderDelivery.getOrderItemId(), deliveryNumber + omsOrderDelivery.getDeliveryNumber());
        });
        for (OmsOrderItem omsOrderItem : orderItemList) {
            Integer realDelivery = deliveryNumberMap.get(omsOrderItem.getId());
            if (realDelivery == null || realDelivery < omsOrderItem.getProductQuantity()) {
                return false;
            }
        }
        return true;
    }

    @Override
    @Transactional
    public int close(OmsOrder omsOrder) {
        omsOrder.setStatus(SdsOrderStatusEnum.CLOSED.getOrderStatus());
        return orderMapper.updateById(omsOrder);
    }

    @Override
    public int delete(List<String> ids) {
        return orderMapper.deleteBatchIds(ids);
    }

    @Override
    public OmsOrder detail(String id) {
        OmsOrder omsOrder = orderMapper.selectById(id);
        if (omsOrder == null) {
            throw BizException.build(SystemCode.RESOURCE_NOTFOUND);
        }
        if (StringUtils.isNotEmpty(omsOrder.getReceiverDetailJson())) {
            omsOrder.setReceiverDetailJsonClass(JSON.parseObject(omsOrder.getReceiverDetailJson(), ReceiverDetailJsonClass.class));
        }
        LambdaQueryWrapper<OmsOrderItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OmsOrderItem::getOrderId, id);
        List<OmsOrderItem> orderItemList = orderItemMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(orderItemList)) {
            throw BizException.build(SystemCode.RESOURCE_NOTFOUND);
        }

        List<OmsOrderDelivery> deliveryList = orderDeliveryDao.listByOrderId(id);
        Map<String, Integer> deliveryNumberMap = Maps.newHashMap();
        deliveryList.forEach(omsOrderDelivery -> {
            Integer deliveryNumber = deliveryNumberMap.get(omsOrderDelivery.getOrderItemId());
            if (deliveryNumber == null) {
                deliveryNumber = 0;
            }
            deliveryNumberMap.put(omsOrderDelivery.getOrderItemId(), deliveryNumber + omsOrderDelivery.getDeliveryNumber());
        });
        for (OmsOrderItem omsOrderItem : orderItemList) {
            if (StringUtils.isNotEmpty(omsOrderItem.getDivideDetailJson())) {
                omsOrderItem.setDivideDetailJsonClass(JSON.parseObject(omsOrderItem.getDivideDetailJson(), DivideDetailJsonClass.class));
            }
            Integer realDelivery = deliveryNumberMap.get(omsOrderItem.getId());
            if (realDelivery == null) {
                realDelivery = 0;
            }
            omsOrderItem.setDeliveriedProductQuantity(realDelivery);
            omsOrderItem.setRemainDeliveryQuantity(omsOrderItem.getProductQuantity() - realDelivery);
        }
        omsOrder.setOrderDeliveryList(deliveryList);
        omsOrder.setOrderItemList(orderItemList);
        return omsOrder;
    }

    @Override
    @Transactional
    public int updateReceiverInfo(OmsOrder order) {
        return orderMapper.updateById(order);
    }

    @Override
    @Transactional
    public int updateNote(OmsOrder order) {
        return orderMapper.updateById(order);
    }

}
