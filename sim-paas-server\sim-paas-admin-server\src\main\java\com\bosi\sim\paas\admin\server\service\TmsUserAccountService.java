package com.bosi.sim.paas.admin.server.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.dao.model.tds.TdsTenantAccount;

import java.util.List;

public interface TmsUserAccountService {

    CommonPage<TdsTenantAccount> page(Page<TdsTenantAccount> page, TdsTenantAccount userAccount);

    int updateStatus(TdsTenantAccount userAccount);

    List<TdsTenantAccount> listAccountByDistributorId(String distributorId);

}
