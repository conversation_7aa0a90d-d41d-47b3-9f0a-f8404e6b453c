package com.bosi.sim.paas.admin.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.BizCode;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.api.SystemCode;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.IdUtils;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.common.core.utils.UUID;
import com.bosi.sim.paas.common.security.auth.CurrentAuthorization;
import com.bosi.sim.paas.dao.enums.sds.SdsProductOperateTypeEnum;
import com.bosi.sim.paas.dao.enums.sds.SdsPromotionTypeEnum;
import com.bosi.sim.paas.dao.mapper.oms.OmsCartItemMapper;
import com.bosi.sim.paas.dao.mapper.pms.PmsProductAttributeTypeMapper;
import com.bosi.sim.paas.dao.mapper.pms.PmsProductFullReductionMapper;
import com.bosi.sim.paas.dao.mapper.pms.PmsProductLadderMapper;
import com.bosi.sim.paas.dao.mapper.pms.PmsProductMapper;
import com.bosi.sim.paas.dao.mapper.pms.PmsProductOperateLogMapper;
import com.bosi.sim.paas.dao.mapper.pms.PmsProductSkuMapper;
import com.bosi.sim.paas.dao.model.sds.OmsCartItem;
import com.bosi.sim.paas.dao.model.sds.PmsProduct;
import com.bosi.sim.paas.dao.model.sds.PmsProductAttributeType;
import com.bosi.sim.paas.dao.model.sds.PmsProductFullReduction;
import com.bosi.sim.paas.dao.model.sds.PmsProductLadder;
import com.bosi.sim.paas.dao.model.sds.PmsProductOperateLog;
import com.bosi.sim.paas.dao.model.sds.PmsProductSku;
import com.bosi.sim.paas.admin.server.dao.PmsProductAttributeTypeDao;
import com.bosi.sim.paas.admin.server.dao.PmsProductDao;
import com.bosi.sim.paas.admin.server.dao.PmsProductFullReductionDao;
import com.bosi.sim.paas.admin.server.dao.PmsProductLadderDao;
import com.bosi.sim.paas.admin.server.dao.PmsProductOperateLogDao;
import com.bosi.sim.paas.admin.server.dao.PmsProductSkuDao;
import com.bosi.sim.paas.admin.server.domain.PmsProductOperateLogCreate;
import com.bosi.sim.paas.admin.server.service.PmsProductService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商品管理Service实现类
 */
@Service
public class PmsProductServiceImpl implements PmsProductService {
    @Autowired
    private PmsProductMapper productMapper;
    @Autowired
    private PmsProductDao productDao;
    @Autowired
    private PmsProductLadderMapper productLadderMapper;
    @Autowired
    private PmsProductLadderDao productLadderDao;
    @Autowired
    private PmsProductFullReductionMapper productFullReductionMapper;
    @Autowired
    private PmsProductFullReductionDao productFullReductionDao;
    @Autowired
    private PmsProductAttributeTypeDao productAttributeTypeDao;
    @Autowired
    private PmsProductAttributeTypeMapper productAttributeTypeMapper;
    @Autowired
    private PmsProductSkuMapper productSkuMapper;
    @Autowired
    private PmsProductSkuDao productSkuDao;
    @Autowired
    private OmsCartItemMapper omsCartItemMapper;

    @Autowired
    private PmsProductOperateLogMapper pmsProductOperateLogMapper;
    @Autowired
    private PmsProductOperateLogDao pmsProductOperateLogDao;

    @Override
    @Transactional
    public int create(PmsProduct product) {
        int count;
        //创建商品
        if (StringUtils.isEmpty(product.getProductSn())) {
            product.setProductSn(RandomUtil.randomNumbers(12));
        }
        product.setWhetherPublish(false);
        productMapper.insert(product);
        //根据促销类型设置价格：会员价格、阶梯价格、满减价格
        String productId = product.getId();

        if (product.getPromotionType().equals(SdsPromotionTypeEnum.DISCOUNT.getPromotionType())) {
            //阶梯价格
            if (CollUtil.isNotEmpty(product.getProductLadderList())) {
                product.getProductLadderList().forEach(pmsProductLadder -> {
                    pmsProductLadder.setId(IdUtils.fastSimpleUUID());
                    pmsProductLadder.setProductId(productId);
                });
                productLadderDao.insertList(product.getProductLadderList());
            }
        } else if (product.getPromotionType().equals(SdsPromotionTypeEnum.FULL_REDUCTION.getPromotionType())) {
            //满减价格
            if (CollUtil.isNotEmpty(product.getProductFullReductionList())) {
                product.getProductFullReductionList().forEach(pmsProductFullReduction -> {
                    pmsProductFullReduction.setId(IdUtils.fastSimpleUUID());
                    pmsProductFullReduction.setProductId(productId);
                });
                productFullReductionDao.insertList(product.getProductFullReductionList());
            }
        }
        if (CollectionUtil.isNotEmpty(product.getProductSkuList())) {
            //处理sku的编码
            handleSkuStockCode(product.getProductSkuList());
            //添加sku库存信息
            product.getProductSkuList().forEach(productSku -> {
                productSku.setId(IdUtils.fastSimpleUUID());
                productSku.setProductId(productId);
            });
            productSkuDao.insertList(product.getProductSkuList());
        }
        if (CollectionUtil.isNotEmpty(product.getProductAttributeTypeList())) {
            //添加商品参数,添加自定义商品规格
            product.getProductAttributeTypeList().forEach(productAttributeType -> {
                productAttributeType.setId(IdUtils.fastSimpleUUID());
                productAttributeType.setProductId(productId);
            });
            productAttributeTypeDao.insertList(product.getProductAttributeTypeList());
        }
        count = 1;
        return count;
    }

    private void handleSkuStockCode(List<PmsProductSku> skuStockList) {
        if (CollectionUtils.isEmpty(skuStockList)) {
            return;
        }
        for (PmsProductSku skuStock : skuStockList) {
            if (StrUtil.isEmpty(skuStock.getSkuCode())) {
                skuStock.setSkuCode(UUID.randomUUID().toString());
            }
        }
    }

    @Override
    public PmsProduct getUpdateInfoById(String id) {
        PmsProduct product = productMapper.selectById(id);
        if (product == null) {
            throw BizException.build(SystemCode.RESOURCE_NOTFOUND);
        }
        if (product.getPromotionType().equals(SdsPromotionTypeEnum.DISCOUNT.getPromotionType())) {
            LambdaQueryWrapper<PmsProductLadder> ladderQueryWrapper = new LambdaQueryWrapper<>();
            ladderQueryWrapper.eq(PmsProductLadder::getProductId, product.getId());
            List<PmsProductLadder> ladders = productLadderMapper.selectList(ladderQueryWrapper);
            product.setProductLadderList(ladders);
        } else if (product.getPromotionType().equals(SdsPromotionTypeEnum.FULL_REDUCTION.getPromotionType())) {
            LambdaQueryWrapper<PmsProductFullReduction> fullQueryWrapper = new LambdaQueryWrapper<>();
            fullQueryWrapper.eq(PmsProductFullReduction::getProductId, product.getId());
            List<PmsProductFullReduction> fullReductions = productFullReductionMapper.selectList(fullQueryWrapper);
            product.setProductFullReductionList(fullReductions);
        }
        LambdaQueryWrapper<PmsProductSku> skuQueryWrapper = new LambdaQueryWrapper<>();
        skuQueryWrapper.eq(PmsProductSku::getProductId, product.getId());
        List<PmsProductSku> skus = productSkuMapper.selectList(skuQueryWrapper);
        product.setProductSkuList(skus);

        LambdaQueryWrapper<PmsProductAttributeType> attributeTypeQueryWrapper = new LambdaQueryWrapper<>();
        attributeTypeQueryWrapper.eq(PmsProductAttributeType::getProductId, product.getId());
        List<PmsProductAttributeType> attributeTypes = productAttributeTypeMapper.selectList(attributeTypeQueryWrapper);
        product.setProductAttributeTypeList(attributeTypes);
        return product;
    }

    public List<PmsProduct> getUpdateInfoByIds(List<String> ids) {
        List<PmsProduct> products = productMapper.selectBatchIds(ids);
        if (CollUtil.isEmpty(products)) {
            throw BizException.build(SystemCode.RESOURCE_NOTFOUND);
        }
        Map<String, List<PmsProductLadder>> ladderMap = getLadderMapByProductIds(ids);
        Map<String, List<PmsProductFullReduction>> fullReductionMap = getFullMapByProductIds(ids);
        Map<String, List<PmsProductSku>> skuMap = getSkuMapByProductIds(ids);
        Map<String, List<PmsProductAttributeType>> attributeTypeMap = getAttributeTypeMapByProductIds(ids);
        products.forEach(product -> {
            product.setProductLadderList(ladderMap.get(product.getId()));
            product.setProductFullReductionList(fullReductionMap.get(product.getId()));
            product.setProductSkuList(skuMap.get(product.getId()));
            product.setProductAttributeTypeList(attributeTypeMap.get(product.getId()));
        });
        return products;
    }

    private Map<String, List<PmsProductLadder>> getLadderMapByProductIds(List<String> productIds) {
        Map<String, List<PmsProductLadder>> ladderMap = Maps.newHashMap();
        LambdaQueryWrapper<PmsProductLadder> ladderQueryWrapper = new LambdaQueryWrapper<>();
        ladderQueryWrapper.in(PmsProductLadder::getProductId, productIds);
        List<PmsProductLadder> ladders = productLadderMapper.selectList(ladderQueryWrapper);
        if (CollUtil.isNotEmpty(ladders)) {

            ladders.forEach(ladder -> {
                List<PmsProductLadder> ladderList = ladderMap.get(ladder.getProductId());
                if (CollUtil.isEmpty(ladderList)) {
                    ladderList = Lists.newArrayList();
                }
                ladderList.add(ladder);
                ladderMap.put(ladder.getProductId(), ladderList);
            });
        }
        return ladderMap;
    }

    private Map<String, List<PmsProductFullReduction>> getFullMapByProductIds(List<String> productIds) {
        Map<String, List<PmsProductFullReduction>> fullReductionMap = Maps.newHashMap();
        LambdaQueryWrapper<PmsProductFullReduction> fullQueryWrapper = new LambdaQueryWrapper<>();
        fullQueryWrapper.in(PmsProductFullReduction::getProductId, productIds);
        List<PmsProductFullReduction> fullReductions = productFullReductionMapper.selectList(fullQueryWrapper);
        if (CollUtil.isNotEmpty(fullReductions)) {
            fullReductions.forEach(fullReduction -> {
                List<PmsProductFullReduction> fullReductionList = fullReductionMap.get(fullReduction.getProductId());
                if (CollUtil.isEmpty(fullReductionList)) {
                    fullReductionList = Lists.newArrayList();
                }
                fullReductionList.add(fullReduction);
                fullReductionMap.put(fullReduction.getProductId(), fullReductionList);
            });
        }
        return fullReductionMap;
    }

    private Map<String, List<PmsProductSku>> getSkuMapByProductIds(List<String> productIds) {
        Map<String, List<PmsProductSku>> skuMap = Maps.newHashMap();
        LambdaQueryWrapper<PmsProductSku> skuQueryWrapper = new LambdaQueryWrapper<>();
        skuQueryWrapper.in(PmsProductSku::getProductId, productIds);
        List<PmsProductSku> skus = productSkuMapper.selectList(skuQueryWrapper);
        if (CollUtil.isNotEmpty(skus)) {
            skus.forEach(sku -> {
                List<PmsProductSku> skuList = skuMap.get(sku.getProductId());
                if (CollUtil.isEmpty(skuList)) {
                    skuList = Lists.newArrayList();
                }
                skuList.add(sku);
                skuMap.put(sku.getProductId(), skuList);
            });
        }
        return skuMap;
    }

    private Map<String, List<PmsProductAttributeType>> getAttributeTypeMapByProductIds(List<String> productIds) {
        Map<String, List<PmsProductAttributeType>> attributeTypeMap = Maps.newHashMap();
        LambdaQueryWrapper<PmsProductAttributeType> attributeTypeQueryWrapper = new LambdaQueryWrapper<>();
        attributeTypeQueryWrapper.in(PmsProductAttributeType::getProductId, productIds);
        List<PmsProductAttributeType> attributeTypes = productAttributeTypeMapper.selectList(attributeTypeQueryWrapper);
        if (CollUtil.isNotEmpty(attributeTypes)) {
            attributeTypes.forEach(attributeType -> {
                List<PmsProductAttributeType> attributeTypeList = attributeTypeMap.get(attributeType.getProductId());
                if (CollUtil.isEmpty(attributeTypeList)) {
                    attributeTypeList = Lists.newArrayList();
                }
                attributeTypeList.add(attributeType);
                attributeTypeMap.put(attributeType.getProductId(), attributeTypeList);
            });
        }
        return attributeTypeMap;
    }

    @Override
    @Transactional
    public int updateBase(PmsProduct product) {
        PmsProduct pmsProduct = this.getUpdateInfoById(product.getId());
        if (pmsProduct.getWhetherPublish()) {
            throw BizException.build(BizCode.PRODUCT_ALREADY_PUBLISH, pmsProduct.getProductName());
        }
        PmsProduct update = new PmsProduct();
        update.setId(product.getId());
        update.setBrandId(product.getBrandId());
        update.setCategoryId(product.getCategoryId());
        update.setProductName(product.getProductName());
        update.setProductSn(product.getProductSn());
        update.setSubTitle(product.getSubTitle());
        update.setPrice(product.getPrice());
        update.setOriginalPrice(product.getOriginalPrice());
        update.setPic(product.getPic());
        update.setDescription(product.getDescription());
        update.setSort(product.getSort());
        update.setUnit(product.getUnit());
        update.setKeywords(product.getKeywords());
        update.setNote(product.getNote());
        update.setAlbumPics(product.getAlbumPics());
        update.setDetailTitle(product.getDetailTitle());
        update.setDetailDesc(product.getDetailDesc());
        update.setDetailHtml(product.getDetailHtml());
        update.setDetailMobileHtml(product.getDetailMobileHtml());
        update.setWeight(product.getWeight());
        int count = productMapper.updateById(update);
        insertOperateLog(pmsProduct, SdsProductOperateTypeEnum.EDITBASE.getOperateType(), update);
        return count;
    }

    @Override
    @Transactional
    public int updateMarket(PmsProduct product) {
        PmsProduct pmsProduct = this.getUpdateInfoById(product.getId());
        if (pmsProduct.getWhetherPublish()) {
            throw BizException.build(BizCode.PRODUCT_ALREADY_PUBLISH, pmsProduct.getProductName());
        }
        productLadderMapper.delete(new LambdaQueryWrapper<PmsProductLadder>().eq(PmsProductLadder::getProductId, product.getId()));
        productFullReductionMapper.delete(new LambdaQueryWrapper<PmsProductFullReduction>().eq(PmsProductFullReduction::getProductId,
                product.getId()));
        if (product.getPromotionType().equals(SdsPromotionTypeEnum.DISCOUNT.getPromotionType())) {
            //阶梯价格
            if (CollUtil.isNotEmpty(product.getProductLadderList())) {
                product.getProductLadderList().forEach(productLadder -> {
                    productLadder.setId(IdUtils.fastSimpleUUID());
                    productLadder.setProductId(product.getId());
                });
                productLadderDao.insertList(product.getProductLadderList());
            }
        } else if (product.getPromotionType().equals(SdsPromotionTypeEnum.FULL_REDUCTION.getPromotionType())) {
            //满减价格
            if (CollUtil.isNotEmpty(product.getProductFullReductionList())) {
                product.getProductFullReductionList().forEach(pmsProductFullReduction -> {
                    pmsProductFullReduction.setId(IdUtils.fastSimpleUUID());
                    pmsProductFullReduction.setProductId(product.getId());
                });
                productFullReductionDao.insertList(product.getProductFullReductionList());
            }
        }
        PmsProduct update = new PmsProduct();
        update.setId(product.getId());
        update.setGiftGrowth(product.getGiftGrowth());
        update.setGiftPoint(product.getGiftPoint());
        update.setServiceIds(product.getServiceIds());
        update.setPromotionType(product.getPromotionType());
        update.setPromotionStartTime(product.getPromotionStartTime());
        update.setPromotionEndTime(product.getPromotionEndTime());
        update.setPromotionPerMemberLimit(product.getPromotionPerMemberLimit());
        int count = productMapper.updateById(update);
        insertOperateLog(pmsProduct, SdsProductOperateTypeEnum.EDITMARKET.getOperateType(), update);
        return count;
    }

    @Override
    @Transactional
    public int updateStock(PmsProduct product) {
        PmsProduct pmsProduct = this.getUpdateInfoById(product.getId());
        if (pmsProduct.getWhetherPublish()) {
            throw BizException.build(BizCode.PRODUCT_ALREADY_PUBLISH, pmsProduct.getProductName());
        }
        PmsProduct update = new PmsProduct();
        update.setId(pmsProduct.getId());
        update.setProductSkuList(product.getProductSkuList());
        //当前没有sku直接删除
        if (CollUtil.isEmpty(update.getProductSkuList())) {
            return productSkuMapper.delete(new LambdaQueryWrapper<PmsProductSku>().eq(PmsProductSku::getProductId, pmsProduct.getId()));
        } else {
            //获取初始sku信息
            List<PmsProductSku> oriStuList = pmsProduct.getProductSkuList();
            //获取新增sku信息
            List<PmsProductSku> insertSkuList = update.getProductSkuList().stream().filter(item -> item.getId() == null).collect(Collectors.toList());
            //获取需要更新的sku信息
            List<PmsProductSku> updateSkuList = update.getProductSkuList().stream().filter(item -> item.getId() != null).collect(Collectors.toList());
            List<String> updateSkuIds = updateSkuList.stream().map(PmsProductSku::getId).collect(Collectors.toList());
            //获取需要删除的sku信息
            List<PmsProductSku> removeSkuList = oriStuList.stream().filter(item -> !updateSkuIds.contains(item.getId())).collect(Collectors.toList());
            //新增sku
            if (CollUtil.isNotEmpty(insertSkuList)) {
                handleSkuStockCode(insertSkuList);
                insertSkuList.forEach(productSku -> {
                    productSku.setId(IdUtils.fastSimpleUUID());
                    productSku.setProductId(product.getId());
                });
                productSkuDao.insertList(insertSkuList);
            }
            //修改sku
            if (CollUtil.isNotEmpty(updateSkuList)) {
                handleSkuStockCode(updateSkuList);
                productSkuDao.updateList(updateSkuList);
            }
            //删除sku
            if (CollUtil.isNotEmpty(removeSkuList)) {
                List<String> removeSkuIds = removeSkuList.stream().map(PmsProductSku::getId).collect(Collectors.toList());
                productSkuMapper.deleteBatchIds(removeSkuIds);
            }
            insertOperateLog(pmsProduct, SdsProductOperateTypeEnum.EDITSTOCK.getOperateType(), update);
            return 1;
        }
    }

    @Override
    public CommonPage<PmsProduct> page(Page<PmsProduct> page, PmsProduct product) {
        return CommonPage.restPage(productDao.page(page, product));
    }

    @Override
    @Transactional
    public int updateBatchPublish(String[] ids) {
        List<String> idList = Arrays.asList(ids);
        List<PmsProduct> pmsProducts = this.getUpdateInfoByIds(idList);
        List<PmsProductOperateLogCreate> createList = Lists.newArrayList();
        List<String> updateIds = Lists.newArrayList();
        PmsProduct update = new PmsProduct();
        update.setWhetherPublish(true);
        pmsProducts.forEach(pmsProduct -> {
            if (pmsProduct.getWhetherPublish()) {
                throw BizException.build(BizCode.PRODUCT_ALREADY_PUBLISH, pmsProduct.getProductName());
            }
            verifyBaseInfo(pmsProduct);
            verifyMarketInfo(pmsProduct);
            verifyStockInfo(pmsProduct);
            updateIds.add(pmsProduct.getId());
            PmsProductOperateLogCreate create = new PmsProductOperateLogCreate();
            create.setOld(pmsProduct);
            create.setOperateType(SdsProductOperateTypeEnum.PUBLISH.getOperateType());
            create.setEdit(update);
            createList.add(create);
        });
        LambdaQueryWrapper<PmsProduct> updateWrapper = new LambdaQueryWrapper<>();
        updateWrapper.in(PmsProduct::getId, updateIds);
        productMapper.update(update, updateWrapper);
        insertOperateLogList(createList);
        return updateIds.size();
    }


    @Override
    @Transactional
    public int updateBatchUnpublish(String[] ids) {
        List<String> idList = Arrays.asList(ids);
        List<PmsProduct> pmsProducts = this.getUpdateInfoByIds(idList);
        List<PmsProductOperateLogCreate> createList = Lists.newArrayList();
        List<String> updateIds = Lists.newArrayList();
        PmsProduct update = new PmsProduct();
        update.setWhetherPublish(false);
        pmsProducts.forEach(pmsProduct -> {
            if (!pmsProduct.getWhetherPublish()) {
                throw BizException.build(BizCode.PRODUCT_ALREADY_UNPUBLISH, pmsProduct.getProductName());
            }
            updateIds.add(pmsProduct.getId());
            PmsProductOperateLogCreate create = new PmsProductOperateLogCreate();
            create.setOld(pmsProduct);
            create.setOperateType(SdsProductOperateTypeEnum.UNPUBLISH.getOperateType());
            create.setEdit(update);
            createList.add(create);
        });
        LambdaQueryWrapper<PmsProduct> updateWrapper = new LambdaQueryWrapper<>();
        updateWrapper.in(PmsProduct::getId, updateIds);
        productMapper.update(update, updateWrapper);
        insertOperateLogList(createList);
        return updateIds.size();
    }

    @Override
    @Transactional
    public int deleteByIds(String[] ids) {
        List<String> idList = Arrays.asList(ids);
        List<PmsProduct> pmsProducts = this.getUpdateInfoByIds(idList);
        List<PmsProductOperateLogCreate> createList = Lists.newArrayList();
        List<String> deleteIds = Lists.newArrayList();
        pmsProducts.forEach(pmsProduct -> {
            if (pmsProduct.getWhetherPublish()) {
                throw BizException.build(BizCode.PRODUCT_ALREADY_PUBLISH, pmsProduct.getProductName());
            }
            deleteIds.add(pmsProduct.getId());
            PmsProductOperateLogCreate create = new PmsProductOperateLogCreate();
            create.setOld(pmsProduct);
            create.setOperateType(SdsProductOperateTypeEnum.UNPUBLISH.getOperateType());
            createList.add(create);
        });
        LambdaQueryWrapper<OmsCartItem> cartItemLambdaWrapper = new LambdaQueryWrapper<>();
        cartItemLambdaWrapper.in(OmsCartItem::getProductId, deleteIds);
        omsCartItemMapper.delete(cartItemLambdaWrapper);
        productMapper.deleteBatchIds(deleteIds);
        insertOperateLogList(createList);
        return deleteIds.size();
    }

    @Override
    public List<PmsProduct> list(PmsProduct product) {
        LambdaQueryWrapper<PmsProduct> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(product.getQuerySearchValue())) {
            lambdaQueryWrapper.like(PmsProduct::getProductName, product.getQuerySearchValue());
            lambdaQueryWrapper.or();
            lambdaQueryWrapper.like(PmsProduct::getProductSn, product.getQuerySearchValue());
        }
        return productMapper.selectList(lambdaQueryWrapper);
    }

    private void verifyBaseInfo(PmsProduct pmsProduct) {
        if (StringUtils.isNull(pmsProduct.getBrandId())) {
            throw BizException.build(BizCode.PRODUCT_IMPROVE_BRAND, pmsProduct.getProductName());
        }
        if (StringUtils.isNull(pmsProduct.getCategoryId())) {
            throw BizException.build(BizCode.PRODUCT_IMPROVE_CATEGORY, pmsProduct.getProductName());
        }
        if (StringUtils.isEmpty(pmsProduct.getProductName())) {
            throw BizException.build(BizCode.PRODUCT_IMPROVE_PRODUCT_NAME, pmsProduct.getProductName());
        }
        if (StringUtils.isEmpty(pmsProduct.getAttributeId())) {
            throw BizException.build(BizCode.PRODUCT_IMPROVE_ATTRIBUTE, pmsProduct.getProductName());
        }
        if (StringUtils.isEmpty(pmsProduct.getProductSn())) {
            throw BizException.build(BizCode.PRODUCT_IMPROVE_PRODUCT_SN, pmsProduct.getProductName());
        }
        if (StringUtils.isEmpty(pmsProduct.getSubTitle())) {
            throw BizException.build(BizCode.PRODUCT_IMPROVE_SUB_NAME, pmsProduct.getProductName());
        }
        if (StringUtils.isNull(pmsProduct.getPrice()) || pmsProduct.getPrice().compareTo(new BigDecimal(0)) <= 0) {
            throw BizException.build(BizCode.PRODUCT_SALE_PRICE_MUST_MORE_ZERO, pmsProduct.getProductName());
        }
        if (StringUtils.isNull(pmsProduct.getOriginalPrice()) || pmsProduct.getOriginalPrice().compareTo(new BigDecimal(0)) <= 0) {
            throw BizException.build(BizCode.PRODUCT_ORIGINAL_PRICE_MUST_MORE_ZERO, pmsProduct.getProductName());
        }
    }

    private void verifyMarketInfo(PmsProduct pmsProduct) {
        if (pmsProduct.getGiftGrowth() != null && pmsProduct.getGiftGrowth() < 0) {
            throw BizException.build(BizCode.PRODUCT_GIFT_GROWTH_CANNOT_LESS_ZERO, pmsProduct.getProductName());
        }
        if (pmsProduct.getGiftPoint() != null && pmsProduct.getGiftPoint() < 0) {
            throw BizException.build(BizCode.PRODUCT_GIFT_POINT_CANNOT_LESS_ZERO, pmsProduct.getProductName());
        }
        if (pmsProduct.getPromotionType().equals(SdsPromotionTypeEnum.DISCOUNT.getPromotionType())) {
            List<PmsProductLadder> productLadderList = pmsProduct.getProductLadderList();
            if (CollUtil.isEmpty(productLadderList)) {
                throw BizException.build(BizCode.PRODUCT_LADDER_CONFIG_CANNOT_EMPTY, pmsProduct.getProductName());
            }
            productLadderList.forEach(pmsProductLadder -> {
                if (pmsProductLadder.getCount() <= 0) {
                    throw BizException.build(BizCode.PRODUCT_LADDER_COUNT_MUST_MORE_ZERO, pmsProduct.getProductName());
                }
                if (pmsProductLadder.getDiscount().compareTo(new BigDecimal(0)) <= 0) {
                    throw BizException.build(BizCode.PRODUCT_LADDER_RATE_MUST_MORE_ZERO, pmsProduct.getProductName());
                }
                if (pmsProductLadder.getDiscount().compareTo(new BigDecimal(10)) >= 0) {
                    throw BizException.build(BizCode.PRODUCT_LADDER_RATE_MUST_LESS_TEN, pmsProduct.getProductName());
                }
            });
        } else if (pmsProduct.getPromotionType().equals(SdsPromotionTypeEnum.FULL_REDUCTION.getPromotionType())) {
            List<PmsProductFullReduction> productFullReductionList = pmsProduct.getProductFullReductionList();
            if (CollUtil.isEmpty(productFullReductionList)) {
                throw BizException.build(BizCode.PRODUCT_FULL_CONFIG_CANNOT_EMPTY, pmsProduct.getProductName());
            }
            productFullReductionList.forEach(pmsProductFullReduction -> {
                if (pmsProductFullReduction.getFullPrice().compareTo(new BigDecimal(0)) <= 0) {
                    throw BizException.build(BizCode.PRODUCT_FULL_REDUCTION_MUST_MORE_ZERO, pmsProduct.getProductName());
                }
                if (pmsProductFullReduction.getReducePrice().compareTo(new BigDecimal(0)) <= 0) {
                    throw BizException.build(BizCode.PRODUCT_FULL_RATE_MUST_MORE_ZERO, pmsProduct.getProductName());
                }
            });
        }
    }

    private void verifyStockInfo(PmsProduct pmsProduct) {
        List<PmsProductSku> oriStuList = pmsProduct.getProductSkuList();
        if (CollUtil.isEmpty(oriStuList)) {
            throw BizException.build(BizCode.PRODUCT_IMPROVE_SKU, pmsProduct.getProductName());
        }
        oriStuList.forEach(pmsProductSku -> {
            if (StringUtils.isEmpty(pmsProductSku.getSkuCode())) {
                throw BizException.build(BizCode.PRODUCT_IMPROVE_SKU_CODE, pmsProduct.getProductName());
            }
            if (StringUtils.isEmpty(pmsProductSku.getSkuName())) {
                throw BizException.build(BizCode.PRODUCT_IMPROVE_SKU_NAME, pmsProduct.getProductName());
            }
            if (StringUtils.isEmpty(pmsProductSku.getSpData())) {
                throw BizException.build(BizCode.PRODUCT_IMPROVE_SKU_PROPERTY, pmsProduct.getProductName());
            }
            if (StringUtils.isNull(pmsProductSku.getStock())) {
                throw BizException.build(BizCode.PRODUCT_IMPROVE_SKU_STOCK, pmsProduct.getProductName());
            }
            if (pmsProductSku.getStock() < 0) {
                throw BizException.build(BizCode.PRODUCT_SKU_STOCK_CANNOT_LESS_ZERO, pmsProduct.getProductName());
            }
            if (StringUtils.isNull(pmsProductSku.getPrice())) {
                throw BizException.build(BizCode.PRODUCT_IMPROVE_SKU_SALE_PRICE, pmsProduct.getProductName());
            }
            if (pmsProductSku.getPrice().compareTo(new BigDecimal(0)) <= 0) {
                throw BizException.build(BizCode.PRODUCT_SKU_SALE_PRICE_MUST_MORE_ZERO, pmsProduct.getProductName());
            }
            if (StringUtils.isNull(pmsProductSku.getOriginalPrice())) {
                throw BizException.build(BizCode.PRODUCT_IMPROVE_SKU_ORIGINAL_PRICE, pmsProduct.getProductName());
            }
            if (pmsProductSku.getOriginalPrice().compareTo(new BigDecimal(0)) <= 0) {
                throw BizException.build(BizCode.PRODUCT_SKU_ORIGINAL_PRICE_MUST_MORE_ZERO, pmsProduct.getProductName());
            }
        });
    }

    private void insertOperateLog(PmsProduct old, Integer operateType, PmsProduct edit) {
        PmsProductOperateLog pmsProductOperateLog = new PmsProductOperateLog();
        pmsProductOperateLog.setProductId(old.getId());
        pmsProductOperateLog.setOperateType(operateType);
        pmsProductOperateLog.setOldProductInfo(JSON.toJSONString(old));
        pmsProductOperateLog.setEditProductInfo(edit == null ? null : JSON.toJSONString(edit));
        pmsProductOperateLog.setOperateMan(CurrentAuthorization.getUsername());
        pmsProductOperateLogMapper.insert(pmsProductOperateLog);
    }

    private void insertOperateLogList(List<PmsProductOperateLogCreate> createList) {
        List<PmsProductOperateLog> pmsProductOperateLogList = Lists.newArrayList();
        createList.forEach(create -> {
            PmsProductOperateLog pmsProductOperateLog = new PmsProductOperateLog();
            pmsProductOperateLog.setId(IdUtils.fastSimpleUUID());
            pmsProductOperateLog.setProductId(create.getOld().getId());
            pmsProductOperateLog.setOperateType(create.getOperateType());
            pmsProductOperateLog.setOldProductInfo(JSON.toJSONString(create.getOld()));
            pmsProductOperateLog.setEditProductInfo(create.getEdit() == null ? null : JSON.toJSONString(create.getEdit()));
            pmsProductOperateLog.setOperateMan(CurrentAuthorization.getUsername());
            pmsProductOperateLogList.add(pmsProductOperateLog);
        });
        pmsProductOperateLogDao.insertList(pmsProductOperateLogList);
    }


}
