package com.bosi.sim.paas.admin.server.controller;

import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.exception.CommonResult;
import com.bosi.sim.paas.common.core.utils.PageUtil;
import com.bosi.sim.paas.common.optlog.annotation.OperateLog;
import com.bosi.sim.paas.dao.model.sds.SdsMember;
import com.bosi.sim.paas.dao.model.sds.SdsMemberGrowthHistory;
import com.bosi.sim.paas.dao.model.sds.SdsMemberPointHistory;
import com.bosi.sim.paas.dao.model.sds.SdsMemberReceiveAddress;
import com.bosi.sim.paas.dao.model.sds.SmsCouponHistory;
import com.bosi.sim.paas.admin.server.service.MmsMemberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 会员管理Controller
 */
@RestController
@Api(tags = "MmsMemberController", value = "会员管理")
@RequestMapping("/mms/member")
public class MmsMemberController {
    @Autowired
    private MmsMemberService memberService;

    @ApiOperation("分页查询会员")
    @GetMapping("/page")
    public CommonResult<CommonPage<SdsMember>> page(SdsMember member,
                                                    @RequestParam(value = "pageSize") Integer pageSize,
                                                    @RequestParam(value = "pageNum") Integer pageNum) {
        CommonPage<SdsMember> commonPage = memberService.page(PageUtil.buildPage(pageNum, pageSize), member);
        return CommonResult.success(commonPage);
    }

    @ApiOperation("获取会员详情")
    @GetMapping("/{id}")
    public CommonResult<SdsMember> detail(@PathVariable String id) {
        SdsMember member = memberService.detail(id);
        return CommonResult.success(member);
    }

    @ApiOperation("更新会员状态")
    @OperateLog("更新会员状态")
    @PostMapping("/updateStatus")
    public CommonResult<Void> updateStatus(@RequestBody SdsMember member) {
        memberService.updateStatus(member);
        return CommonResult.success();
    }

    @ApiOperation("分页查询会员积分历史")
    @GetMapping("/pagePoint")
    public CommonResult<CommonPage<SdsMemberPointHistory>> pagePoint(SdsMemberPointHistory memberPointHistory,
                                                                     @RequestParam(value = "pageSize") Integer pageSize,
                                                                     @RequestParam(value = "pageNum") Integer pageNum) {
        CommonPage<SdsMemberPointHistory> commonPage = memberService.pagePoint(PageUtil.buildPage(pageNum, pageSize), memberPointHistory);
        return CommonResult.success(commonPage);
    }

    @ApiOperation("分页查询会员成长历史")
    @GetMapping("/pageGrowth")
    public CommonResult<CommonPage<SdsMemberGrowthHistory>> pageGrowth(SdsMemberGrowthHistory memberGrowthHistory,
                                                                       @RequestParam(value = "pageSize") Integer pageSize,
                                                                       @RequestParam(value = "pageNum") Integer pageNum) {
        CommonPage<SdsMemberGrowthHistory> commonPage = memberService.pageGrowth(PageUtil.buildPage(pageNum, pageSize), memberGrowthHistory);
        return CommonResult.success(commonPage);
    }

    @ApiOperation("分页查询优惠券情况")
    @GetMapping("/pageCoupon")
    public CommonResult<CommonPage<SmsCouponHistory>> pageCoupon(SmsCouponHistory couponHistory,
                                                                 @RequestParam(value = "pageSize") Integer pageSize,
                                                                 @RequestParam(value = "pageNum") Integer pageNum) {
        CommonPage<SmsCouponHistory> commonPage = memberService.pageCoupon(PageUtil.buildPage(pageNum, pageSize), couponHistory);
        return CommonResult.success(commonPage);
    }

    @ApiOperation("根据会员ID查询收货地址")
    @GetMapping("/listAddressByMemberId")
    public CommonResult<List<SdsMemberReceiveAddress>> listAddressByMemberId(@RequestParam String memberId) {
        List<SdsMemberReceiveAddress> result = memberService.listAddressByMemberId(memberId);
        return CommonResult.success(result);
    }
}
