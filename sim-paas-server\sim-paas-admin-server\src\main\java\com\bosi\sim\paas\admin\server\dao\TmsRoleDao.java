package com.bosi.sim.paas.admin.server.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.dao.model.tds.TdsRole;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 后台角色管理自定义Dao
 */
public interface TmsRoleDao {
    /**
     * 根据条件分页查询角色数据
     */
    Page<TdsRole> selectRoleList(Page<TdsRole> iPage, @Param("params") TdsRole umsRole);

    /**
     * 根据用户ID查询角色
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<TdsRole> selectRolePermissionByUserId(String userId);

    /**
     * 查询所有角色
     *
     * @return 角色列表
     */
    List<TdsRole> selectRoleAll();

    /**
     * 根据用户ID获取角色选择框列表
     *
     * @param userId 用户ID
     * @return 选中角色ID列表
     */
    Set<String> selectRoleListByUserId(String userId);

    /**
     * 通过角色ID查询角色
     *
     * @param roleId 角色ID
     * @return 角色对象信息
     */
    TdsRole selectRoleById(String roleId);

    /**
     * 根据用户ID查询角色
     *
     * @param userName 用户名
     * @return 角色列表
     */
    List<TdsRole> selectRolesByUserName(String userName);

}
