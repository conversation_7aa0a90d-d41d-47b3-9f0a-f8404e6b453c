package com.bosi.sim.paas.open.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bosi.sim.paas.common.security.auth.CurrentAuthorization;
import com.bosi.sim.paas.dao.mapper.mms.MmsMemberReceiveAddressMapper;
import com.bosi.sim.paas.dao.model.sds.SdsMemberReceiveAddress;
import com.bosi.sim.paas.open.server.handler.OrderHandler;
import com.bosi.sim.paas.open.server.service.MmsMemberReceiveAddressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户地址管理Service实现类
 */
@Service
public class MmsMemberReceiveAddressServiceImpl implements MmsMemberReceiveAddressService {
    @Autowired
    private MmsMemberReceiveAddressMapper addressMapper;

    @Autowired
    private OrderHandler orderHandler;

    @Override
    public int add(SdsMemberReceiveAddress address) {
        address.setMemberId(CurrentAuthorization.getUserId());
        return addressMapper.insert(address);
    }

    @Override
    public int delete(String id) {
        LambdaQueryWrapper<SdsMemberReceiveAddress> example = new LambdaQueryWrapper<>();
        example.eq(SdsMemberReceiveAddress::getMemberId, CurrentAuthorization.getUserId());
        example.eq(SdsMemberReceiveAddress::getId, id);
        return addressMapper.delete(example);
    }

    @Override
    public int update(SdsMemberReceiveAddress address) {
        if (address.getWhetherDefault() == null) {
            address.setWhetherDefault(false);
        }
        if (address.getWhetherDefault()) {
            //先将原来的默认地址去除
            SdsMemberReceiveAddress record = new SdsMemberReceiveAddress();
            record.setWhetherDefault(false);
            LambdaQueryWrapper<SdsMemberReceiveAddress> updateExample = new LambdaQueryWrapper<>();
            updateExample.eq(SdsMemberReceiveAddress::getMemberId, CurrentAuthorization.getUserId());
            updateExample.eq(SdsMemberReceiveAddress::getWhetherDefault, true);
            addressMapper.update(record, updateExample);
        }
        LambdaQueryWrapper<SdsMemberReceiveAddress> example = new LambdaQueryWrapper<>();
        example.eq(SdsMemberReceiveAddress::getMemberId, CurrentAuthorization.getUserId());
        example.eq(SdsMemberReceiveAddress::getId, address.getId());
        return addressMapper.update(address, example);
    }

    @Override
    public List<SdsMemberReceiveAddress> list() {
        return orderHandler.listMemberReceiveAddressByMemberId(CurrentAuthorization.getUserId());
    }

    @Override
    public SdsMemberReceiveAddress getItem(String id) {
        LambdaQueryWrapper<SdsMemberReceiveAddress> example = new LambdaQueryWrapper<>();
        example.eq(SdsMemberReceiveAddress::getMemberId, CurrentAuthorization.getUserId());
        example.eq(SdsMemberReceiveAddress::getId, id);
        return addressMapper.selectOne(example);
    }
}
