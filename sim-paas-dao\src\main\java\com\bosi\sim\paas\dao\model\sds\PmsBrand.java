package com.bosi.esim.mall.dao.model.pms;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.esim.mall.dao.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("pms_brand")
public class PmsBrand extends BaseEntity {
    private String name;

    @ApiModelProperty(value = "首字母")
    private String firstLetter;

    private Integer sort;

    @ApiModelProperty(value = "是否为品牌制造商：0->否；1->是")
    private Boolean whetherFactory;

    @ApiModelProperty(value = "品牌logo")
    private String logo;

    @ApiModelProperty(value = "专区大图")
    private String bigPic;

    @ApiModelProperty(value = "品牌故事")
    private String brandStory;

}
