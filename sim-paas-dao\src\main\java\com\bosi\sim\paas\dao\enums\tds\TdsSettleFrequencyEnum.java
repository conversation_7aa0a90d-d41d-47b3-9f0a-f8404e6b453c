package com.bosi.sim.paas.dao.enums.tds;


public enum TdsSettleFrequencyEnum {

    EVERY_DAY(1, "", "每日24点执行"),

    EVERY_WEEK(2, "", "每周一24点执行"),

    EVERY_MONTH(3, "", "每月月初0点执行");

    private Integer settleFrequency;

    private String cron;

    private String desc;

    TdsSettleFrequencyEnum(Integer settleFrequency, String cron, String desc) {
        this.settleFrequency = settleFrequency;
        this.cron = cron;
        this.desc = desc;
    }

    public Integer getSettleFrequency() {
        return settleFrequency;
    }

    public String getCron() {
        return cron;
    }

    public String getDesc() {
        return desc;
    }
}
