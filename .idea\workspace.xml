<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="aebc79c9-4a6e-4906-82ab-759103b33c04" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/sim-paas-web/.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sim-paas-web/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sim-paas-web/eslint.config.mjs" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sim-paas-web/next.config.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sim-paas-web/package-lock.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sim-paas-web/package.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sim-paas-web/postcss.config.mjs" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sim-paas-web/public/file.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sim-paas-web/public/globe.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sim-paas-web/public/next.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sim-paas-web/public/vercel.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sim-paas-web/public/window.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sim-paas-web/src/app/favicon.ico" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sim-paas-web/src/app/globals.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sim-paas-web/src/app/layout.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sim-paas-web/src/app/page.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sim-paas-web/tsconfig.json" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/sim-paas-web" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 5
}]]></component>
  <component name="ProjectId" id="2y5Hg2OWVR6WZG3bL0Rnppy2TYe" />
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.sim-paas-system [clean].executor": "Run",
    "Maven.sim-paas-system [package].executor": "Run",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "master",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "E:/code/bosi/sim-paas-system/sim-paas-server/sim-paas-open-server/src/main/java/com/bosi/sim/paas/open/server",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "run.configurations.included.in.services": "true",
    "settings.editor.selected.configurable": "MavenSettings",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="E:\code\bosi\sim-paas-system\sim-paas-server\sim-paas-open-server\src\main\java\com\bosi\sim\paas\open\server" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.SimPaasAdminServerApplication">
    <configuration name="DistributorServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="sim-paas-tenant-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bosi.esim.mall.distributor.server.DistributorServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SimPaasAdminServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="sim-paas-admin-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bosi.sim.paas.admin.server.SimPaasAdminServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SimPassOpenServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="sim-paas-open-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bosi.sim.paas.open.server.SimPassOpenServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SimPassTenantServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="sim-paas-tenant-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bosi.sim.paas.tenant.server.SimPassTenantServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="aebc79c9-4a6e-4906-82ab-759103b33c04" name="更改" comment="" />
      <created>1719280629015</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1719280629015</updated>
      <workItem from="1749117622575" duration="11072000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>