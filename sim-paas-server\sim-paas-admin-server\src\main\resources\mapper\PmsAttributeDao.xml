<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bosi.sim.paas.admin.server.dao.PmsAttributeDao">
    <resultMap id="getListWithSpecMap" type="com.bosi.sim.paas.dao.model.sds.PmsAttribute">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <collection property="attributeTypeList" columnPrefix="type_" resultMap="WithSpecMap"/>
    </resultMap>

    <resultMap id="WithSpecMap" type="com.bosi.sim.paas.dao.model.sds.PmsAttribute">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
    </resultMap>

    <select id="getListWithSpec" resultMap="getListWithSpecMap">
        select
            pa.id,
            pa.name,
            pat.id   type_id,
            pat.name type_name
        from
            sds_attribute pa
        left join
            sds_attribute_type pat on pa.id = pat.attribute_id
        where
            pat.classify = 1
        and
            pa.whether_delete = false
    </select>

    <select id="getWithSpecById" resultMap="getListWithSpecMap">
        select
            pa.id,
            pa.name,
            pat.id   type_id,
            pat.name type_name
        from
            sds_attribute pa
        left join
            sds_attribute_type pat on pa.id = pat.attribute_id
        where
            pat.classify = 1
        and
            pa.whether_delete = false
        and
            pa.id = #{id}
    </select>
</mapper>
