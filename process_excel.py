import pandas as pd
import os

def split_mccmnc_data():
    """
    读取test.xlsx文件，将MCCMNC列中包含'/'的数据分裂成多行
    """
    try:
        # 读取Excel文件
        input_file = 'doc/test.xlsx'
        df = pd.read_excel(input_file)
        
        print(f"原始数据行数: {len(df)}")
        print(f"列名: {list(df.columns)}")
        
        # 检查是否存在MCCMNC列
        if 'MCCMNC' not in df.columns:
            print("未找到MCCMNC列，请检查列名")
            print("可用的列名:", list(df.columns))
            return
        
        # 显示前几行数据以便确认
        print("\n前5行数据:")
        print(df.head())
        
        # 创建新的数据列表
        new_rows = []
        
        for index, row in df.iterrows():
            mccmnc_value = str(row['MCCMNC'])
            
            # 检查是否包含'/'
            if '/' in mccmnc_value:
                # 分割数据
                mccmnc_parts = mccmnc_value.split('/')
                
                # 为每个分割后的值创建新行
                for part in mccmnc_parts:
                    new_row = row.copy()
                    new_row['MCCMNC'] = part.strip()  # 去除可能的空格
                    new_rows.append(new_row)
                    
                print(f"行 {index}: '{mccmnc_value}' 分裂为 {len(mccmnc_parts)} 行")
            else:
                # 不包含'/'的行直接添加
                new_rows.append(row)
        
        # 创建新的DataFrame
        new_df = pd.DataFrame(new_rows)
        
        print(f"\n处理后数据行数: {len(new_df)}")
        
        # 保存到新文件
        output_file = 'doc/test1.xlsx'
        new_df.to_excel(output_file, index=False)
        
        print(f"数据已保存到: {output_file}")
        
        # 显示处理后的前几行数据
        print("\n处理后前10行数据:")
        print(new_df.head(10))
        
        return True
        
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        return False

if __name__ == "__main__":
    split_mccmnc_data()
