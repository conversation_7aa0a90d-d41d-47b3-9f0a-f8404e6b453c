package com.bosi.sim.paas.admin.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.BizCode;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.api.SystemCode;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.IdUtils;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.dao.mapper.ads.AdsRoleDeptMapper;
import com.bosi.sim.paas.dao.mapper.ads.AdsRoleMapper;
import com.bosi.sim.paas.dao.mapper.ads.AdsRoleMenuMapper;
import com.bosi.sim.paas.dao.mapper.ads.AdsUserRoleMapper;
import com.bosi.sim.paas.dao.model.ads.AdsRole;
import com.bosi.sim.paas.dao.model.ads.AdsRoleDept;
import com.bosi.sim.paas.dao.model.ads.AdsRoleMenu;
import com.bosi.sim.paas.dao.model.ads.AdsUser;
import com.bosi.sim.paas.dao.model.ads.AdsUserRole;
import com.bosi.sim.paas.admin.server.dao.UmsRoleDao;
import com.bosi.sim.paas.admin.server.dao.UmsRoleDeptDao;
import com.bosi.sim.paas.admin.server.dao.UmsRoleMenuDao;
import com.bosi.sim.paas.admin.server.dao.UmsUserRoleDao;
import com.bosi.sim.paas.admin.server.service.UmsRoleService;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 角色 业务层处理
 */
@Service
public class UmsRoleServiceImpl implements UmsRoleService {
    @Autowired
    private UmsRoleDao roleDao;

    @Autowired
    private AdsRoleMapper roleMapper;

    @Autowired
    private AdsRoleMenuMapper roleMenuMapper;

    @Autowired
    private UmsRoleMenuDao roleMenuDao;

    @Autowired
    private AdsUserRoleMapper userRoleMapper;

    @Autowired
    private UmsUserRoleDao userRoleDao;

    @Autowired
    private AdsRoleDeptMapper roleDeptMapper;

    @Autowired
    private UmsRoleDeptDao roleDeptDao;

    /**
     * 根据条件分页查询角色数据
     *
     * @param role 角色信息
     * @return 角色数据集合信息
     */
    @Override
    public CommonPage<AdsRole> page(Page<AdsRole> page, AdsRole role) {
        return CommonPage.restPage(roleDao.selectRoleList(page, role));
    }

    /**
     * 根据用户ID查询角色
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    @Override
    public List<AdsRole> selectRolesByUserId(String userId) {
        List<AdsRole> userRoles = roleDao.selectRolePermissionByUserId(userId);
        List<AdsRole> roles = selectRoleAll();
        for (AdsRole role : roles) {
            for (AdsRole userRole : userRoles) {
                if (role.getId().equals(userRole.getId())) {
                    role.setFlag(true);
                    break;
                }
            }
        }
        return roles;
    }

    /**
     * 获取角色数据权限
     */
    @Override
    public Set<String> getRolePermission(AdsUser user) {
        Set<String> roles = new HashSet<>();
        // 管理员拥有所有权限
        if (user.getWhetherAdmin()) {
            roles.add("admin");
        } else {
            roles.addAll(this.selectRolePermissionByUserId(user.getId()));
        }
        return roles;
    }

    private Set<String> selectRolePermissionByUserId(String userId) {
        List<AdsRole> perms = roleDao.selectRolePermissionByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (AdsRole perm : perms) {
            if (StringUtils.isNotNull(perm)) {
                permsSet.addAll(Arrays.asList(perm.getRoleKey().trim().split(",")));
            }
        }
        return permsSet;
    }

    /**
     * 查询所有角色
     *
     * @return 角色列表
     */
    @Override
    public List<AdsRole> selectRoleAll() {
        return roleMapper.selectList(new LambdaQueryWrapper<>());
    }

    /**
     * 通过角色ID查询角色
     *
     * @param roleId 角色ID
     * @return 角色对象信息
     */
    @Override
    public AdsRole selectRoleById(String roleId) {
        return roleMapper.selectById(roleId);
    }

    /**
     * 校验角色名称是否唯一
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    public boolean checkRoleNameUnique(AdsRole role) {
        String roleId = StringUtils.isNull(role.getId()) ? "" : role.getId();
        AdsRole info = roleDao.checkRoleNameUnique(role.getRoleName());
        return StringUtils.isNull(info) || info.getId().equals(roleId);
    }

    /**
     * 通过角色ID查询角色使用数量
     *
     * @param roleId 角色ID
     * @return 结果
     */
    @Override
    public long countUserRoleByRoleId(String roleId) {
        LambdaQueryWrapper<AdsUserRole> countWrapper = new LambdaQueryWrapper<>();
        countWrapper.eq(AdsUserRole::getRoleId, roleId);
        return userRoleMapper.selectCount(countWrapper);
    }

    /**
     * 新增保存角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertRole(AdsRole role) {
        // 新增角色信息
        roleMapper.insert(role);
        return insertRoleMenu(role);
    }

    /**
     * 修改保存角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateRole(AdsRole role) {
        AdsRole umsRole = roleMapper.selectById(role.getId());
        if (umsRole == null) {
            throw BizException.build(SystemCode.RESOURCE_NOTFOUND);
        }
        if (umsRole.getWhetherAdmin()) {
            throw BizException.build(SystemCode.FORBIDDEN_OPERATION);
        }
        // 修改角色信息
        roleMapper.updateById(role);
        // 删除角色与菜单关联
        LambdaQueryWrapper<AdsRoleMenu> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(AdsRoleMenu::getRoleId, role.getId());
        roleMenuMapper.delete(deleteWrapper);
        return insertRoleMenu(role);
    }

    @Override
    public int updateRoleStatus(AdsRole role) {
        AdsRole umsRole = roleMapper.selectById(role.getId());
        if (umsRole == null) {
            throw BizException.build(SystemCode.RESOURCE_NOTFOUND);
        }
        if (umsRole.getWhetherAdmin()) {
            throw BizException.build(SystemCode.FORBIDDEN_OPERATION);
        }
        return roleMapper.updateById(role);
    }

    /**
     * 修改数据权限信息
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int authDataScope(AdsRole role) {
        // 修改角色信息
        roleMapper.updateById(role);
        // 删除角色与部门关联
        LambdaQueryWrapper<AdsRoleDept> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(AdsRoleDept::getRoleId, role.getId());
        roleDeptMapper.delete(deleteWrapper);
        // 新增角色和部门信息（数据权限）
        return insertRoleDept(role);
    }

    /**
     * 新增角色菜单信息
     *
     * @param role 角色对象
     */
    public int insertRoleMenu(AdsRole role) {
        int rows = 1;
        // 新增用户与角色管理
        List<AdsRoleMenu> list = Lists.newArrayList();
        for (String menuId : role.getMenuIds()) {
            AdsRoleMenu rm = new AdsRoleMenu();
            rm.setId(IdUtils.fastSimpleUUID());
            rm.setRoleId(role.getId());
            rm.setMenuId(menuId);
            list.add(rm);
        }
        roleMenuDao.insertList(list);
        return rows;
    }

    /**
     * 新增角色部门信息(数据权限)
     *
     * @param role 角色对象
     */
    public int insertRoleDept(AdsRole role) {
        int rows = 1;
        // 新增角色与部门（数据权限）管理
        List<AdsRoleDept> list = Lists.newArrayList();
        for (String deptId : role.getDeptIds()) {
            AdsRoleDept rd = new AdsRoleDept();
            rd.setId(IdUtils.fastSimpleUUID());
            rd.setRoleId(role.getId());
            rd.setDeptId(deptId);
            list.add(rd);
        }
        roleDeptDao.insertList(list);
        return rows;
    }

    /**
     * 通过角色ID删除角色
     *
     * @param roleId 角色ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteRoleById(String roleId) {
        // 删除角色与菜单关联
        LambdaQueryWrapper<AdsRoleMenu> roleMenuDeleteWrapper = new LambdaQueryWrapper<>();
        roleMenuDeleteWrapper.eq(AdsRoleMenu::getRoleId, roleId);
        roleMenuMapper.delete(roleMenuDeleteWrapper);
        // 删除角色与部门关联
        LambdaQueryWrapper<AdsRoleDept> roleDeptDeleteWrapper = new LambdaQueryWrapper<>();
        roleDeptDeleteWrapper.eq(AdsRoleDept::getRoleId, roleId);
        roleDeptMapper.delete(roleDeptDeleteWrapper);
        return roleMapper.deleteById(roleId);
    }

    /**
     * 批量删除角色信息
     *
     * @param roleIds 需要删除的角色ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteRoleByIds(String[] roleIds) {
        LambdaQueryWrapper<AdsRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AdsRole::getId, Arrays.asList(roleIds));
        List<AdsRole> roles = roleMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(roles)) {
            return roleIds.length;
        }
        for (AdsRole role : roles) {
            if (role.getWhetherAdmin()) {
                throw BizException.build(SystemCode.FORBIDDEN_OPERATION);
            }
            if (countUserRoleByRoleId(role.getId()) > 0) {
                throw BizException.build(BizCode.ROLE_ALREADY_ALLOCATION);
            }
        }
        // 删除角色与部门关联
        LambdaQueryWrapper<AdsRoleMenu> roleMenuDeleteWrapper = new LambdaQueryWrapper<>();
        roleMenuDeleteWrapper.in(AdsRoleMenu::getRoleId, Arrays.asList(roleIds));
        roleMenuMapper.delete(roleMenuDeleteWrapper);
        // 删除角色与部门关联
        LambdaQueryWrapper<AdsRoleDept> roleDeptDeleteWrapper = new LambdaQueryWrapper<>();
        roleDeptDeleteWrapper.in(AdsRoleDept::getRoleId, Arrays.asList(roleIds));
        roleDeptMapper.delete(roleDeptDeleteWrapper);
        return roleMapper.deleteBatchIds(Arrays.asList(roleIds));
    }

    /**
     * 取消授权用户角色
     *
     * @param userRole 用户和角色关联信息
     * @return 结果
     */
    @Override
    public int deleteAuthUser(AdsUserRole userRole) {
        LambdaQueryWrapper<AdsUserRole> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(AdsUserRole::getRoleId, userRole.getRoleId());
        deleteWrapper.eq(AdsUserRole::getUserId, userRole.getUserId());
        return userRoleMapper.delete(deleteWrapper);
    }

    /**
     * 批量取消授权用户角色
     *
     * @param roleId  角色ID
     * @param userIds 需要取消授权的用户数据ID
     * @return 结果
     */
    @Override
    public int deleteAuthUsers(String roleId, String[] userIds) {
        LambdaQueryWrapper<AdsUserRole> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(AdsUserRole::getRoleId, roleId);
        deleteWrapper.in(AdsUserRole::getUserId, Arrays.asList(userIds));
        return userRoleMapper.delete(deleteWrapper);
    }

    /**
     * 批量选择授权用户角色
     *
     * @param roleId  角色ID
     * @param userIds 需要授权的用户数据ID
     * @return 结果
     */
    @Override
    public int insertAuthUsers(String roleId, String[] userIds) {
        // 新增用户与角色管理
        List<AdsUserRole> list = Lists.newArrayList();
        for (String userId : userIds) {
            AdsUserRole ur = new AdsUserRole();
            ur.setId(IdUtils.fastSimpleUUID());
            ur.setUserId(userId);
            ur.setRoleId(roleId);
            list.add(ur);
        }
        userRoleDao.insertList(list);
        return userIds.length;
    }
}
