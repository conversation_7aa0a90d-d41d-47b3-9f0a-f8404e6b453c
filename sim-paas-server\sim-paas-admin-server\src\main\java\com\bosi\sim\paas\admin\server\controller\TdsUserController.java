package com.bosi.sim.paas.admin.server.controller;

import com.alibaba.fastjson2.JSONObject;
import com.bosi.sim.paas.common.core.api.BizCode;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.exception.CommonResult;
import com.bosi.sim.paas.common.core.api.SystemCode;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.PageUtil;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.common.optlog.annotation.OperateLog;
import com.bosi.sim.paas.common.security.annotation.RequiresPermissions;
import com.bosi.sim.paas.common.security.util.PasswordUtils;
import com.bosi.sim.paas.dao.model.tds.TdsRole;
import com.bosi.sim.paas.dao.model.tds.TdsUser;
import com.bosi.sim.paas.admin.server.service.TdsRoleService;
import com.bosi.sim.paas.admin.server.service.TdsUserService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户信息
 */
@RestController
@Api(tags = "TdsUserController", value = "用户管理")
@RequestMapping("/tds/user")
public class TdsUserController {
    @Autowired
    private TdsUserService userService;

    @Autowired
    private TdsRoleService roleService;

    /**
     * 获取用户列表
     */
    @RequiresPermissions("tds:user:page")
    @GetMapping("/page")
    public CommonResult page(
            @RequestParam(value = "pageNum") Integer pageNum,
            @RequestParam(value = "pageSize") Integer pageSize,
            TdsUser user) {
        CommonPage<TdsUser> page = userService.page(PageUtil.buildPage(pageNum, pageSize, "whether_admin", false), user);
        return CommonResult.success(page);
    }

    /**
     * 根据用户编号获取详细信息
     */
    @RequiresPermissions("tds:user:query")
    @GetMapping({"/{id}"})
    public CommonResult getInfo(@PathVariable String id) {
        JSONObject jsonObject = new JSONObject();
        TdsUser tdsUser = userService.selectUserById(id);
        if (tdsUser == null) {
            throw BizException.build(SystemCode.RESOURCE_NOTFOUND);
        }
        TdsRole tdsRole = new TdsRole();
        tdsRole.setDistributorId(tdsUser.getDistributorId());
        tdsRole.setWhetherEnable(true);
        List<TdsRole> roles = roleService.list(tdsRole);
        jsonObject.put("roles", roles.stream().filter(r -> !r.getWhetherAdmin()).collect(Collectors.toList()));
        jsonObject.put("user", tdsUser);
        return CommonResult.success(jsonObject);
    }

    /**
     * 新增用户
     */
    @RequiresPermissions("tds:user:add")
    @OperateLog("用户新增")
    @PostMapping
    public CommonResult add(@Validated @RequestBody TdsUser user) {
        if (!userService.checkUserNameUnique(user.getId(), user.getUserName())) {
            throw BizException.build(BizCode.USER_ACCOUNT_ALREADY_EXISTED);
        } else if (StringUtils.isNotEmpty(user.getPhone()) && !userService.checkPhoneUnique(user.getId(), user.getPhone())) {
            throw BizException.build(BizCode.USER_PHONE_ALREADY_EXISTED);
        } else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user.getId(), user.getEmail())) {
            throw BizException.build(BizCode.USER_EMAIL_ALREADY_EXISTED);
        }
        user.setPassword(PasswordUtils.encryptPassword(user.getPassword()));
        return CommonResult.success(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @RequiresPermissions("tds:user:edit")
    @OperateLog("用户管理")
    @PutMapping
    public CommonResult edit(@Validated @RequestBody TdsUser user) {
        if (!userService.checkUserNameUnique(user.getId(), user.getUserName())) {
            throw BizException.build(BizCode.USER_ACCOUNT_ALREADY_EXISTED);
        } else if (StringUtils.isNotEmpty(user.getPhone()) && !userService.checkPhoneUnique(user.getId(), user.getPhone())) {
            throw BizException.build(BizCode.USER_PHONE_ALREADY_EXISTED);
        } else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user.getId(), user.getEmail())) {
            throw BizException.build(BizCode.USER_EMAIL_ALREADY_EXISTED);
        }
        return CommonResult.success(userService.updateUser(user));
    }

    /**
     * 删除用户
     */
    @RequiresPermissions("tds:user:remove")
    @OperateLog("用户管理")
    @DeleteMapping("/{ids}")
    public CommonResult remove(@PathVariable String[] ids) {
        return CommonResult.success(userService.deleteUserByIds(ids));
    }

    /**
     * 重置密码
     */
    @RequiresPermissions("tds:user:edit")
    @OperateLog("重置密码")
    @PutMapping("/resetPwd")
    public CommonResult resetPwd(@RequestBody TdsUser user) {
        user.setPassword(PasswordUtils.encryptPassword(user.getPassword()));
        return CommonResult.success(userService.resetPwd(user));
    }

    /**
     * 状态修改
     */
    @RequiresPermissions("tds:user:edit")
    @OperateLog("修改用户状态")
    @PutMapping("/changeStatus")
    public CommonResult changeStatus(@RequestBody TdsUser user) {
        return CommonResult.success(userService.updateUserStatus(user));
    }
}
