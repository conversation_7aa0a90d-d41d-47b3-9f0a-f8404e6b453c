package com.bosi.sim.paas.admin.server.controller;

import com.bosi.sim.paas.common.core.api.BizCode;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.exception.CommonResult;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.PageUtil;
import com.bosi.sim.paas.common.optlog.annotation.OperateLog;
import com.bosi.sim.paas.common.security.annotation.RequiresPermissions;
import com.bosi.sim.paas.dao.model.tds.TdsTenant;
import com.bosi.sim.paas.admin.server.domain.TmsDistributorCreateParam;
import com.bosi.sim.paas.admin.server.service.TmsDistributorService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 分销商管理
 */
@RestController
@Api(tags = "TmsDistributorController", value = "分销商管理")
@RequestMapping("/tms/distributor")
public class TmsDistributorController {
    @Autowired
    private TmsDistributorService distributorService;

    /**
     * 获取分销商列表
     */
    @RequiresPermissions("tms:distributor:page")
    @GetMapping("/page")
    public CommonResult page(TdsTenant distributor,
                             @RequestParam(value = "pageSize") Integer pageSize,
                             @RequestParam(value = "pageNum") Integer pageNum) {
        CommonPage<TdsTenant> page = distributorService.page(PageUtil.buildPage(pageNum, pageSize), distributor);
        return CommonResult.success(page);
    }

    @RequiresPermissions("tms:distributor:list:all")
    @GetMapping("/listAll")
    public CommonResult listAll() {
        return CommonResult.success(distributorService.listAll());
    }

    /**
     * 根据分销商编号获取详细信息
     */
    @RequiresPermissions("tms:distributor:query")
    @GetMapping("/{id}")
    public CommonResult getInfo(@PathVariable String id) {
        return CommonResult.success(distributorService.selectDistributorById(id));
    }

    /**
     * 新增分销商
     */
    @RequiresPermissions("tms:distributor:add")
    @OperateLog("新增分销商")
    @PostMapping
    public CommonResult add(@Validated @RequestBody TmsDistributorCreateParam distributorCreateParam) {
        distributorService.insertDistributor(distributorCreateParam);
        return CommonResult.success();
    }

    /**
     * 修改分销商
     */
    @RequiresPermissions("tms:distributor:edit")
    @OperateLog("修改分销商")
    @PutMapping
    public CommonResult edit(@Validated @RequestBody TdsTenant distributor) {
        distributorService.updateDistributor(distributor);
        return CommonResult.success();
    }

    /**
     * 删除分销商
     */
    @RequiresPermissions("tms:distributor:remove")
    @OperateLog("删除分销商")
    @DeleteMapping("/{id}")
    public CommonResult remove(@PathVariable String id) {
        if (distributorService.checkDistributorExistUser(id)) {
            throw BizException.build(BizCode.DISTRIBUTOR_EXIST_USER);
        }
        distributorService.deleteDistributorById(id);
        return CommonResult.success();
    }

}
