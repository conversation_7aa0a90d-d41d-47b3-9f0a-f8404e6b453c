package com.bosi.sim.paas.dao.model.sds;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Set;

@Data
@TableName("sds_product_sku")
public class SdsProductSku extends BaseEntity {
    private String skuName;

    private Integer sort;

    private String productId;

    @ApiModelProperty(value = "sku编码")
    private String skuCode;

    @ApiModelProperty(value = "原始价格")
    private BigDecimal originalPrice;

    @ApiModelProperty(value = "真实价格")
    private BigDecimal price;

    @ApiModelProperty(value = "库存")
    private Integer stock;

    @ApiModelProperty(value = "锁定库存")
    private Integer lockStock;

    @ApiModelProperty(value = "预警库存")
    private Integer lowStock;

    @ApiModelProperty(value = "展示图片")
    private String pic;

    @ApiModelProperty(value = "销量")
    private Integer sale;

    @ApiModelProperty(value = "商品销售属性，json格式")
    private String spData;

    @ApiModelProperty(value = "品牌名称")
    @TableField(exist = false)
    private String brandName;

    @ApiModelProperty(value = "货号")
    @TableField(exist = false)
    private String productSn;

    @ApiModelProperty(value = "是否上架")
    @TableField(exist = false)
    private Boolean whetherPublish;

    @TableField(exist = false)
    private Set<String> referralCodes;


}
