package com.bosi.sim.paas.admin.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.BizCode;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.common.security.util.PasswordUtils;
import com.bosi.sim.paas.dao.enums.tds.TdsAccountTypeEnum;
import com.bosi.sim.paas.dao.mapper.tms.TmsDistributorMapper;
import com.bosi.sim.paas.dao.mapper.tms.TmsRoleMapper;
import com.bosi.sim.paas.dao.mapper.tms.TmsUserAccountMapper;
import com.bosi.sim.paas.dao.mapper.tms.TmsUserMapper;
import com.bosi.sim.paas.dao.mapper.tms.TmsUserRoleMapper;
import com.bosi.sim.paas.dao.model.tds.TdsTenant;
import com.bosi.sim.paas.dao.model.tds.TdsRole;
import com.bosi.sim.paas.dao.model.tds.TdsUser;
import com.bosi.sim.paas.dao.model.tds.TdsTenantAccount;
import com.bosi.sim.paas.dao.model.tds.TdsUserRole;
import com.bosi.sim.paas.admin.server.domain.TmsDistributorCreateParam;
import com.bosi.sim.paas.admin.server.service.TmsDistributorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 分销商管理 服务实现
 */
@Service
public class TmsDistributorServiceImpl implements TmsDistributorService {
    @Autowired
    private TmsDistributorMapper distributorMapper;

    @Autowired
    private TmsRoleMapper tmsRoleMapper;

    @Autowired
    private TmsUserAccountMapper tmsUserAccountMapper;

    @Autowired
    private TmsUserRoleMapper tmsUserRoleMapper;

    @Autowired
    private TmsUserMapper tmsUserMapper;

    @Override
    public CommonPage<TdsTenant> page(Page<TdsTenant> page, TdsTenant distributor) {
        LambdaQueryWrapper<TdsTenant> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(distributor.getDistributorName())) {
            queryWrapper.like(TdsTenant::getDistributorName, distributor.getDistributorName());
        }
        if (StringUtils.isNotNull(distributor.getWhetherEnable())) {
            queryWrapper.eq(TdsTenant::getWhetherEnable, distributor.getWhetherEnable());
        }
        if (StringUtils.isNotNull(distributor.getSettleMode())) {
            queryWrapper.eq(TdsTenant::getSettleMode, distributor.getSettleMode());
        }
        return CommonPage.restPage(distributorMapper.selectPage(page, queryWrapper));
    }

    @Override
    public List<TdsTenant> listAll() {
        return distributorMapper.selectList(new LambdaQueryWrapper<>());
    }

    /**
     * 根据分销商ID查询信息
     *
     * @param distributorId 分销商ID
     * @return 分销商信息
     */
    @Override
    public TdsTenant selectDistributorById(String distributorId) {
        return distributorMapper.selectById(distributorId);
    }

    /**
     * 查询分销商是否存在用户
     *
     * @param distributorId 分销商ID
     * @return 结果 true 存在 false 不存在
     */
    @Override
    public boolean checkDistributorExistUser(String distributorId) {
        long result = tmsUserMapper.selectCount(new LambdaQueryWrapper<TdsUser>().eq(TdsUser::getDistributorId, distributorId));
        return result > 0L;
    }

    /**
     * 新增保存分销商信息
     *
     * @param distributorCreateParam 分销商信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertDistributor(TmsDistributorCreateParam distributorCreateParam) {
        String distributroName = distributorCreateParam.getDistributor().getDistributorName();
        LambdaQueryWrapper<TdsTenant> distributorLambdaQueryWrapper = new LambdaQueryWrapper<>();
        distributorLambdaQueryWrapper.eq(TdsTenant::getDistributorName, distributroName);
        if (distributorMapper.selectOne(distributorLambdaQueryWrapper) != null) {
            throw BizException.build(BizCode.DISTRIBUTOR_NAME_REPEAT);
        }
        String userName = distributorCreateParam.getDistributor().getDistributorName();
        LambdaQueryWrapper<TdsUser> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userLambdaQueryWrapper.eq(TdsUser::getUserName, userName);
        if (tmsUserMapper.selectOne(userLambdaQueryWrapper) != null) {
            throw BizException.build(BizCode.USER_ACCOUNT_ALREADY_EXISTED);
        }
        TdsTenant distributor = distributorCreateParam.getDistributor();
        int result = distributorMapper.insert(distributor);
        TdsUser tmsUser = distributorCreateParam.getUser();
        tmsUser.setPassword(PasswordUtils.encryptPassword(tmsUser.getPassword()));
        tmsUser.setDistributorId(distributor.getId());
        tmsUser.setWhetherAdmin(true);
        tmsUser.setWhetherEnable(true);
        tmsUserMapper.insert(tmsUser);
        TdsRole tmsRole = new TdsRole();
        tmsRole.setRoleName("分销管理员");
        tmsRole.setDistributorId(distributor.getId());
        tmsRole.setWhetherEnable(true);
        tmsRole.setWhetherAdmin(true);
        tmsRoleMapper.insert(tmsRole);
        TdsUserRole tmsUserRoleRelation = new TdsUserRole();
        tmsUserRoleRelation.setRoleId(tmsRole.getId());
        tmsUserRoleRelation.setUserId(tmsUser.getId());
        tmsUserRoleMapper.insert(tmsUserRoleRelation);
        TdsTenantAccount tmsUserAccount = new TdsTenantAccount();
        tmsUserAccount.setDistributorId(distributor.getId());
        tmsUserAccount.setUserId(tmsUser.getId());
        tmsUserAccount.setAccountType(TdsAccountTypeEnum.COMPANY.getAccountType());
        tmsUserAccountMapper.insert(tmsUserAccount);
        return result;
    }

    /**
     * 修改保存分销商信息
     *
     * @param distributor 分销商信息
     * @return 结果
     */
    @Override
    public int updateDistributor(TdsTenant distributor) {
        String distributroName = distributor.getDistributorName();
        LambdaQueryWrapper<TdsTenant> distributorLambdaQueryWrapper = new LambdaQueryWrapper<>();
        distributorLambdaQueryWrapper.eq(TdsTenant::getDistributorName, distributroName);
        TdsTenant info = distributorMapper.selectOne(distributorLambdaQueryWrapper);
        if (info != null && !info.getId().equals(distributor.getId())) {
            throw BizException.build(BizCode.DISTRIBUTOR_NAME_REPEAT);
        }
        return distributorMapper.updateById(distributor);
    }

    /**
     * 删除分销商管理信息
     *
     * @param distributorId 分销商ID
     * @return 结果
     */
    @Override
    public int deleteDistributorById(String distributorId) {
        return distributorMapper.deleteById(distributorId);
    }

}
