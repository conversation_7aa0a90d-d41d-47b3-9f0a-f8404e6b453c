package com.bosi.sim.paas.tenant.server.controller;

import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.exception.CommonResult;
import com.bosi.sim.paas.common.core.utils.PageUtil;
import com.bosi.sim.paas.common.optlog.annotation.OperateLog;
import com.bosi.sim.paas.common.security.annotation.RequiresPermissions;
import com.bosi.sim.paas.common.security.auth.CurrentAuthorization;
import com.bosi.sim.paas.dao.model.tds.TdsNotifyRecord;
import com.bosi.sim.paas.tenant.server.enums.DistributorTokenExtendKeyEnum;
import com.bosi.sim.paas.tenant.server.service.TmsNotifyRecordService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 通知记录管理
 */
@RestController
@Api(tags = "TmsNotifyRecordController", value = "通知记录管理")
@RequestMapping("/tms/notify/record")
public class TmsNotifyRecordController {
    @Autowired
    private TmsNotifyRecordService tmsNotifyRecordService;

    @RequiresPermissions("tms:notify:record:page")
    @GetMapping("/page")
    public CommonResult page(
            @RequestParam(value = "pageNum") Integer pageNum,
            @RequestParam(value = "pageSize") Integer pageSize,
            TdsNotifyRecord tmsNotifyRecord) {
        tmsNotifyRecord.setDistributorId(CurrentAuthorization.getStringFromExtendJson(DistributorTokenExtendKeyEnum.DISTRIBUTORID_KEY.getKey()));
        CommonPage<TdsNotifyRecord> page = tmsNotifyRecordService.page(PageUtil.buildPage(pageNum, pageSize), tmsNotifyRecord);
        return CommonResult.success(page);
    }

    @RequiresPermissions("tms:notify:record:read")
    @OperateLog("读取通知记录")
    @PostMapping("/read")
    public CommonResult read(@RequestParam String id) {
        return CommonResult.success(tmsNotifyRecordService.read(id, CurrentAuthorization.getStringFromExtendJson(DistributorTokenExtendKeyEnum.DISTRIBUTORID_KEY.getKey())));
    }


}
