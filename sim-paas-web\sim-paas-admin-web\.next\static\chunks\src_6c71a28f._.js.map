{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/components/layout/AppMain.tsx"], "sourcesContent": ["'use client';\n\nimport { usePathname } from 'next/navigation';\nimport { useEffect, useRef, useState } from 'react';\n\ninterface AppMainProps {\n  children: React.ReactNode;\n}\n\n/**\n * 主应用容器组件\n * 参考若依框架的实现，使用缓存和平滑过渡\n */\nexport default function AppMain({ children }: AppMainProps) {\n  const pathname = usePathname();\n  const [cachedViews, setCachedViews] = useState<Record<string, React.ReactNode>>({});\n  const [currentView, setCurrentView] = useState<React.ReactNode>(children);\n  const [isTransitioning, setIsTransitioning] = useState(false);\n  const prevPathRef = useRef(pathname);\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  // 当路由变化时更新视图\n  useEffect(() => {\n    // 只有当路径真正变化时才触发过渡效果\n    if (prevPathRef.current !== pathname) {\n      // 开始过渡动画\n      setIsTransitioning(true);\n      \n      // 缓存当前视图\n      setCachedViews(prev => ({\n        ...prev,\n        [prevPathRef.current]: currentView\n      }));\n      \n      // 使用 requestAnimationFrame 确保平滑过渡\n      requestAnimationFrame(() => {\n        // 短暂延迟后更新视图\n        setTimeout(() => {\n          // 如果有缓存，使用缓存的视图\n          if (cachedViews[pathname]) {\n            setCurrentView(cachedViews[pathname]);\n          } else {\n            // 否则使用新的视图\n            setCurrentView(children);\n          }\n          \n          // 结束过渡动画\n          setIsTransitioning(false);\n        }, 100);\n      });\n      \n      // 更新前一个路径\n      prevPathRef.current = pathname;\n    } else {\n      // 如果只是组件更新，直接更新视图\n      setCurrentView(children);\n    }\n  }, [pathname, children]);\n\n  return (\n    <div \n      ref={containerRef}\n      className=\"app-main-container\"\n      style={{\n        position: 'relative',\n        minHeight: '400px',\n        overflow: 'hidden'\n      }}\n    >\n      <div\n        className={`app-main-content ${isTransitioning ? 'transitioning' : ''}`}\n        style={{\n          opacity: isTransitioning ? 0.8 : 1,\n          transform: isTransitioning ? 'translateY(8px)' : 'translateY(0)',\n          transition: 'opacity 0.2s ease-in-out, transform 0.2s ease-in-out',\n        }}\n      >\n        {currentView}\n      </div>\n      \n      <style jsx>{`\n        .app-main-container {\n          position: relative;\n          min-height: 400px;\n          overflow: hidden;\n        }\n        \n        .app-main-content {\n          width: 100%;\n          height: 100%;\n        }\n        \n        .transitioning {\n          pointer-events: none;\n        }\n      `}</style>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;;AAae,SAAS,QAAQ,EAAE,QAAQ,EAAgB;;IACxD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC,CAAC;IACjF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAChE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,aAAa;IACb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,oBAAoB;YACpB,IAAI,YAAY,OAAO,KAAK,UAAU;gBACpC,SAAS;gBACT,mBAAmB;gBAEnB,SAAS;gBACT;yCAAe,CAAA,OAAQ,CAAC;4BACtB,GAAG,IAAI;4BACP,CAAC,YAAY,OAAO,CAAC,EAAE;wBACzB,CAAC;;gBAED,kCAAkC;gBAClC;yCAAsB;wBACpB,YAAY;wBACZ;iDAAW;gCACT,gBAAgB;gCAChB,IAAI,WAAW,CAAC,SAAS,EAAE;oCACzB,eAAe,WAAW,CAAC,SAAS;gCACtC,OAAO;oCACL,WAAW;oCACX,eAAe;gCACjB;gCAEA,SAAS;gCACT,mBAAmB;4BACrB;gDAAG;oBACL;;gBAEA,UAAU;gBACV,YAAY,OAAO,GAAG;YACxB,OAAO;gBACL,kBAAkB;gBAClB,eAAe;YACjB;QACF;4BAAG;QAAC;QAAU;KAAS;IAEvB,qBACE,6LAAC;QACC,KAAK;QAEL,OAAO;YACL,UAAU;YACV,WAAW;YACX,UAAU;QACZ;kDALU;;0BAOV,6LAAC;gBAEC,OAAO;oBACL,SAAS,kBAAkB,MAAM;oBACjC,WAAW,kBAAkB,oBAAoB;oBACjD,YAAY;gBACd;0DALW,CAAC,iBAAiB,EAAE,kBAAkB,kBAAkB,IAAI;0BAOtE;;;;;;;;;;;;;;;;AAqBT;GArFwB;;QACL,qIAAA,CAAA,cAAW;;;KADN", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/components/layout/AdminLayout.tsx"], "sourcesContent": ["'use client';\n\nimport AppMain from '@/components/layout/AppMain';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { menuService } from '@/services';\nimport { Menu } from '@/types';\nimport {\n  DashboardOutlined,\n  LogoutOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  SettingOutlined,\n  TeamOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport { Avatar, Breadcrumb, Dropdown, Layout, Menu as AntMenu, Spin, theme } from 'antd';\nimport Link from 'next/link';\nimport { usePathname, useRouter } from 'next/navigation';\nimport { useEffect, useState } from 'react';\n\nconst { Header, Sider, Content } = Layout;\n\n// 图标映射\nconst iconMap: Record<string, React.ReactNode> = {\n  dashboard: <DashboardOutlined />,\n  setting: <SettingOutlined />,\n  user: <UserOutlined />,\n  team: <TeamOutlined />,\n  menu: <MenuOutlined />,\n};\n\n// 自定义 MenuOutlined 组件\nfunction MenuOutlined() {\n  return <span className=\"anticon\">\n    <svg viewBox=\"64 64 896 896\" focusable=\"false\" data-icon=\"menu\" width=\"1em\" height=\"1em\" fill=\"currentColor\" aria-hidden=\"true\">\n      <path d=\"M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z\"></path>\n    </svg>\n  </span>;\n}\n\n// 将菜单数据转换为 Ant Design Menu 组件所需的格式\nconst convertMenuItems = (menus: Menu[]) => {\n  return menus.map(menu => {\n    const item = {\n      key: menu.path,\n      icon: menu.icon ? iconMap[menu.icon] || <MenuOutlined /> : null,\n      label: menu.children && menu.children.length > 0\n        ? menu.name\n        : <Link href={menu.path} style={{ color: 'inherit', textDecoration: 'none' }}>{menu.name}</Link>,\n      children: menu.children && menu.children.length > 0 ? convertMenuItems(menu.children) : undefined,\n    };\n    return item;\n  });\n};\n\n// 获取当前路径的面包屑\nconst getBreadcrumbItems = (pathname: string, menus: Menu[]) => {\n  const paths = pathname.split('/').filter(Boolean);\n  const breadcrumbItems = [{\n    title: <Link href=\"/dashboard\" style={{ color: 'inherit', textDecoration: 'none' }}>首页</Link>,\n  }];\n\n  let currentPath = '';\n  let currentMenus = menus;\n\n  for (const path of paths) {\n    currentPath += `/${path}`;\n\n    // 在当前菜单层级中查找匹配的菜单\n    const findMenu = (menus: Menu[], path: string): Menu | undefined => {\n      for (const menu of menus) {\n        if (menu.path === path) {\n          return menu;\n        }\n        if (menu.children) {\n          const found = findMenu(menu.children, path);\n          if (found) {\n            return found;\n          }\n        }\n      }\n      return undefined;\n    };\n\n    const menu = findMenu(currentMenus, currentPath);\n    if (menu) {\n      breadcrumbItems.push({\n        title: <Link href={menu.path} style={{ color: 'inherit', textDecoration: 'none' }}>{menu.name}</Link>,\n      });\n      if (menu.children) {\n        currentMenus = menu.children;\n      }\n    }\n  }\n\n  return breadcrumbItems;\n};\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n}\n\nconst AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {\n  const { user, logout } = useAuth();\n  const router = useRouter();\n  const pathname = usePathname();\n  const [collapsed, setCollapsed] = useState(false);\n  const [menuItems, setMenuItems] = useState<any[]>([]);\n  const [menuTree, setMenuTree] = useState<Menu[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  const {\n    token: { colorBgContainer, borderRadiusLG },\n  } = theme.useToken();\n\n  // 获取菜单数据\n  useEffect(() => {\n    const fetchMenus = async () => {\n      try {\n        setLoading(true);\n        const menuTree = await menuService.getMenuTree();\n        setMenuTree(menuTree);\n        setMenuItems(convertMenuItems(menuTree));\n      } catch (error) {\n        console.error('获取菜单失败:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchMenus();\n  }, []);\n\n  // 处理菜单点击 - 参考若依框架实现\n  const handleMenuClick = ({ key }: { key: string }) => {\n    // 如果点击的是当前路径，不进行跳转\n    if (pathname === key) return;\n\n    // 使用 router.push 进行客户端导航，但不滚动到顶部\n    router.push(key, { scroll: false });\n\n    // 阻止默认行为，防止页面刷新\n    // 注意：这是模拟若依框架的行为，实际上 Next.js 的 router.push 已经是客户端导航\n  };\n\n  // 处理登出\n  const handleLogout = async () => {\n    await logout();\n    router.push('/login');\n  };\n\n  // 用户下拉菜单\n  const userMenuItems = [\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n      onClick: handleLogout,\n    },\n  ];\n\n  // 如果正在加载，显示加载中\n  if (loading) {\n    return (\n      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>\n        <Spin size=\"large\" />\n      </div>\n    );\n  }\n\n  return (\n    <Layout style={{ minHeight: '100vh' }}>\n      <Sider trigger={null} collapsible collapsed={collapsed} theme=\"light\">\n        <div style={{ height: 64, display: 'flex', alignItems: 'center', justifyContent: 'center', borderBottom: '1px solid #f0f0f0' }}>\n          <h1 style={{ margin: 0, fontSize: collapsed ? 16 : 20, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>\n            {collapsed ? 'IOT' : 'IOT Admin'}\n          </h1>\n        </div>\n        <AntMenu\n          mode=\"inline\"\n          selectedKeys={[pathname]}\n          defaultOpenKeys={['/system']}\n          style={{ height: '100%', borderRight: 0 }}\n          items={menuItems}\n          onClick={handleMenuClick}\n        />\n      </Sider>\n      <Layout>\n        <Header style={{ padding: 0, background: colorBgContainer, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n          <div style={{ paddingLeft: 16 }}>\n            {collapsed ? (\n              <MenuUnfoldOutlined onClick={() => setCollapsed(false)} style={{ fontSize: 18 }} />\n            ) : (\n              <MenuFoldOutlined onClick={() => setCollapsed(true)} style={{ fontSize: 18 }} />\n            )}\n          </div>\n          <div style={{ paddingRight: 24 }}>\n            <Dropdown menu={{ items: userMenuItems }} placement=\"bottomRight\">\n              <div style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}>\n                <Avatar src={user?.avatar} style={{ marginRight: 8 }} />\n                <span>{user?.name}</span>\n              </div>\n            </Dropdown>\n          </div>\n        </Header>\n        <Content style={{ margin: '16px' }}>\n          <Breadcrumb style={{ margin: '16px 0' }} items={getBreadcrumbItems(pathname, menuTree)} />\n          <div\n            style={{\n              padding: 24,\n              minHeight: 360,\n              background: colorBgContainer,\n              borderRadius: borderRadiusLG,\n            }}\n          >\n            <AppMain>\n              {children}\n            </AppMain>\n          </div>\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\nexport default AdminLayout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAlBA;;;;;;;;;AAoBA,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,qLAAA,CAAA,SAAM;AAEzC,OAAO;AACP,MAAM,UAA2C;IAC/C,yBAAW,6LAAC,+NAAA,CAAA,oBAAiB;;;;;IAC7B,uBAAS,6LAAC,2NAAA,CAAA,kBAAe;;;;;IACzB,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;IACnB,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;IACnB,oBAAM,6LAAC;;;;;AACT;AAEA,sBAAsB;AACtB,SAAS;IACP,qBAAO,6LAAC;QAAK,WAAU;kBACrB,cAAA,6LAAC;YAAI,SAAQ;YAAgB,WAAU;YAAQ,aAAU;YAAO,OAAM;YAAM,QAAO;YAAM,MAAK;YAAe,eAAY;sBACvH,cAAA,6LAAC;gBAAK,GAAE;;;;;;;;;;;;;;;;AAGd;KANS;AAQT,mCAAmC;AACnC,MAAM,mBAAmB,CAAC;IACxB,OAAO,MAAM,GAAG,CAAC,CAAA;QACf,MAAM,OAAO;YACX,KAAK,KAAK,IAAI;YACd,MAAM,KAAK,IAAI,GAAG,OAAO,CAAC,KAAK,IAAI,CAAC,kBAAI,6LAAC;;;;uBAAkB;YAC3D,OAAO,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,IAC3C,KAAK,IAAI,iBACT,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAM,KAAK,IAAI;gBAAE,OAAO;oBAAE,OAAO;oBAAW,gBAAgB;gBAAO;0BAAI,KAAK,IAAI;;;;;;YAC1F,UAAU,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,IAAI,iBAAiB,KAAK,QAAQ,IAAI;QAC1F;QACA,OAAO;IACT;AACF;AAEA,aAAa;AACb,MAAM,qBAAqB,CAAC,UAAkB;IAC5C,MAAM,QAAQ,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;IACzC,MAAM,kBAAkB;QAAC;YACvB,qBAAO,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAK;gBAAa,OAAO;oBAAE,OAAO;oBAAW,gBAAgB;gBAAO;0BAAG;;;;;;QACtF;KAAE;IAEF,IAAI,cAAc;IAClB,IAAI,eAAe;IAEnB,KAAK,MAAM,QAAQ,MAAO;QACxB,eAAe,CAAC,CAAC,EAAE,MAAM;QAEzB,kBAAkB;QAClB,MAAM,WAAW,CAAC,OAAe;YAC/B,KAAK,MAAM,QAAQ,MAAO;gBACxB,IAAI,KAAK,IAAI,KAAK,MAAM;oBACtB,OAAO;gBACT;gBACA,IAAI,KAAK,QAAQ,EAAE;oBACjB,MAAM,QAAQ,SAAS,KAAK,QAAQ,EAAE;oBACtC,IAAI,OAAO;wBACT,OAAO;oBACT;gBACF;YACF;YACA,OAAO;QACT;QAEA,MAAM,OAAO,SAAS,cAAc;QACpC,IAAI,MAAM;YACR,gBAAgB,IAAI,CAAC;gBACnB,qBAAO,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAM,KAAK,IAAI;oBAAE,OAAO;wBAAE,OAAO;wBAAW,gBAAgB;oBAAO;8BAAI,KAAK,IAAI;;;;;;YAC/F;YACA,IAAI,KAAK,QAAQ,EAAE;gBACjB,eAAe,KAAK,QAAQ;YAC9B;QACF;IACF;IAEA,OAAO;AACT;AAMA,MAAM,cAA0C,CAAC,EAAE,QAAQ,EAAE;;IAC3D,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACpD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,EACJ,OAAO,EAAE,gBAAgB,EAAE,cAAc,EAAE,EAC5C,GAAG,mLAAA,CAAA,QAAK,CAAC,QAAQ;IAElB,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;oDAAa;oBACjB,IAAI;wBACF,WAAW;wBACX,MAAM,WAAW,MAAM,qIAAA,CAAA,cAAW,CAAC,WAAW;wBAC9C,YAAY;wBACZ,aAAa,iBAAiB;oBAChC,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;oBAC3B,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;gCAAG,EAAE;IAEL,oBAAoB;IACpB,MAAM,kBAAkB,CAAC,EAAE,GAAG,EAAmB;QAC/C,mBAAmB;QACnB,IAAI,aAAa,KAAK;QAEtB,iCAAiC;QACjC,OAAO,IAAI,CAAC,KAAK;YAAE,QAAQ;QAAM;IAEjC,gBAAgB;IAChB,oDAAoD;IACtD;IAEA,OAAO;IACP,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,SAAS;IACT,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;YACP,SAAS;QACX;KACD;IAED,eAAe;IACf,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,OAAO;gBAAE,SAAS;gBAAQ,gBAAgB;gBAAU,YAAY;gBAAU,QAAQ;YAAQ;sBAC7F,cAAA,6LAAC,iLAAA,CAAA,OAAI;gBAAC,MAAK;;;;;;;;;;;IAGjB;IAEA,qBACE,6LAAC,qLAAA,CAAA,SAAM;QAAC,OAAO;YAAE,WAAW;QAAQ;;0BAClC,6LAAC;gBAAM,SAAS;gBAAM,WAAW;gBAAC,WAAW;gBAAW,OAAM;;kCAC5D,6LAAC;wBAAI,OAAO;4BAAE,QAAQ;4BAAI,SAAS;4BAAQ,YAAY;4BAAU,gBAAgB;4BAAU,cAAc;wBAAoB;kCAC3H,cAAA,6LAAC;4BAAG,OAAO;gCAAE,QAAQ;gCAAG,UAAU,YAAY,KAAK;gCAAI,YAAY;gCAAU,UAAU;gCAAU,cAAc;4BAAW;sCACvH,YAAY,QAAQ;;;;;;;;;;;kCAGzB,6LAAC,iLAAA,CAAA,OAAO;wBACN,MAAK;wBACL,cAAc;4BAAC;yBAAS;wBACxB,iBAAiB;4BAAC;yBAAU;wBAC5B,OAAO;4BAAE,QAAQ;4BAAQ,aAAa;wBAAE;wBACxC,OAAO;wBACP,SAAS;;;;;;;;;;;;0BAGb,6LAAC,qLAAA,CAAA,SAAM;;kCACL,6LAAC;wBAAO,OAAO;4BAAE,SAAS;4BAAG,YAAY;4BAAkB,SAAS;4BAAQ,YAAY;4BAAU,gBAAgB;wBAAgB;;0CAChI,6LAAC;gCAAI,OAAO;oCAAE,aAAa;gCAAG;0CAC3B,0BACC,6LAAC,iOAAA,CAAA,qBAAkB;oCAAC,SAAS,IAAM,aAAa;oCAAQ,OAAO;wCAAE,UAAU;oCAAG;;;;;yDAE9E,6LAAC,6NAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,aAAa;oCAAO,OAAO;wCAAE,UAAU;oCAAG;;;;;;;;;;;0CAG/E,6LAAC;gCAAI,OAAO;oCAAE,cAAc;gCAAG;0CAC7B,cAAA,6LAAC,yLAAA,CAAA,WAAQ;oCAAC,MAAM;wCAAE,OAAO;oCAAc;oCAAG,WAAU;8CAClD,cAAA,6LAAC;wCAAI,OAAO;4CAAE,QAAQ;4CAAW,SAAS;4CAAQ,YAAY;wCAAS;;0DACrE,6LAAC,qLAAA,CAAA,SAAM;gDAAC,KAAK,MAAM;gDAAQ,OAAO;oDAAE,aAAa;gDAAE;;;;;;0DACnD,6LAAC;0DAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAKrB,6LAAC;wBAAQ,OAAO;4BAAE,QAAQ;wBAAO;;0CAC/B,6LAAC,6LAAA,CAAA,aAAU;gCAAC,OAAO;oCAAE,QAAQ;gCAAS;gCAAG,OAAO,mBAAmB,UAAU;;;;;;0CAC7E,6LAAC;gCACC,OAAO;oCACL,SAAS;oCACT,WAAW;oCACX,YAAY;oCACZ,cAAc;gCAChB;0CAEA,cAAA,6LAAC,0IAAA,CAAA,UAAO;8CACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAzHM;;QACqB,kIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QAQxB,mLAAA,CAAA,QAAK,CAAC;;;MAXN;uCA2HS", "debugId": null}}, {"offset": {"line": 619, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/app/system/roles/page.tsx"], "sourcesContent": ["'use client';\n\nimport AdminLayout from '@/components/layout/AdminLayout';\nimport { menuService, roleService } from '@/services';\nimport { Menu, Role, RoleCreateInput, RoleUpdateInput } from '@/types';\nimport { DeleteOutlined, EditOutlined, PlusOutlined, SearchOutlined } from '@ant-design/icons';\nimport { Button, Form, Input, Modal, Popconfirm, Space, Table, Tag, Tree, message } from 'antd';\nimport { ColumnsType } from 'antd/es/table';\nimport { DataNode } from 'antd/es/tree';\nimport { useEffect, useState } from 'react';\n\n// 将菜单数据转换为 Tree 组件所需的格式\nconst convertMenusToTreeData = (menus: Menu[]): DataNode[] => {\n  return menus.map(menu => ({\n    title: menu.name,\n    key: menu.id,\n    children: menu.children ? convertMenusToTreeData(menu.children) : undefined,\n  }));\n};\n\nexport default function RolesPage() {\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [menuTree, setMenuTree] = useState<Menu[]>([]);\n  const [treeData, setTreeData] = useState<DataNode[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize, setPageSize] = useState(10);\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isEditing, setIsEditing] = useState(false);\n  const [currentRole, setCurrentRole] = useState<Role | null>(null);\n  const [form] = Form.useForm();\n\n  // 获取角色列表\n  const fetchRoles = async () => {\n    try {\n      setLoading(true);\n      const response = await roleService.getRoles({ page: currentPage, pageSize });\n      setRoles(response.data);\n      setTotal(response.total);\n    } catch (error) {\n      console.error('获取角色列表失败:', error);\n      message.error('获取角色列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取菜单树\n  const fetchMenuTree = async () => {\n    try {\n      const menuTree = await menuService.getMenuTree();\n      setMenuTree(menuTree);\n      setTreeData(convertMenusToTreeData(menuTree));\n    } catch (error) {\n      console.error('获取菜单树失败:', error);\n      message.error('获取菜单树失败');\n    }\n  };\n\n  // 初始化\n  useEffect(() => {\n    fetchRoles();\n    fetchMenuTree();\n  }, [currentPage, pageSize]);\n\n  // 处理分页变化\n  const handleTableChange = (pagination: any) => {\n    setCurrentPage(pagination.current);\n    setPageSize(pagination.pageSize);\n  };\n\n  // 打开创建角色模态框\n  const showCreateModal = () => {\n    setIsEditing(false);\n    setCurrentRole(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  // 打开编辑角色模态框\n  const showEditModal = (role: Role) => {\n    setIsEditing(true);\n    setCurrentRole(role);\n    form.setFieldsValue({\n      ...role,\n    });\n    setIsModalVisible(true);\n  };\n\n  // 关闭模态框\n  const handleCancel = () => {\n    setIsModalVisible(false);\n    form.resetFields();\n  };\n\n  // 处理表单提交\n  const handleSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n      \n      if (isEditing && currentRole) {\n        // 更新角色\n        await roleService.updateRole({\n          id: currentRole.id,\n          ...values,\n        } as RoleUpdateInput);\n        message.success('角色更新成功');\n      } else {\n        // 创建角色\n        await roleService.createRole(values as RoleCreateInput);\n        message.success('角色创建成功');\n      }\n      \n      setIsModalVisible(false);\n      fetchRoles();\n    } catch (error) {\n      console.error('提交表单失败:', error);\n      message.error('操作失败，请重试');\n    }\n  };\n\n  // 处理删除角色\n  const handleDelete = async (id: string) => {\n    try {\n      await roleService.deleteRole(id);\n      message.success('角色删除成功');\n      fetchRoles();\n    } catch (error) {\n      console.error('删除角色失败:', error);\n      message.error('删除角色失败');\n    }\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<Role> = [\n    {\n      title: '角色名称',\n      dataIndex: 'name',\n      key: 'name',\n    },\n    {\n      title: '描述',\n      dataIndex: 'description',\n      key: 'description',\n    },\n    {\n      title: '菜单权限',\n      dataIndex: 'menuIds',\n      key: 'menuIds',\n      render: (menuIds: string[]) => (\n        <>\n          {menuIds.slice(0, 3).map(menuId => {\n            // 递归查找菜单名称\n            const findMenuName = (menus: Menu[], id: string): string | undefined => {\n              for (const menu of menus) {\n                if (menu.id === id) {\n                  return menu.name;\n                }\n                if (menu.children) {\n                  const name = findMenuName(menu.children, id);\n                  if (name) return name;\n                }\n              }\n              return undefined;\n            };\n            \n            const menuName = findMenuName(menuTree, menuId);\n            return menuName ? <Tag key={menuId}>{menuName}</Tag> : null;\n          })}\n          {menuIds.length > 3 && <Tag>+{menuIds.length - 3}</Tag>}\n        </>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space size=\"middle\">\n          <Button \n            type=\"text\" \n            icon={<EditOutlined />} \n            onClick={() => showEditModal(record)}\n          />\n          <Popconfirm\n            title=\"确定要删除此角色吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button \n              type=\"text\" \n              danger \n              icon={<DeleteOutlined />} \n            />\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <AdminLayout>\n      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>\n        <h1>角色管理</h1>\n        <Button type=\"primary\" icon={<PlusOutlined />} onClick={showCreateModal}>\n          新增角色\n        </Button>\n      </div>\n      \n      <div style={{ marginBottom: 16 }}>\n        <Input.Search\n          placeholder=\"搜索角色\"\n          allowClear\n          enterButton={<SearchOutlined />}\n          onSearch={(value) => console.log(value)}\n          style={{ width: 300 }}\n        />\n      </div>\n      \n      <Table\n        columns={columns}\n        dataSource={roles}\n        rowKey=\"id\"\n        loading={loading}\n        pagination={{\n          current: currentPage,\n          pageSize: pageSize,\n          total: total,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total) => `共 ${total} 条记录`,\n        }}\n        onChange={handleTableChange}\n      />\n      \n      <Modal\n        title={isEditing ? '编辑角色' : '新增角色'}\n        open={isModalVisible}\n        onOk={handleSubmit}\n        onCancel={handleCancel}\n        maskClosable={false}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"角色名称\"\n            rules={[{ required: true, message: '请输入角色名称' }]}\n          >\n            <Input placeholder=\"请输入角色名称\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"description\"\n            label=\"描述\"\n            rules={[{ required: true, message: '请输入描述' }]}\n          >\n            <Input.TextArea placeholder=\"请输入描述\" rows={3} />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"menuIds\"\n            label=\"菜单权限\"\n            rules={[{ required: true, message: '请选择菜单权限' }]}\n          >\n            <Tree\n              checkable\n              treeData={treeData}\n              defaultExpandAll\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </AdminLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA;;;AATA;;;;;;AAWA,wBAAwB;AACxB,MAAM,yBAAyB,CAAC;IAC9B,OAAO,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;YACxB,OAAO,KAAK,IAAI;YAChB,KAAK,KAAK,EAAE;YACZ,UAAU,KAAK,QAAQ,GAAG,uBAAuB,KAAK,QAAQ,IAAI;QACpE,CAAC;AACH;AAEe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,SAAS;IACT,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,qIAAA,CAAA,cAAW,CAAC,QAAQ,CAAC;gBAAE,MAAM;gBAAa;YAAS;YAC1E,SAAS,SAAS,IAAI;YACtB,SAAS,SAAS,KAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,QAAQ;IACR,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,qIAAA,CAAA,cAAW,CAAC,WAAW;YAC9C,YAAY;YACZ,YAAY,uBAAuB;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,YAAY;YAC1B,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,MAAM;IACN,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;YACA;QACF;8BAAG;QAAC;QAAa;KAAS;IAE1B,SAAS;IACT,MAAM,oBAAoB,CAAC;QACzB,eAAe,WAAW,OAAO;QACjC,YAAY,WAAW,QAAQ;IACjC;IAEA,YAAY;IACZ,MAAM,kBAAkB;QACtB,aAAa;QACb,eAAe;QACf,KAAK,WAAW;QAChB,kBAAkB;IACpB;IAEA,YAAY;IACZ,MAAM,gBAAgB,CAAC;QACrB,aAAa;QACb,eAAe;QACf,KAAK,cAAc,CAAC;YAClB,GAAG,IAAI;QACT;QACA,kBAAkB;IACpB;IAEA,QAAQ;IACR,MAAM,eAAe;QACnB,kBAAkB;QAClB,KAAK,WAAW;IAClB;IAEA,SAAS;IACT,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,SAAS,MAAM,KAAK,cAAc;YAExC,IAAI,aAAa,aAAa;gBAC5B,OAAO;gBACP,MAAM,qIAAA,CAAA,cAAW,CAAC,UAAU,CAAC;oBAC3B,IAAI,YAAY,EAAE;oBAClB,GAAG,MAAM;gBACX;gBACA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB,OAAO;gBACL,OAAO;gBACP,MAAM,qIAAA,CAAA,cAAW,CAAC,UAAU,CAAC;gBAC7B,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB;YAEA,kBAAkB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,SAAS;IACT,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,qIAAA,CAAA,cAAW,CAAC,UAAU,CAAC;YAC7B,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,QAAQ;IACR,MAAM,UAA6B;QACjC;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,wBACP;;wBACG,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA;4BACvB,WAAW;4BACX,MAAM,eAAe,CAAC,OAAe;gCACnC,KAAK,MAAM,QAAQ,MAAO;oCACxB,IAAI,KAAK,EAAE,KAAK,IAAI;wCAClB,OAAO,KAAK,IAAI;oCAClB;oCACA,IAAI,KAAK,QAAQ,EAAE;wCACjB,MAAM,OAAO,aAAa,KAAK,QAAQ,EAAE;wCACzC,IAAI,MAAM,OAAO;oCACnB;gCACF;gCACA,OAAO;4BACT;4BAEA,MAAM,WAAW,aAAa,UAAU;4BACxC,OAAO,yBAAW,6LAAC,+KAAA,CAAA,MAAG;0CAAe;+BAAT;;;;uCAA2B;wBACzD;wBACC,QAAQ,MAAM,GAAG,mBAAK,6LAAC,+KAAA,CAAA,MAAG;;gCAAC;gCAAE,QAAQ,MAAM,GAAG;;;;;;;;;QAGrD;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,6LAAC,mMAAA,CAAA,QAAK;oBAAC,MAAK;;sCACV,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4BACnB,SAAS,IAAM,cAAc;;;;;;sCAE/B,6LAAC,6LAAA,CAAA,aAAU;4BACT,OAAM;4BACN,WAAW,IAAM,aAAa,OAAO,EAAE;4BACvC,QAAO;4BACP,YAAW;sCAEX,cAAA,6LAAC,qMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,MAAM;gCACN,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;QAK/B;KACD;IAED,qBACE,6LAAC,8IAAA,CAAA,UAAW;;0BACV,6LAAC;gBAAI,OAAO;oBAAE,SAAS;oBAAQ,gBAAgB;oBAAiB,cAAc;gBAAG;;kCAC/E,6LAAC;kCAAG;;;;;;kCACJ,6LAAC,qMAAA,CAAA,SAAM;wBAAC,MAAK;wBAAU,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wBAAK,SAAS;kCAAiB;;;;;;;;;;;;0BAK3E,6LAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAG;0BAC7B,cAAA,6LAAC,mLAAA,CAAA,QAAK,CAAC,MAAM;oBACX,aAAY;oBACZ,UAAU;oBACV,2BAAa,6LAAC,yNAAA,CAAA,iBAAc;;;;;oBAC5B,UAAU,CAAC,QAAU,QAAQ,GAAG,CAAC;oBACjC,OAAO;wBAAE,OAAO;oBAAI;;;;;;;;;;;0BAIxB,6LAAC,mLAAA,CAAA,QAAK;gBACJ,SAAS;gBACT,YAAY;gBACZ,QAAO;gBACP,SAAS;gBACT,YAAY;oBACV,SAAS;oBACT,UAAU;oBACV,OAAO;oBACP,iBAAiB;oBACjB,iBAAiB;oBACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;gBACxC;gBACA,UAAU;;;;;;0BAGZ,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAO,YAAY,SAAS;gBAC5B,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,cAAc;gBACd,OAAO;0BAEP,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBACH,MAAM;oBACN,QAAO;;sCAEP,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,6LAAC,mLAAA,CAAA,QAAK;gCAAC,aAAY;;;;;;;;;;;sCAGrB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAQ;6BAAE;sCAE7C,cAAA,6LAAC,mLAAA,CAAA,QAAK,CAAC,QAAQ;gCAAC,aAAY;gCAAQ,MAAM;;;;;;;;;;;sCAG5C,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,6LAAC,iLAAA,CAAA,OAAI;gCACH,SAAS;gCACT,UAAU;gCACV,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9B;GAnQwB;;QAWP,iLAAA,CAAA,OAAI,CAAC;;;KAXE", "debugId": null}}]}