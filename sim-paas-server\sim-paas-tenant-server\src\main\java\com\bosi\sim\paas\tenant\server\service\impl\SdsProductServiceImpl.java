package com.bosi.sim.paas.tenant.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.dao.mapper.SdsProductMapper;
import com.bosi.sim.paas.dao.model.sds.SdsProduct;
import com.bosi.sim.paas.tenant.server.service.SdsProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SdsProductServiceImpl implements SdsProductService {

    @Autowired
    private SdsProductMapper productMapper;

    @Override
    public Page<SdsProduct> page(SdsProduct product, Integer pageNum, Integer pageSize) {
        Page<SdsProduct> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<SdsProduct> queryWrapper = new LambdaQueryWrapper<>();
        // 添加查询条件
        return productMapper.selectPage(page, queryWrapper);
    }

    @Override
    public SdsProduct getById(String id) {
        return productMapper.selectById(id);
    }

    @Override
    public boolean create(SdsProduct product) {
        return productMapper.insert(product) > 0;
    }

    @Override
    public boolean update(SdsProduct product) {
        return productMapper.updateById(product) > 0;
    }

    @Override
    public boolean delete(String id) {
        return productMapper.deleteById(id) > 0;
    }
}