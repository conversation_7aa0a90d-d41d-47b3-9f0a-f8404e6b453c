package com.bosi.sim.paas.dao.model.tms;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import lombok.Data;

@Data
@TableName("tms_notify_record")
public class TdsNotifyRecord extends BaseEntity {

    private String title;

    private String content;

    private String tenantId;

    private Boolean whetherRead;

    @TableField(exist = false)
    private String tenantName;

}
