package com.bosi.sim.paas.tenant.server.controller;

import com.alibaba.fastjson2.JSONObject;
import com.bosi.sim.paas.common.core.exception.CommonResult;
import com.bosi.sim.paas.common.core.api.SystemCode;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.common.security.auth.CacheTokenUser;
import com.bosi.sim.paas.common.security.auth.CurrentAuthorization;
import com.bosi.sim.paas.common.security.service.TokenService;
import com.bosi.sim.paas.dao.model.tds.TdsRole;
import com.bosi.sim.paas.dao.model.tds.TdsUser;
import com.bosi.sim.paas.dao.pojo.param.LoginParam;
import com.bosi.sim.paas.tenant.server.enums.DistributorTokenExtendKeyEnum;
import com.bosi.sim.paas.tenant.server.service.TmsMenuService;
import com.bosi.sim.paas.tenant.server.service.TmsRoleService;
import com.bosi.sim.paas.tenant.server.service.TmsUserLoginService;
import com.bosi.sim.paas.tenant.server.service.TmsUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 后台用户管理Controller
 */
@RestController
@Api(tags = "TmsUserLoginController", value = "用户登录")
@RequestMapping("/tms/userLogin")
public class TmsUserLoginController {

    @Autowired
    private TmsUserLoginService tmsUserLoginService;

    @Autowired
    private TmsUserService userService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private TmsMenuService menuService;

    @Autowired
    private TmsRoleService roleService;

    @ApiOperation(value = "登录以后返回token")
    @PostMapping("/login")
    public CommonResult login(@Validated @RequestBody LoginParam loginParam) {
        TdsUser user = tmsUserLoginService.login(loginParam.getUsername(), loginParam.getPassword());
        Set<String> permissions = menuService.getMenuPermission(user);
        CacheTokenUser cacheTokenUser = new CacheTokenUser();
        JSONObject extendJson = new JSONObject();
        extendJson.put(DistributorTokenExtendKeyEnum.DISTRIBUTORID_KEY.getKey(), user.getDistributorId());
        cacheTokenUser.setUserId(user.getId());
        cacheTokenUser.setUsername(user.getUserName());
        cacheTokenUser.setExtendJson(extendJson);
        cacheTokenUser.setPerms(permissions);
        String token = tokenService.generateToken(cacheTokenUser);
        Map<String, String> tokenMap = new HashMap<>();
        tokenMap.put("accessToken", token);
        return CommonResult.success(tokenMap);
    }

    @ApiOperation(value = "刷新token")
    @RequestMapping(value = "/refreshToken")
    @PostMapping
    public CommonResult refreshToken() {
        tokenService.refreshToken(CurrentAuthorization.getUsername());
        return CommonResult.success();
    }

    @ApiOperation(value = "登出功能")
    @DeleteMapping("/logout")
    public CommonResult logout() {
        String userName = tokenService.parseUserNameFromToken(CurrentAuthorization.getCurrentToken());
        tmsUserLoginService.logout(userName);
        tokenService.disableToken(userName);
        return CommonResult.success();
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/current")
    public CommonResult<JSONObject> current() {
        TdsUser sysUser = userService.selectUserByUserName(CurrentAuthorization.getUsername());
        if (StringUtils.isNull(sysUser)) {
            throw BizException.build(SystemCode.RESOURCE_NOTFOUND);
        }
        TdsRole role = roleService.selectRoleById(sysUser.getRoleId());
        // 权限集合
        Set<String> permissions = menuService.getMenuPermission(sysUser);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("user", sysUser);
        jsonObject.put("role", role);
        jsonObject.put("permissions", permissions);
        return CommonResult.success(jsonObject);
    }


}
