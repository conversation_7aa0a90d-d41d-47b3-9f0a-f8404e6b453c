<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bosi.sim.paas.admin.server.dao.TmsUserAccountDao">

    <select id="page" resultType="com.bosi.sim.paas.dao.model.tds.TdsTenantAccount">
        select
            a.*,d.distributor_name,u.user_name
        from
            tms_user_account as a
        left join
            tms_distributor as d on d.id = a.distributor_id
        left join
            tms_user as u on u.id = a.user_id
        <where>
            <if test="true">
                and a.whether_delete = false
            </if>
            <if test="params.accountType != null and params.accountType != ''">
                and a.account_type = #{params.accountType}
            </if>
            <if test="params.whetherEnable != null">
                and a.whether_enable = #{params.whetherEnable}
            </if>
            <if test="params.userName != null and params.userName != ''">
                and u.user_name like concat('%', #{params.userName}, '%')
            </if>
            <if test="params.distributorName != null and params.distributorName != ''">
                and d.distributor_name like concat('%', #{params.distributorName}, '%')
            </if>
        </where>
    </select>

    <select id="listAccountByDistributorId" resultType="com.bosi.sim.paas.dao.model.tds.TdsTenantAccount">
        select
            a.*,d.distributor_name,u.user_name
        from
            tms_user_account as a
        left join
            tms_distributor as d on d.id = a.distributor_id
        left join
            tms_user as u on u.id = a.user_id
        where
            a.whether_delete = false
        and
            d.id = #{distributorId}
    </select>

    <update id="increaseAvailableAmount">
        update
            tms_user_account
        set
            available_balance = available_balance + #{amount}
        where
            whether_delete = false
        and
            id = #{id}
    </update>

    <update id="decreaseIntransitBalance">
        update
            tms_user_account
        set
            intransit_balance = intransit_balance - #{amount}
        where
            whether_delete = false
          and
            intransit_balance - #{amount} > 0
          and
            id = #{id}
    </update>

    <update id="increaseIntransitBalance">
        update
            tms_user_account
        set
            intransit_balance = intransit_balance + #{amount}
        where
            whether_delete = false
          and
            id = #{id}
    </update>

</mapper>
