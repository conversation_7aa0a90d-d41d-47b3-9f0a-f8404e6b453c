package com.bosi.sim.paas.dao.enums.tds;


public enum TdsAccountTypeEnum {

    PERSON(1, "个人"),

    COMPANY(2, "企业");

    private Integer accountType;

    private String desc;

    TdsAccountTypeEnum(Integer accountType, String desc) {
        this.accountType = accountType;
        this.desc = desc;
    }

    public Integer getAccountType() {
        return accountType;
    }

    public String getDesc() {
        return desc;
    }
}
