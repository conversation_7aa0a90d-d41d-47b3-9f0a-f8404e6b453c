package com.bosi.sim.paas.tenant.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.api.SystemCode;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.dao.enums.tms.TmsAccountTypeEnum;
import com.bosi.sim.paas.dao.mapper.tms.TmsUserAccountMapper;
import com.bosi.sim.paas.dao.mapper.tms.TmsUserMapper;
import com.bosi.sim.paas.dao.mapper.tms.TmsUserRoleMapper;
import com.bosi.sim.paas.dao.model.tds.TdsRole;
import com.bosi.sim.paas.dao.model.tds.TdsUser;
import com.bosi.sim.paas.dao.model.tds.TdsTenantAccount;
import com.bosi.sim.paas.dao.model.tds.TdsUserRole;
import com.bosi.sim.paas.tenant.server.dao.TmsRoleDao;
import com.bosi.sim.paas.tenant.server.dao.TmsUserDao;
import com.bosi.sim.paas.tenant.server.service.TmsUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;

/**
 * 用户 业务层处理
 */
@Service
public class TmsUserServiceImpl implements TmsUserService {

    @Autowired
    private TmsUserMapper userMapper;

    @Autowired
    private TmsUserDao userDao;

    @Autowired
    private TmsRoleDao roleDao;

    @Autowired
    private TmsUserRoleMapper userRoleMapper;

    @Autowired
    private TmsUserAccountMapper userAccountMapper;

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public CommonPage<TdsUser> page(Page<TdsUser> page, TdsUser user) {
        return CommonPage.restPage(userDao.selectUserList(page, user));
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public TdsUser selectUserByUserName(String userName) {
        return userDao.selectUserByUserName(userName);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public TdsUser selectUserById(String userId) {
        return userDao.selectUserById(userId);
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName) {
        TdsRole tmsRole = roleDao.selectRolesByUserName(userName);
        if (tmsRole == null) {
            return StringUtils.EMPTY;
        }
        return tmsRole.getRoleName();
    }

    /**
     * 校验用户名称是否唯一
     *
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(String userId, String userName) {
        TdsUser info = userDao.checkUserNameUnique(userName);
        if (StringUtils.isNotNull(info) && !info.getId().equals(userId)) {
            return false;
        }
        return true;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @return
     */
    @Override
    public boolean checkPhoneUnique(String userId, String phone) {
        TdsUser info = userDao.checkPhoneUnique(phone);
        if (StringUtils.isNotNull(info) && !info.getId().equals(userId)) {
            return false;
        }
        return true;
    }

    /**
     * 校验email是否唯一
     *
     * @return
     */
    @Override
    public boolean checkEmailUnique(String userId, String email) {
        TdsUser info = userDao.checkEmailUnique(email);
        if (StringUtils.isNotNull(info) && !info.getId().equals(userId)) {
            return false;
        }
        return true;
    }

    /**
     * 校验用户是否允许操作
     */
    @Override
    public void checkUserAllowed(String userId) {
        TdsUser tmsUser = userDao.selectUserById(userId);
        if (tmsUser == null) {
            throw BizException.build(SystemCode.RESOURCE_NOTFOUND);
        }
        if (tmsUser.getWhetherAdmin()) {
            throw BizException.build(SystemCode.FORBIDDEN_OPERATION);
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertUser(TdsUser user) {
        // 新增用户信息
        int rows = userMapper.insert(user);
        // 新增用户与角色管理
        insertUserRole(user.getId(), user.getRoleId());
        TdsTenantAccount tmsUserAccount = new TdsTenantAccount();
        tmsUserAccount.setDistributorId(user.getDistributorId());
        tmsUserAccount.setUserId(user.getId());
        tmsUserAccount.setAccountType(TmsAccountTypeEnum.PERSON.getAccountType());
        userAccountMapper.insert(tmsUserAccount);
        return rows;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateUser(TdsUser user) {
        String userId = user.getId();
        // 删除用户与角色关联
        LambdaQueryWrapper<TdsUserRole> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(TdsUserRole::getUserId, userId);
        userRoleMapper.delete(deleteWrapper);
        // 新增用户与角色管理
        insertUserRole(user.getId(), user.getRoleId());
        return userMapper.updateById(user);
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(TdsUser user) {
        return userMapper.updateById(user);
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(TdsUser user) {
        return userMapper.updateById(user);
    }

    /**
     * 新增用户角色信息
     *
     * @param userId 用户ID
     */
    public void insertUserRole(String userId, String roleId) {
        if (StringUtils.isNotNull(roleId)) {
            // 新增用户与角色管理
            TdsUserRole ur = new TdsUserRole();
            ur.setUserId(userId);
            ur.setRoleId(roleId);
            userRoleMapper.insert(ur);
        }

    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserByIds(String[] userIds) {
        for (String userId : userIds) {
            TdsUser tmsUser = new TdsUser();
            tmsUser.setId(userId);
            checkUserAllowed(userId);
        }
        // 删除用户与角色关联
        LambdaQueryWrapper<TdsUserRole> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.in(TdsUserRole::getUserId, Arrays.asList(userIds));
        userRoleMapper.delete(deleteWrapper);     // 删除用户与岗位关联
        return userMapper.deleteBatchIds(Arrays.asList(userIds));
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(TdsUser user) {
        return userMapper.updateById(user);
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar) {
        return userDao.updateUserAvatar(userName, avatar) > 0;
    }

    @Override
    public int resetUserPwd(String userName, String password) {
        return userDao.resetUserPwd(userName, password);
    }


}
