package com.bosi.sim.paas.dao.model.tds;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import lombok.Data;

import java.util.Set;

/**
 * 角色表 ads_role
 */
@Data
@TableName("tds_role")
public class TdsRole extends BaseEntity {

    /**
     * 角色ID
     */
    /**
     * 角色名称
     */
    private String roleName;


    private String tenantId;

    /**
     * 角色状态（0正常 1停用）
     */
    private Boolean whetherEnable;

    private Boolean whetherAdmin;

    /**
     * 菜单组
     */
    @TableField(exist = false)
    private String[] menuIds;

    @TableField(exist = false)
    private String distributorName;

    /**
     * 角色菜单权限
     */
    @TableField(exist = false)
    private Set<String> permissions;

}
