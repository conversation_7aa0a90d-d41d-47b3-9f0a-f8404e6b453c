package com.bosi.sim.paas.tenant.server.controller;

import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.exception.CommonResult;
import com.bosi.sim.paas.common.core.utils.PageUtil;
import com.bosi.sim.paas.common.security.annotation.RequiresPermissions;
import com.bosi.sim.paas.common.security.auth.CurrentAuthorization;
import com.bosi.sim.paas.dao.model.tds.TdsTenantAccount;
import com.bosi.sim.paas.tenant.server.enums.DistributorTokenExtendKeyEnum;
import com.bosi.sim.paas.tenant.server.service.TmsUserAccountService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@RestController
@Api(tags = "TmsUserAccountController", value = "分销账户管理")
@RequestMapping("/tms/user/account")
public class TmsUserAccountController {
    @Autowired
    private TmsUserAccountService userAccountService;

    @RequiresPermissions("tms:user:account:page")
    @GetMapping("/page")
    public CommonResult page(TdsTenantAccount tmsUserAccount,
                             @RequestParam(value = "pageSize") Integer pageSize,
                             @RequestParam(value = "pageNum") Integer pageNum) {
        tmsUserAccount.setDistributorId(CurrentAuthorization.getStringFromExtendJson(DistributorTokenExtendKeyEnum.DISTRIBUTORID_KEY.getKey()));
        CommonPage<TdsTenantAccount> page = userAccountService.page(PageUtil.buildPage(pageNum, pageSize), tmsUserAccount);
        return CommonResult.success(page);
    }

}
