package com.bosi.sim.paas.dao.model.sds;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("sds_product_operate_log")
public class SdsProductOperateLog extends BaseEntity {
    private String productId;

    private Integer operateType;

    private String oldProductInfo;

    private String editProductInfo;

    @ApiModelProperty(value = "操作人")
    private String operateMan;

}
