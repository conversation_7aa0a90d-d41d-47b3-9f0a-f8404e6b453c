package com.bosi.sim.paas.admin.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.api.SystemCode;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.IdUtils;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.dao.mapper.ads.AdsUserMapper;
import com.bosi.sim.paas.dao.mapper.ads.AdsUserRoleMapper;
import com.bosi.sim.paas.dao.model.ads.AdsRole;
import com.bosi.sim.paas.dao.model.ads.AdsUser;
import com.bosi.sim.paas.dao.model.ads.AdsUserRole;
import com.bosi.sim.paas.admin.server.dao.UmsRoleDao;
import com.bosi.sim.paas.admin.server.dao.UmsUserDao;
import com.bosi.sim.paas.admin.server.dao.UmsUserRoleDao;
import com.bosi.sim.paas.admin.server.service.UmsUserService;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户 业务层处理
 */
@Service
public class UmsUserServiceImpl implements UmsUserService {

    @Autowired
    private AdsUserMapper userMapper;

    @Autowired
    private UmsUserDao userDao;

    @Autowired
    private UmsRoleDao roleDao;

    @Autowired
    private AdsUserRoleMapper userRoleMapper;

    @Autowired
    private UmsUserRoleDao userRoleDao;

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public CommonPage<AdsUser> page(Page<AdsUser> page, AdsUser user) {
        return CommonPage.restPage(userDao.selectUserList(page, user));
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public CommonPage<AdsUser> selectAllocatedList(Page<AdsUser> page, AdsUser user) {
        return CommonPage.restPage(userDao.selectAllocatedList(page, user));
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public CommonPage<AdsUser> selectUnallocatedList(Page<AdsUser> page, AdsUser user) {
        return CommonPage.restPage(userDao.selectUnallocatedList(page, user));
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public AdsUser selectUserByUserName(String userName) {
        return userDao.selectUserByUserName(userName);
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public AdsUser selectUserById(String userId) {
        return userDao.selectUserById(userId);
    }

    /**
     * 查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(String userName) {
        List<AdsRole> list = roleDao.selectRolesByUserName(userName);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return list.stream().map(AdsRole::getRoleName).collect(Collectors.joining(","));
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(AdsUser user) {
        String userId = StringUtils.isNull(user.getId()) ? "" : user.getId();
        AdsUser info = userDao.checkUserNameUnique(user.getUserName());
        return StringUtils.isNull(info) || info.getId().equals(userId);
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     */
    @Override
    public boolean checkPhoneUnique(AdsUser user) {
        String userId = StringUtils.isNull(user.getId()) ? "" : user.getId();
        AdsUser info = userDao.checkPhoneUnique(user.getPhone());
        return StringUtils.isNull(info) || info.getId().equals(userId);
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     */
    @Override
    public boolean checkEmailUnique(AdsUser user) {
        String userId = StringUtils.isNull(user.getId()) ? "" : user.getId();
        AdsUser info = userDao.checkEmailUnique(user.getEmail());
        return StringUtils.isNull(info) || info.getId().equals(userId);
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertUser(AdsUser user) {
        // 新增用户信息
        int rows = userMapper.insert(user);
        // 新增用户与角色管理
        insertUserRole(user);
        return rows;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateUser(AdsUser user) {
        AdsUser umsUser = userMapper.selectById(user.getId());
        if (umsUser == null) {
            throw BizException.build(SystemCode.RESOURCE_NOTFOUND);
        }
        if (umsUser.getWhetherAdmin()) {
            throw BizException.build(SystemCode.FORBIDDEN_OPERATION);
        }
        String userId = user.getId();
        // 删除用户与角色关联
        LambdaQueryWrapper<AdsUserRole> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(AdsUserRole::getUserId, userId);
        userRoleMapper.delete(deleteWrapper);
        // 新增用户与角色管理
        insertUserRole(user);
        return userMapper.updateById(user);
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertUserAuth(String userId, String[] roleIds) {
        LambdaQueryWrapper<AdsUserRole> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(AdsUserRole::getUserId, userId);
        userRoleMapper.delete(deleteWrapper);
        insertUserRole(userId, roleIds);
    }

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserStatus(AdsUser user) {
        AdsUser umsUser = userMapper.selectById(user.getId());
        if (umsUser == null) {
            throw BizException.build(SystemCode.RESOURCE_NOTFOUND);
        }
        if (umsUser.getWhetherAdmin()) {
            throw BizException.build(SystemCode.FORBIDDEN_OPERATION);
        }
        return userMapper.updateById(user);
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(AdsUser user) {
        return userMapper.updateById(user);
    }

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(String userName, String avatar) {
        return userDao.updateUserAvatar(userName, avatar) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int resetPwd(AdsUser user) {
        AdsUser umsUser = userMapper.selectById(user.getId());
        if (umsUser == null) {
            throw BizException.build(SystemCode.RESOURCE_NOTFOUND);
        }
        if (umsUser.getWhetherAdmin()) {
            throw BizException.build(SystemCode.FORBIDDEN_OPERATION);
        }
        return userMapper.updateById(user);
    }

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(String userName, String password) {
        return userDao.resetUserPwd(userName, password);
    }

    /**
     * 新增用户角色信息
     *
     * @param user 用户对象
     */
    public void insertUserRole(AdsUser user) {
        this.insertUserRole(user.getId(), user.getRoleIds());
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserRole(String userId, String[] roleIds) {
        if (StringUtils.isNotEmpty(roleIds)) {
            List<AdsUserRole> list = Lists.newArrayList();
            // 新增用户与角色管理
            for (String roleId : roleIds) {
                AdsUserRole ur = new AdsUserRole();
                ur.setId(IdUtils.fastSimpleUUID());
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                list.add(ur);
            }
            userRoleDao.insertList(list);
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserById(String userId) {
        // 删除用户与角色关联
        AdsUser umsUser = userMapper.selectById(userId);
        if (umsUser == null) {
            return 1;
        }
        if (umsUser.getWhetherAdmin()) {
            throw BizException.build(SystemCode.FORBIDDEN_OPERATION);
        }
        LambdaQueryWrapper<AdsUserRole> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(AdsUserRole::getUserId, userId);
        userRoleMapper.delete(deleteWrapper);
        return userMapper.deleteById(userId);
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserByIds(String[] userIds) {
        LambdaQueryWrapper<AdsUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AdsUser::getId, Arrays.asList(userIds));
        List<AdsUser> users = userMapper.selectList(queryWrapper);
        users.forEach(user -> {
            if (user.getWhetherAdmin()) {
                throw BizException.build(SystemCode.FORBIDDEN_OPERATION);
            }
        });
        // 删除用户与角色关联
        LambdaQueryWrapper<AdsUserRole> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.in(AdsUserRole::getUserId, Arrays.asList(userIds));
        userRoleMapper.delete(deleteWrapper);
        // 删除用户与岗位关联
        return userMapper.deleteBatchIds(Arrays.asList(userIds));
    }

}
