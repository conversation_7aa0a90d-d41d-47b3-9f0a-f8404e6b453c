package com.bosi.sim.paas.dao.model.tms;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
@TableName("tms_user_login_log")
public class TdsUserLoginLog extends BaseEntity {

    /**
     * 用户账号
     */
    private String loginName;

    /**
     * 地址
     */
    private String ipaddr;

    /**
     * 状态 0成功 1失败
     */
    private Boolean whetherSuccess;

    /**
     * 描述
     */
    private String msg;

    /**
     * 访问时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date loginTime;

    private String tenantId;

}
