<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bosi.sim.paas.tenant.server.dao.TmsUserAccountDao">

    <select id="page" resultType="com.bosi.sim.paas.dao.model.tds.TdsTenantAccount">
        select
            a.*,d.distributor_name,u.user_name
        from
            tms_user_account as a
        left join
            tms_distributor as d on d.id = a.distributor_id
        left join
            tms_user as u on u.id = a.user_id
        <where>
            <if test="true">
                and a.whether_delete = false
            </if>
            <if test="params.accountType != null and params.accountType != ''">
                and a.account_type = #{params.accountType}
            </if>
            <if test="params.whetherEnable != null and params.whetherEnable != ''">
                and a.whether_enable = #{params.whetherEnable}
            </if>
            <if test="params.distributorId != null and params.distributorId != ''">
                and a.distributor_id = #{params.distributorId}
            </if>
            <if test="params.userName != null and params.userName != ''">
                and u.user_name = like concat('%', #{params.userName}, '%')
            </if>
        </where>
    </select>

    <select id="listByIds" resultType="com.bosi.sim.paas.dao.model.tds.TdsTenantAccount">
        select
            a.*,u.user_name
        from
            tms_user_account as a
        left join
            tms_user as u on u.id = a.user_id
        where
            a.whether_delete = false
        and
            a.id in
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
    </select>

</mapper>
