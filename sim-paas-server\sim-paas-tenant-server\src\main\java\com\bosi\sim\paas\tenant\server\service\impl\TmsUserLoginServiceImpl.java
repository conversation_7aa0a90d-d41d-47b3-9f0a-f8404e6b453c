package com.bosi.sim.paas.tenant.server.service.impl;

import com.bosi.sim.paas.common.core.api.BizCode;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.MessageUtils;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.common.redis.service.RedisService;
import com.bosi.sim.paas.common.security.auth.CurrentAuthorization;
import com.bosi.sim.paas.common.security.util.PasswordUtils;
import com.bosi.sim.paas.dao.mapper.tms.TmsUserLoginLogMapper;
import com.bosi.sim.paas.dao.model.tds.TdsUser;
import com.bosi.sim.paas.dao.model.tds.TdsUserLoginLog;
import com.bosi.sim.paas.tenant.server.dao.TmsUserDao;
import com.bosi.sim.paas.tenant.server.enums.DistributorRedisKeyEnum;
import com.bosi.sim.paas.tenant.server.service.TmsUserLoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 登录校验方法
 */
@Service
public class TmsUserLoginServiceImpl implements TmsUserLoginService {

    /**
     * 用户名长度限制
     */
    private static final int USERNAME_MIN_LENGTH = 2;

    private static final int USERNAME_MAX_LENGTH = 20;

    /**
     * 密码长度限制
     */
    private static final int PASSWORD_MIN_LENGTH = 5;

    private static final int PASSWORD_MAX_LENGTH = 20;

    private static final int MAX_RETRY_COUNT = 5;

    @Autowired
    private RedisService redisService;

    @Autowired
    private TmsUserLoginLogMapper userLoginLogMapper;

    @Autowired
    private TmsUserDao tmsUserDao;

    /**
     * 登录
     */
    public TdsUser login(String username, String password) {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password)) {
            this.saveLog(username, false, null, MessageUtils.getMessage(BizCode.USER_LOGIN_PARAM_REQUIRED));
            throw BizException.build(BizCode.USER_LOGIN_PARAM_REQUIRED);
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < PASSWORD_MIN_LENGTH
                || password.length() > PASSWORD_MAX_LENGTH) {
            this.saveLog(username, false, null, MessageUtils.getMessage(BizCode.USER_LOGIN_PASSWORD_ILLEGAL));
            throw BizException.build(BizCode.USER_LOGIN_PASSWORD_ILLEGAL);
        }
        // 用户名不在指定范围内 错误
        if (username.length() < USERNAME_MIN_LENGTH
                || username.length() > USERNAME_MAX_LENGTH) {
            this.saveLog(username, false, null, MessageUtils.getMessage(BizCode.USER_LOGIN_ACCOUNT_ILLEGAL));
            throw BizException.build(BizCode.USER_LOGIN_ACCOUNT_ILLEGAL);
        }
        // 查询用户信息
        TdsUser userResult = tmsUserDao.selectUserByUserName(username);
        if (StringUtils.isNull(userResult)) {
            this.saveLog(username, false, null, MessageUtils.getMessage(BizCode.USER_LOGIN_NOT_FOUND));
            throw BizException.build(BizCode.USER_LOGIN_NOT_FOUND);
        }
        if (!userResult.getWhetherEnable()) {
            this.saveLog(username, false, userResult.getDistributorId(), MessageUtils.getMessage(BizCode.USER_ACCOUNT_DISABLE));
            throw BizException.build(BizCode.USER_ACCOUNT_DISABLE);
        }
        this.validate(userResult, password);
        this.saveLog(username, true, userResult.getDistributorId(), MessageUtils.getMessage(BizCode.USER_LOGIN_SUCCESS));
        return userResult;
    }

    public void logout(String loginName) {
        TdsUser tmsUser = tmsUserDao.selectUserByUserName(loginName);
        this.saveLog(loginName, true, tmsUser == null ? null : tmsUser.getDistributorId(),
                MessageUtils.getMessage(BizCode.USER_LOGOUT_SUCCESS));
    }

    public void saveLog(String username, Boolean whetherSuccess, String distributorId, String message) {
        TdsUserLoginLog logininfor = new TdsUserLoginLog();
        logininfor.setLoginName(username);
        logininfor.setIpaddr(CurrentAuthorization.getReqIp());
        logininfor.setMsg(message);
        logininfor.setWhetherSuccess(whetherSuccess);
        logininfor.setLoginTime(new Date());
        logininfor.setDistributorId(distributorId);
        userLoginLogMapper.insert(logininfor);
    }


    public void validate(TdsUser user, String password) {
        String username = user.getUserName();

        Integer retryCount = redisService.get(DistributorRedisKeyEnum.PASSWORD_ERROR, username);

        if (retryCount == null) {
            retryCount = 0;
        }

        if (retryCount >= MAX_RETRY_COUNT) {
            String errMsg = MessageUtils.getMessage(BizCode.USER_LOGIN_ERROR_LOCK, MAX_RETRY_COUNT,
                    DistributorRedisKeyEnum.PASSWORD_ERROR.expireIn());
            this.saveLog(username, false, user.getDistributorId(), errMsg);
            throw BizException.build(BizCode.USER_LOGIN_ERROR_LOCK, MAX_RETRY_COUNT, DistributorRedisKeyEnum.PASSWORD_ERROR.expireIn());
        }

        if (!PasswordUtils.matchesPassword(password, user.getPassword())) {
            retryCount = retryCount + 1;
            this.saveLog(username, false, user.getDistributorId(), MessageUtils.getMessage(BizCode.USER_LOGIN_ERROR_TIMES, retryCount));
            redisService.set(DistributorRedisKeyEnum.PASSWORD_ERROR, username, retryCount, DistributorRedisKeyEnum.PASSWORD_ERROR.expireIn(),
                    DistributorRedisKeyEnum.PASSWORD_ERROR.timeUnit());
            throw BizException.build(BizCode.USER_LOGIN_ERROR_TIMES, retryCount);
        } else {
            redisService.del(DistributorRedisKeyEnum.PASSWORD_ERROR, username);
        }
    }

    public void deleteLock(String username) {
        redisService.del(DistributorRedisKeyEnum.PASSWORD_ERROR, username);
    }
}
