<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bosi.sim.paas.tenant.server.dao.TmsNotifyRecordDao">

    <select id="page" resultType="com.bosi.sim.paas.dao.model.tds.TdsNotifyRecord">
        select
            a.*,b.title,b.content
        from
            tms_notify_record as a
        left join
            tms_notify as b on a.notify_id = b.id
        <where>
            <if test="true">
                and a.whether_delete = false
            </if>
            <if test="params.distributorId != null and params.distributorId != ''">
                and a.distributor_id = #{params.distributorId}
            </if>
            <if test="params.querySearchValue != null and params.querySearchValue != ''">
                and (b.title like concat('%', #{params.querySearchValue}, '%') or b.content like concat('%', #{params.querySearchValue}, '%'))
            </if>
        </where>
    </select>

</mapper>
