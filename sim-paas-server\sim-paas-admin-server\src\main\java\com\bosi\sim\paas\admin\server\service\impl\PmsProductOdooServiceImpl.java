package com.bosi.sim.paas.admin.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bosi.sim.paas.common.core.api.BizCode;
import com.bosi.sim.paas.common.core.api.SystemCode;
import com.bosi.sim.paas.common.core.context.SpringContext;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.dao.enums.sds.SdsAttributeTypeClassifyEnum;
import com.bosi.sim.paas.dao.enums.sds.SdsAttributeTypeSelectTypeEnum;
import com.bosi.sim.paas.dao.enums.sds.SdsPromotionTypeEnum;
import com.bosi.sim.paas.dao.mapper.pms.PmsAttributeMapper;
import com.bosi.sim.paas.dao.mapper.pms.PmsAttributeTypeMapper;
import com.bosi.sim.paas.dao.mapper.pms.PmsBrandMapper;
import com.bosi.sim.paas.dao.mapper.pms.PmsCategoryMapper;
import com.bosi.sim.paas.dao.mapper.pms.PmsProductAttributeTypeMapper;
import com.bosi.sim.paas.dao.mapper.pms.PmsProductFullReductionMapper;
import com.bosi.sim.paas.dao.mapper.pms.PmsProductLadderMapper;
import com.bosi.sim.paas.dao.mapper.pms.PmsProductMapper;
import com.bosi.sim.paas.dao.mapper.pms.PmsProductSkuMapper;
import com.bosi.sim.paas.dao.model.sds.PmsAttribute;
import com.bosi.sim.paas.dao.model.sds.PmsAttributeType;
import com.bosi.sim.paas.dao.model.sds.PmsBrand;
import com.bosi.sim.paas.dao.model.sds.PmsCategory;
import com.bosi.sim.paas.dao.model.sds.PmsProduct;
import com.bosi.sim.paas.dao.model.sds.PmsProductAttributeType;
import com.bosi.sim.paas.dao.model.sds.PmsProductFullReduction;
import com.bosi.sim.paas.dao.model.sds.PmsProductLadder;
import com.bosi.sim.paas.dao.model.sds.PmsProductSku;
import com.bosi.sim.paas.admin.server.config.odoo.OdooProperties;
import com.bosi.sim.paas.admin.server.service.PmsProductOdooService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.log4j.Log4j2;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Log4j2
@Service
public class PmsProductOdooServiceImpl implements PmsProductOdooService {

    @Autowired
    private OdooProperties odooProperties;

    @Autowired
    private PmsProductMapper pmsProductMapper;

    @Autowired
    private PmsBrandMapper pmsBrandMapper;

    @Autowired
    private PmsCategoryMapper pmsCategoryMapper;

    @Autowired
    private PmsAttributeMapper pmsAttributeMapper;

    @Autowired
    private PmsProductSkuMapper pmsProductSkuMapper;

    @Autowired
    private PmsProductLadderMapper pmsProductLadderMapper;

    @Autowired
    private PmsProductFullReductionMapper pmsProductFullReductionMapper;

    @Autowired
    private PmsAttributeTypeMapper pmsAttributeTypeMapper;

    @Autowired
    private PmsProductAttributeTypeMapper pmsProductAttributeTypeMapper;

    @Autowired
    private RedissonClient redissonClient;

    @Override
    @Transactional
    @Async
    public void startSynchronous(List<JSONObject> partnerProducts) {
        try {
            this.toStart(partnerProducts);
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("startSynchronous error", e);
        }
    }

    public void toStart(List<JSONObject> partnerProducts) {
        RLock lock = redissonClient.getLock(SpringContext.getApplicationName() + ":lock:odooPullProductLock");
        try {
            // 尝试获取锁，如果无法立即获取则等待直到获得锁或者达到最大等待时间
            // 尝试获取锁，等待10秒，锁的最大持有时间为5秒
            if (lock.tryLock(3, 3600, TimeUnit.SECONDS)) {
                try {
                    this.synchronousProductList(partnerProducts);
                } catch (Exception e) {
                    log.error("synchronousProductList error", e);
                    throw BizException.build(SystemCode.FAILED);
                }
            } else {
                log.error("try lock fail");
            }
        } catch (InterruptedException interruptedException) {
            Thread.currentThread().interrupt();
            log.error("try lock interrupt");
            throw BizException.build(SystemCode.FAILED);
        } finally {
            // 无论是否执行成功，都要确保解锁
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void synchronousProductList(List<JSONObject> partnerProducts) {
        Map<String, BigDecimal> skuMinPriceMap = getSkuMinPriceMap(partnerProducts);

        LambdaQueryWrapper<PmsBrand> brandWrapper = new LambdaQueryWrapper<>();
        brandWrapper.eq(PmsBrand::getName, odooProperties.getBrandName());
        PmsBrand brand = pmsBrandMapper.selectOne(brandWrapper);
        if (brand == null) {
            log.error("can not find brand name:{}", odooProperties.getBrandName());
            return;
        }

        LambdaQueryWrapper<PmsProduct> productWrapper = new LambdaQueryWrapper<>();
        productWrapper.eq(PmsProduct::getBrandId, brand.getId());
        List<PmsProduct> originalProducts = pmsProductMapper.selectList(productWrapper);

        Map<String, PmsProduct> originalProductMap = Maps.uniqueIndex(originalProducts, PmsProduct::getProductSn);

        //获取新增sku信息
        List<JSONObject> partnerInsertProductList =
                partnerProducts.stream().filter(item -> !originalProductMap.containsKey(item.getString("product_code"))).collect(Collectors.toList());
        //获取有可能更新的sku信息
        List<JSONObject> partnerUpdateProductList =
                partnerProducts.stream().filter(item -> originalProductMap.containsKey(item.getString("product_code"))).collect(Collectors.toList());
        //获取需要删除的sku信息
        List<String> updateProductCodeList =
                partnerUpdateProductList.stream().map(item -> item.getString("product_code")).collect(Collectors.toList());
        List<PmsProduct> removeProductList =
                originalProducts.stream().filter(item -> !updateProductCodeList.contains(item.getProductSn())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(partnerInsertProductList)) {
            partnerInsertProductList.forEach(insertProduct -> synchronousAddProduct(insertProduct,
                    brand, skuMinPriceMap.get(insertProduct.getString("product_code"))));
        }

        if (!CollectionUtils.isEmpty(partnerUpdateProductList)) {
            partnerUpdateProductList.forEach(updateProduct -> synchronousEditProduct(updateProduct,
                    originalProductMap.get(updateProduct.getString("product_code")),
                    skuMinPriceMap.get(updateProduct.getString("product_code"))));
        }

        if (!CollectionUtils.isEmpty(removeProductList)) {
            removeProductList.forEach(this::synchronousDeleteProduct);
        }


    }

    private Map<String, BigDecimal> getSkuMinPriceMap(List<JSONObject> partnerProducts) {
        Map<String, BigDecimal> skuMinPriceMap = Maps.newHashMap();
        partnerProducts.forEach(partnerProductJson -> {
            String key = partnerProductJson.getString("product_code");
            List<JSONObject> variants = partnerProductJson.getList("variants", JSONObject.class);
            if (CollUtil.isEmpty(variants)) {
                skuMinPriceMap.put(key, new BigDecimal(0));
            } else {
                skuMinPriceMap.put(key,
                        variants.stream().map(variant -> variant.getBigDecimal("supply_price").setScale(2, RoundingMode.HALF_UP))
                                .min(BigDecimal::compareTo)
                                .orElseGet(() -> new BigDecimal(0)));
            }
        });
        return skuMinPriceMap;
    }


    public void synchronousAddProduct(JSONObject productJson, PmsBrand brand, BigDecimal skuMinPrice) {
        String productType = productJson.getString("product_type");
        LambdaQueryWrapper<PmsCategory> categoryWrapper = new LambdaQueryWrapper<>();
        categoryWrapper.eq(PmsCategory::getName, productType);
        PmsCategory category = pmsCategoryMapper.selectOne(categoryWrapper);
        if (category == null) {
            log.error("can not find category name:{}", productType);
            return;
        }
        LambdaQueryWrapper<PmsAttribute> pmsAttributeWrapper = new LambdaQueryWrapper<>();
        pmsAttributeWrapper.eq(PmsAttribute::getName, productType);
        PmsAttribute attribute = pmsAttributeMapper.selectOne(pmsAttributeWrapper);
        if (attribute == null) {
            log.error("can not find attribute name:{}", productType);
            return;
        }
        PmsProduct product = new PmsProduct();
        product.setBrandId(brand.getId());
        product.setCategoryId(category.getId());
        product.setAttributeId(attribute.getId());
        product.setProductName(productJson.getString("name"));
        product.setProductSn(productJson.getString("product_code"));
        product.setWhetherPublish(false);
        product.setSubTitle(productJson.getString("name"));
        product.setDescription(productJson.getString("website_description"));
        product.setWhetherVirtually(productType.equals("card") || productType.equals("esim-card"));
        product.setPromotionType(SdsPromotionTypeEnum.NULL.getPromotionType());
        product.setPrice(skuMinPrice == null ? new BigDecimal(0) : skuMinPrice);
        product.setSort(0);
        product.setOriginalPrice(skuMinPrice == null ? new BigDecimal(0) : skuMinPrice);
        pmsProductMapper.insert(product);
        List<JSONObject> variants = productJson.getList("variants", JSONObject.class);
        if (CollUtil.isEmpty(variants)) {
            log.error("variants is empty,the product is {}", productJson.toJSONString());
            throw BizException.build(BizCode.ODOO_PULL_PRODUCT_VARIANTS_EMPRY);

        }
        for (JSONObject variant : variants) {
            synchronousSaveProductSku(variant, product);
        }
        handleAttributeType(productJson, attribute.getId(), product.getId());
        syncAttributeCount(attribute);
    }

    private void handleAttributeType(JSONObject productJson, String attributeId, String productId) {
        List<JSONObject> product_attributes = productJson.getList("product_attributes", JSONObject.class);
        if (CollUtil.isNotEmpty(product_attributes)) {
            LambdaQueryWrapper<PmsProductAttributeType> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(PmsProductAttributeType::getProductId, productId);
            pmsProductAttributeTypeMapper.delete(deleteWrapper);
            for (JSONObject product_attribute : product_attributes) {
                LambdaQueryWrapper<PmsAttributeType> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(PmsAttributeType::getAttributeId, attributeId);
                wrapper.eq(PmsAttributeType::getCode, product_attribute.getString("code"));
                PmsAttributeType pmsAttributeType = pmsAttributeTypeMapper.selectOne(wrapper);
                if (pmsAttributeType == null) {
                    pmsAttributeType = new PmsAttributeType();
                    pmsAttributeType.setAttributeId(attributeId);
                    pmsAttributeType.setName(product_attribute.getString("name"));
                    pmsAttributeType.setCode(product_attribute.getString("code"));
                    pmsAttributeType.setSelectType(SdsAttributeTypeSelectTypeEnum.Multiple.getSelectType());
                    pmsAttributeType.setInputList(StringUtils.join(product_attribute.getList("values", String.class), ","));
                    pmsAttributeType.setSort(0);
                    pmsAttributeType.setClassify(SdsAttributeTypeClassifyEnum.SPEC.getClassify());
                    pmsAttributeTypeMapper.insert(pmsAttributeType);
                } else {
                    List<String> types = Lists.newArrayList(Arrays.stream(pmsAttributeType.getInputList().split(",")).iterator());
                    List<String> values = product_attribute.getList("values", String.class);
                    for (String value : values) {
                        if (!types.contains(value)) {
                            types.add(value);
                            pmsAttributeType.setInputList(StringUtils.join(types, ","));
                            pmsAttributeTypeMapper.updateById(pmsAttributeType);
                        }
                    }
                }
                LambdaQueryWrapper<PmsProductAttributeType> attributeTypeQueryWrapper = new LambdaQueryWrapper<>();
                attributeTypeQueryWrapper.eq(PmsProductAttributeType::getAttributeTypeId, pmsAttributeType.getId());
                attributeTypeQueryWrapper.eq(PmsProductAttributeType::getProductId, productId);
                PmsProductAttributeType productAttributeType = pmsProductAttributeTypeMapper.selectOne(attributeTypeQueryWrapper);
                if (productAttributeType == null) {
                    productAttributeType = new PmsProductAttributeType();
                    productAttributeType.setProductId(productId);
                    productAttributeType.setAttributeTypeId(pmsAttributeType.getId());
                    productAttributeType.setValue(StringUtils.join(product_attribute.getList("values", String.class), ","));
                    pmsProductAttributeTypeMapper.insert(productAttributeType);
                } else {
                    productAttributeType.setValue(StringUtils.join(product_attribute.getList("values", String.class), ","));
                    pmsProductAttributeTypeMapper.updateById(productAttributeType);
                }
            }
        }

        List<JSONObject> product_parameters = productJson.getList("product_parameters", JSONObject.class);
        if (CollUtil.isNotEmpty(product_parameters)) {
            for (JSONObject product_parameter : product_parameters) {
                LambdaQueryWrapper<PmsAttributeType> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(PmsAttributeType::getAttributeId, attributeId);
                wrapper.eq(PmsAttributeType::getCode, product_parameter.getString("code"));
                PmsAttributeType pmsAttributeType = pmsAttributeTypeMapper.selectOne(wrapper);
                if (pmsAttributeType == null) {
                    pmsAttributeType = new PmsAttributeType();
                    pmsAttributeType.setAttributeId(attributeId);
                    pmsAttributeType.setName(product_parameter.getString("name"));
                    pmsAttributeType.setCode(product_parameter.getString("code"));
                    pmsAttributeType.setSelectType(SdsAttributeTypeSelectTypeEnum.Multiple.getSelectType());
                    pmsAttributeType.setInputList(product_parameter.getString("value"));
                    pmsAttributeType.setSort(0);
                    pmsAttributeType.setClassify(SdsAttributeTypeClassifyEnum.PARAM.getClassify());
                    pmsAttributeTypeMapper.insert(pmsAttributeType);
                } else {
                    List<String> types = Lists.newArrayList(Arrays.stream(pmsAttributeType.getInputList().split(",")).iterator());
                    String[] values = product_parameter.getString("value").split(",");
                    for (String value : values) {
                        if (!types.contains(value)) {
                            types.add(value);
                            pmsAttributeType.setInputList(StringUtils.join(types, ","));
                            pmsAttributeTypeMapper.updateById(pmsAttributeType);
                        }
                    }
                }
                LambdaQueryWrapper<PmsProductAttributeType> attributeTypeQueryWrapper = new LambdaQueryWrapper<>();
                attributeTypeQueryWrapper.eq(PmsProductAttributeType::getAttributeTypeId, pmsAttributeType.getId());
                attributeTypeQueryWrapper.eq(PmsProductAttributeType::getProductId, productId);
                PmsProductAttributeType productAttributeType = pmsProductAttributeTypeMapper.selectOne(attributeTypeQueryWrapper);
                if (productAttributeType == null) {
                    productAttributeType = new PmsProductAttributeType();
                    productAttributeType.setProductId(productId);
                    productAttributeType.setAttributeTypeId(pmsAttributeType.getId());
                    productAttributeType.setValue(product_parameter.getString("value"));
                    pmsProductAttributeTypeMapper.insert(productAttributeType);
                } else {
                    productAttributeType.setValue(product_parameter.getString("value"));
                    pmsProductAttributeTypeMapper.updateById(productAttributeType);
                }

            }
        }
    }

    public void synchronousEditProduct(JSONObject productJson, PmsProduct product, BigDecimal skuMinPrice) {
        if (product != null) {
            String productType = productJson.getString("product_type");
            LambdaQueryWrapper<PmsCategory> categoryWrapper = new LambdaQueryWrapper<>();
            categoryWrapper.eq(PmsCategory::getName, productType);
            PmsCategory category = pmsCategoryMapper.selectOne(categoryWrapper);
            if (category == null) {
                log.error("can not find category name:{}", productType);
                return;
            }
            LambdaQueryWrapper<PmsAttribute> pmsAttributeWrapper = new LambdaQueryWrapper<>();
            pmsAttributeWrapper.eq(PmsAttribute::getName, productType);
            PmsAttribute attribute = pmsAttributeMapper.selectOne(pmsAttributeWrapper);
            if (attribute == null) {
                log.error("can not find attribute name:{}", productType);
                return;
            }
            boolean needUpdate = false;
            PmsProduct productUpdate = new PmsProduct();
            productUpdate.setId(product.getId());
            if (StringUtils.isNotNull(skuMinPrice) &&
                    skuMinPrice.compareTo(product.getOriginalPrice()) != 0) {
                productUpdate.setOriginalPrice(skuMinPrice);
                needUpdate = true;
            }
            if (StringUtils.isNotEmpty(productJson.getString("name")) &&
                    !product.getProductName().equals(productJson.getString("name"))) {
                productUpdate.setProductName(productJson.getString("name"));
                productUpdate.setSubTitle(productJson.getString("name"));
                needUpdate = true;
            }
            if (StringUtils.isNotEmpty(productJson.getString("website_description")) &&
                    !product.getDescription().equals(productJson.getString("website_description"))) {
                productUpdate.setDescription(productJson.getString("website_description"));
                needUpdate = true;
            }
            if (needUpdate) {
                productUpdate.setWhetherPublish(false);
                pmsProductMapper.updateById(productUpdate);
            }

            List<JSONObject> partnerSkuList = productJson.getList("variants", JSONObject.class);
            if (CollUtil.isEmpty(partnerSkuList)) {
                log.error("variants is empty,the product is {}", productJson.toJSONString());
                throw BizException.build(BizCode.ODOO_PULL_PRODUCT_VARIANTS_EMPRY);
            }

            LambdaQueryWrapper<PmsProductSku> skuWrapper = new LambdaQueryWrapper<>();
            skuWrapper.eq(PmsProductSku::getProductId, product.getId());
            List<PmsProductSku> originalSkuList = pmsProductSkuMapper.selectList(skuWrapper);
            Map<String, PmsProductSku> originalSkuMap = Maps.uniqueIndex(originalSkuList, PmsProductSku::getSkuCode);

            //获取新增sku信息
            List<JSONObject> partnerInsertProductSkuList =
                    partnerSkuList.stream().filter(item -> !originalSkuMap.containsKey(item.getString("variant_code"))).collect(Collectors.toList());
            //获取有可能更新的sku信息
            List<JSONObject> partnerUpdateProductSkuList =
                    partnerSkuList.stream().filter(item -> originalSkuMap.containsKey(item.getString("variant_code"))).collect(Collectors.toList());
            //获取需要删除的sku信息
            List<String> updateProductSkuCodeList =
                    partnerUpdateProductSkuList.stream().map(item -> item.getString("variant_code")).collect(Collectors.toList());
            List<PmsProductSku> removeProductSkuList =
                    originalSkuList.stream().filter(item -> !updateProductSkuCodeList.contains(item.getSkuCode())).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(partnerInsertProductSkuList)) {
                if (!needUpdate) {
                    productUpdate.setWhetherPublish(false);
                    pmsProductMapper.updateById(productUpdate);
                }
                partnerInsertProductSkuList.forEach(variant -> synchronousSaveProductSku(variant, product));
            }
            if (CollUtil.isNotEmpty(partnerUpdateProductSkuList)) {
                List<PmsProductSku> needUpdateSku = parseNeedUpdateSku(partnerUpdateProductSkuList, originalSkuMap);
                if (CollUtil.isNotEmpty(needUpdateSku)) {
                    productUpdate.setWhetherPublish(false);
                    pmsProductMapper.updateById(productUpdate);
                    needUpdateSku.forEach(sku -> pmsProductSkuMapper.updateById(sku));
                }
            }
            if (CollUtil.isNotEmpty(removeProductSkuList)) {
                if (!needUpdate) {
                    productUpdate.setWhetherPublish(false);
                    pmsProductMapper.updateById(productUpdate);
                }
                removeProductSkuList.forEach(sku -> pmsProductSkuMapper.deleteById(sku.getId()));
            }


            handleAttributeType(productJson, attribute.getId(), product.getId());
            syncAttributeCount(attribute);
        }
    }


    private void syncAttributeCount(PmsAttribute attribute) {
        LambdaQueryWrapper<PmsAttributeType> specCountWrapper = new LambdaQueryWrapper<>();
        specCountWrapper.eq(PmsAttributeType::getAttributeId, attribute.getId());
        specCountWrapper.eq(PmsAttributeType::getClassify, SdsAttributeTypeClassifyEnum.SPEC.getClassify());
        long spec = pmsAttributeTypeMapper.selectCount(specCountWrapper);
        LambdaQueryWrapper<PmsAttributeType> paramCountWrapper = new LambdaQueryWrapper<>();
        paramCountWrapper.eq(PmsAttributeType::getAttributeId, attribute.getId());
        paramCountWrapper.eq(PmsAttributeType::getClassify, SdsAttributeTypeClassifyEnum.PARAM.getClassify());
        long param = pmsAttributeTypeMapper.selectCount(paramCountWrapper);
        PmsAttribute updatePmsAttribute = new PmsAttribute();
        updatePmsAttribute.setId(attribute.getId());
        updatePmsAttribute.setSpecCount(Integer.parseInt(String.valueOf(spec)));
        updatePmsAttribute.setParamCount(Integer.parseInt(String.valueOf(param)));
        pmsAttributeMapper.updateById(updatePmsAttribute);
    }

    public void synchronousDeleteProduct(PmsProduct product) {
        pmsProductMapper.deleteById(product.getId());
        LambdaQueryWrapper<PmsProductLadder> ladderDeleteWrapper = new LambdaQueryWrapper<>();
        ladderDeleteWrapper.eq(PmsProductLadder::getProductId, product.getId());
        pmsProductLadderMapper.delete(ladderDeleteWrapper);
        LambdaQueryWrapper<PmsProductFullReduction> fullReductionDeleteWrapper = new LambdaQueryWrapper<>();
        fullReductionDeleteWrapper.eq(PmsProductFullReduction::getProductId, product.getId());
        pmsProductFullReductionMapper.delete(fullReductionDeleteWrapper);
        LambdaQueryWrapper<PmsProductSku> skuDeleteWrapper = new LambdaQueryWrapper<>();
        skuDeleteWrapper.eq(PmsProductSku::getProductId, product.getId());
        pmsProductSkuMapper.delete(skuDeleteWrapper);
        LambdaQueryWrapper<PmsProductAttributeType> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(PmsProductAttributeType::getProductId, product.getId());
        pmsProductAttributeTypeMapper.delete(deleteWrapper);
    }

    private List<PmsProductSku> parseNeedUpdateSku(List<JSONObject> partnerUpdateProductSkuList, Map<String, PmsProductSku> originalSkuMap) {
        List<PmsProductSku> needUpdateSku = Lists.newArrayList();
        partnerUpdateProductSkuList.forEach(variant -> {
            PmsProductSku originalProductSku = originalSkuMap.get(variant.getString("variant_code"));
            if (originalProductSku != null) {
                boolean skuNeedUpdate = false;
                PmsProductSku productSkuUpdate = new PmsProductSku();
                productSkuUpdate.setId(originalProductSku.getId());
                if (StringUtils.isNotEmpty(variant.getString("variant_name")) &&
                        !originalProductSku.getSkuName().equals(variant.getString("variant_name"))) {
                    productSkuUpdate.setSkuName(variant.getString("variant_name"));
                    skuNeedUpdate = true;
                }
                if (StringUtils.isNotNull(variant.getBigDecimal("supply_price")) &&
                        originalProductSku.getOriginalPrice().compareTo(variant.getBigDecimal("supply_price").setScale(2, RoundingMode.HALF_UP)) != 0) {
                    productSkuUpdate.setOriginalPrice(variant.getBigDecimal("supply_price"));
                    skuNeedUpdate = true;
                }
                List<JSONObject> partnerSpData = getSpData(variant);
                List<JSONObject> originalSpData = JSON.parseArray(originalProductSku.getSpData(), JSONObject.class);
                if (CollUtil.isNotEmpty(partnerSpData) && whetherChangeOfSpData(originalSpData, partnerSpData)) {
                    productSkuUpdate.setSpData(JSON.toJSONString(partnerSpData));
                    skuNeedUpdate = true;
                }
                if (skuNeedUpdate) {
                    needUpdateSku.add(productSkuUpdate);
                }
            }
        });
        return needUpdateSku;
    }

    private boolean whetherChangeOfSpData(List<JSONObject> originalSpData, List<JSONObject> partnerSpData) {
        if (originalSpData.size() != partnerSpData.size()) {
            return true;
        }
        Map<String, String> originalSpDataMap = Maps.newHashMap();
        originalSpData.forEach(json -> originalSpDataMap.put(json.getString("key"), json.getString("value")));
        Map<String, String> partnerSpDataMap = Maps.newHashMap();
        partnerSpData.forEach(json -> partnerSpDataMap.put(json.getString("key"), json.getString("value")));
        for (Map.Entry<String, String> originalEntry : originalSpDataMap.entrySet()) {
            if (!originalEntry.getValue().equals(partnerSpDataMap.get(originalEntry.getKey()))) {
                return true;
            }
        }
        return false;
    }

    private void synchronousSaveProductSku(JSONObject variant, PmsProduct product) {
        List<JSONObject> spDataList = getSpData(variant);
        PmsProductSku productSku = new PmsProductSku();
        productSku.setProductId(product.getId());
        productSku.setSkuName(variant.getString("variant_name"));
        productSku.setSkuCode(variant.getString("variant_code"));
        productSku.setOriginalPrice(variant.getBigDecimal("supply_price").setScale(2, RoundingMode.HALF_UP));
        productSku.setPrice(variant.getBigDecimal("supply_price").setScale(2, RoundingMode.HALF_UP));
        productSku.setStock(odooProperties.getDefaultSku());
        productSku.setLockStock(0);
        productSku.setLowStock(0);
        productSku.setSale(0);
        productSku.setSpData(JSON.toJSONString(spDataList));
        pmsProductSkuMapper.insert(productSku);
    }

    private List<JSONObject> getSpData(JSONObject variant) {
        List<JSONObject> spDataList = Lists.newArrayList();
        JSONArray variant_attributes = variant.getJSONArray("variant_attributes");
        if (CollUtil.isNotEmpty(variant_attributes)) {
            for (int x = 0; x < variant_attributes.size(); x++) {
                JSONObject variant_attribute = variant_attributes.getJSONObject(x);
                JSONObject attribute = new JSONObject();
                attribute.put("key", variant_attribute.getString("name"));
                attribute.put("value", variant_attribute.getString("value"));
                spDataList.add(attribute);
            }
        }
        return spDataList;
    }
}
