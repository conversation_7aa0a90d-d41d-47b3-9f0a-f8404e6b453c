{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Spin } from 'antd';\nimport { useRouter } from 'next/navigation';\nimport { useEffect } from 'react';\n\nexport default function Home() {\n  const { user, loading } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!loading) {\n      if (user) {\n        router.push('/dashboard');\n      } else {\n        router.push('/login');\n      }\n    }\n  }, [user, loading, router]);\n\n  return (\n    <div className=\"flex justify-center items-center min-h-screen\">\n      <Spin size=\"large\" tip=\"加载中...\" />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,CAAC,SAAS;gBACZ,IAAI,MAAM;oBACR,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF;QACF;yBAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,iLAAA,CAAA,OAAI;YAAC,MAAK;YAAQ,KAAI;;;;;;;;;;;AAG7B;GAnBwB;;QACI,kIAAA,CAAA,UAAO;QAClB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}