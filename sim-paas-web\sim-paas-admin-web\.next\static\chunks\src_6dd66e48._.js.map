{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/i18n/locales/en.ts"], "sourcesContent": ["export default {\n  // 通用\n  common: {\n    welcome: 'Welcome',\n    login: 'Login',\n    logout: 'Logout',\n    username: '<PERSON><PERSON><PERSON>',\n    password: 'Password',\n    email: 'Em<PERSON>',\n    name: 'Name',\n    status: 'Status',\n    role: 'Role',\n    roles: 'Roles',\n    action: 'Action',\n    search: 'Search',\n    add: 'Add',\n    edit: 'Edit',\n    delete: 'Delete',\n    cancel: 'Cancel',\n    confirm: 'Confirm',\n    save: 'Save',\n    refresh: 'Refresh',\n    back: 'Back',\n    success: 'Success',\n    error: 'Error',\n    warning: 'Warning',\n    info: 'Info',\n    loading: 'Loading...',\n    required: 'Required',\n    optional: 'Optional',\n    active: 'Active',\n    inactive: 'Inactive',\n    enabled: 'Enabled',\n    disabled: 'Disabled',\n    yes: 'Yes',\n    no: 'No',\n    all: 'All',\n    none: 'None',\n    total: 'Total {{count}} items',\n    language: 'Language',\n    switchLanguage: 'Switch Language',\n    theme: 'Theme',\n    switchTheme: 'Switch Theme',\n    light: 'Light',\n    dark: 'Dark',\n  },\n\n  // 菜单\n  menu: {\n    dashboard: 'Dashboard',\n    system: 'System',\n    user: 'User Management',\n    role: 'Role Management',\n    menu: 'Menu Management',\n    home: 'Home',\n  },\n\n  // 登录页\n  login: {\n    title: 'IOT Admin',\n    subtitle: 'A clean and beautiful admin system',\n    rememberMe: 'Remember me',\n    forgotPassword: 'Forgot password?',\n    noAccount: 'Don\\'t have an account?',\n    register: 'Register now',\n    loginButton: 'Login',\n    defaultAccount: 'Default account: admin / admin123',\n    loginSuccess: 'Login successful',\n    loginFailed: 'Login failed, please check your username and password',\n  },\n\n  // 仪表盘\n  dashboard: {\n    title: 'Dashboard',\n    userCount: 'User Count',\n    roleCount: 'Role Count',\n    menuCount: 'Menu Count',\n    systemInfo: 'System Information',\n    systemName: 'System Name',\n    version: 'Version',\n    frontendFramework: 'Frontend Framework',\n    backendTechnology: 'Backend Technology',\n  },\n\n  // 用户管理\n  user: {\n    title: 'User Management',\n    addUser: 'Add User',\n    editUser: 'Edit User',\n    deleteUser: 'Delete User',\n    deleteConfirm: 'Are you sure you want to delete this user?',\n    username: 'Username',\n    name: 'Name',\n    email: 'Email',\n    password: 'Password',\n    role: 'Role',\n    status: 'Status',\n    createSuccess: 'User created successfully',\n    updateSuccess: 'User updated successfully',\n    deleteSuccess: 'User deleted successfully',\n    pleaseEnterUsername: 'Please enter username',\n    pleaseEnterName: 'Please enter name',\n    pleaseEnterEmail: 'Please enter email',\n    pleaseEnterPassword: 'Please enter password',\n    pleaseSelectRole: 'Please select role',\n    invalidEmail: 'Please enter a valid email address',\n  },\n\n  // 角色管理\n  role: {\n    title: 'Role Management',\n    addRole: 'Add Role',\n    editRole: 'Edit Role',\n    deleteRole: 'Delete Role',\n    deleteConfirm: 'Are you sure you want to delete this role?',\n    name: 'Role Name',\n    description: 'Description',\n    menuPermission: 'Menu Permission',\n    createSuccess: 'Role created successfully',\n    updateSuccess: 'Role updated successfully',\n    deleteSuccess: 'Role deleted successfully',\n    pleaseEnterName: 'Please enter role name',\n    pleaseEnterDescription: 'Please enter description',\n    pleaseSelectMenuPermission: 'Please select menu permission',\n  },\n\n  // 菜单管理\n  menuManagement: {\n    title: 'Menu Management',\n    addMenu: 'Add Menu',\n    editMenu: 'Edit Menu',\n    deleteMenu: 'Delete Menu',\n    deleteConfirm: 'Are you sure you want to delete this menu?',\n    name: 'Menu Name',\n    path: 'Path',\n    icon: 'Icon',\n    parentMenu: 'Parent Menu',\n    order: 'Order',\n    rootMenu: 'Root Menu',\n    createSuccess: 'Menu created successfully',\n    updateSuccess: 'Menu updated successfully',\n    deleteSuccess: 'Menu deleted successfully',\n    pleaseEnterName: 'Please enter menu name',\n    pleaseEnterPath: 'Please enter path',\n    pleaseSelectIcon: 'Please select icon',\n    pleaseSelectParentMenu: 'Please select parent menu',\n    pleaseEnterOrder: 'Please enter order',\n  },\n\n  // 标签页\n  tabs: {\n    refresh: 'Refresh Page',\n    close: 'Close Tab',\n    closeOthers: 'Close Other Tabs',\n    closeAll: 'Close All Tabs',\n    refreshSuccess: 'Page refreshed',\n  },\n};\n"], "names": [], "mappings": ";;;uCAAe;IACb,KAAK;IACL,QAAQ;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,UAAU;QACV,UAAU;QACV,OAAO;QACP,MAAM;QACN,QAAQ;QACR,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,KAAK;QACL,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,MAAM;QACN,SAAS;QACT,MAAM;QACN,SAAS;QACT,OAAO;QACP,SAAS;QACT,MAAM;QACN,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;QACV,SAAS;QACT,UAAU;QACV,KAAK;QACL,IAAI;QACJ,KAAK;QACL,MAAM;QACN,OAAO;QACP,UAAU;QACV,gBAAgB;QAChB,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;IACR;IAEA,KAAK;IACL,MAAM;QACJ,WAAW;QACX,QAAQ;QACR,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;IACR;IAEA,MAAM;IACN,OAAO;QACL,OAAO;QACP,UAAU;QACV,YAAY;QACZ,gBAAgB;QAChB,WAAW;QACX,UAAU;QACV,aAAa;QACb,gBAAgB;QAChB,cAAc;QACd,aAAa;IACf;IAEA,MAAM;IACN,WAAW;QACT,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,SAAS;QACT,mBAAmB;QACnB,mBAAmB;IACrB;IAEA,OAAO;IACP,MAAM;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,YAAY;QACZ,eAAe;QACf,UAAU;QACV,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,eAAe;QACf,eAAe;QACf,eAAe;QACf,qBAAqB;QACrB,iBAAiB;QACjB,kBAAkB;QAClB,qBAAqB;QACrB,kBAAkB;QAClB,cAAc;IAChB;IAEA,OAAO;IACP,MAAM;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,YAAY;QACZ,eAAe;QACf,MAAM;QACN,aAAa;QACb,gBAAgB;QAChB,eAAe;QACf,eAAe;QACf,eAAe;QACf,iBAAiB;QACjB,wBAAwB;QACxB,4BAA4B;IAC9B;IAEA,OAAO;IACP,gBAAgB;QACd,OAAO;QACP,SAAS;QACT,UAAU;QACV,YAAY;QACZ,eAAe;QACf,MAAM;QACN,MAAM;QACN,MAAM;QACN,YAAY;QACZ,OAAO;QACP,UAAU;QACV,eAAe;QACf,eAAe;QACf,eAAe;QACf,iBAAiB;QACjB,iBAAiB;QACjB,kBAAkB;QAClB,wBAAwB;QACxB,kBAAkB;IACpB;IAEA,MAAM;IACN,MAAM;QACJ,SAAS;QACT,OAAO;QACP,aAAa;QACb,UAAU;QACV,gBAAgB;IAClB;AACF", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/i18n/locales/zh.ts"], "sourcesContent": ["export default {\n  // 通用\n  common: {\n    welcome: '欢迎',\n    login: '登录',\n    logout: '退出登录',\n    username: '用户名',\n    password: '密码',\n    email: '邮箱',\n    name: '姓名',\n    status: '状态',\n    role: '角色',\n    roles: '角色',\n    action: '操作',\n    search: '搜索',\n    add: '新增',\n    edit: '编辑',\n    delete: '删除',\n    cancel: '取消',\n    confirm: '确认',\n    save: '保存',\n    refresh: '刷新',\n    back: '返回',\n    success: '成功',\n    error: '错误',\n    warning: '警告',\n    info: '信息',\n    loading: '加载中...',\n    required: '必填',\n    optional: '可选',\n    active: '启用',\n    inactive: '禁用',\n    enabled: '启用',\n    disabled: '禁用',\n    yes: '是',\n    no: '否',\n    all: '全部',\n    none: '无',\n    total: '共 {{count}} 条记录',\n    language: '语言',\n    switchLanguage: '切换语言',\n    theme: '主题',\n    switchTheme: '切换主题',\n    light: '亮色',\n    dark: '暗色',\n  },\n\n  // 菜单\n  menu: {\n    dashboard: '仪表盘',\n    system: '系统管理',\n    user: '用户管理',\n    role: '角色管理',\n    menu: '菜单管理',\n    home: '首页',\n  },\n\n  // 登录页\n  login: {\n    title: 'IOT Admin',\n    subtitle: '一个简洁美观的后台管理系统',\n    rememberMe: '记住我',\n    forgotPassword: '忘记密码？',\n    noAccount: '没有账号？',\n    register: '立即注册',\n    loginButton: '登录',\n    defaultAccount: '默认账号：admin / admin123',\n    loginSuccess: '登录成功',\n    loginFailed: '登录失败，请检查用户名和密码',\n  },\n\n  // 仪表盘\n  dashboard: {\n    title: '仪表盘',\n    userCount: '用户总数',\n    roleCount: '角色总数',\n    menuCount: '菜单总数',\n    systemInfo: '系统信息',\n    systemName: '系统名称',\n    version: '当前版本',\n    frontendFramework: '前端框架',\n    backendTechnology: '后端技术',\n  },\n\n  // 用户管理\n  user: {\n    title: '用户管理',\n    addUser: '新增用户',\n    editUser: '编辑用户',\n    deleteUser: '删除用户',\n    deleteConfirm: '确定要删除此用户吗？',\n    username: '用户名',\n    name: '姓名',\n    email: '邮箱',\n    password: '密码',\n    role: '角色',\n    status: '状态',\n    createSuccess: '用户创建成功',\n    updateSuccess: '用户更新成功',\n    deleteSuccess: '用户删除成功',\n    pleaseEnterUsername: '请输入用户名',\n    pleaseEnterName: '请输入姓名',\n    pleaseEnterEmail: '请输入邮箱',\n    pleaseEnterPassword: '请输入密码',\n    pleaseSelectRole: '请选择角色',\n    invalidEmail: '请输入有效的邮箱地址',\n  },\n\n  // 角色管理\n  role: {\n    title: '角色管理',\n    addRole: '新增角色',\n    editRole: '编辑角色',\n    deleteRole: '删除角色',\n    deleteConfirm: '确定要删除此角色吗？',\n    name: '角色名称',\n    description: '描述',\n    menuPermission: '菜单权限',\n    createSuccess: '角色创建成功',\n    updateSuccess: '角色更新成功',\n    deleteSuccess: '角色删除成功',\n    pleaseEnterName: '请输入角色名称',\n    pleaseEnterDescription: '请输入描述',\n    pleaseSelectMenuPermission: '请选择菜单权限',\n  },\n\n  // 菜单管理\n  menuManagement: {\n    title: '菜单管理',\n    addMenu: '新增菜单',\n    editMenu: '编辑菜单',\n    deleteMenu: '删除菜单',\n    deleteConfirm: '确定要删除此菜单吗？',\n    name: '菜单名称',\n    path: '路径',\n    icon: '图标',\n    parentMenu: '上级菜单',\n    order: '排序',\n    rootMenu: '根菜单',\n    createSuccess: '菜单创建成功',\n    updateSuccess: '菜单更新成功',\n    deleteSuccess: '菜单删除成功',\n    pleaseEnterName: '请输入菜单名称',\n    pleaseEnterPath: '请输入路径',\n    pleaseSelectIcon: '请选择图标',\n    pleaseSelectParentMenu: '请选择上级菜单',\n    pleaseEnterOrder: '请输入排序',\n  },\n\n  // 标签页\n  tabs: {\n    refresh: '刷新页面',\n    close: '关闭标签页',\n    closeOthers: '关闭其他标签页',\n    closeAll: '关闭所有标签页',\n    refreshSuccess: '页面已刷新',\n  },\n};\n"], "names": [], "mappings": ";;;uCAAe;IACb,KAAK;IACL,QAAQ;QACN,SAAS;QACT,OAAO;QACP,QAAQ;QACR,UAAU;QACV,UAAU;QACV,OAAO;QACP,MAAM;QACN,QAAQ;QACR,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,KAAK;QACL,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,MAAM;QACN,SAAS;QACT,MAAM;QACN,SAAS;QACT,OAAO;QACP,SAAS;QACT,MAAM;QACN,SAAS;QACT,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;QACV,SAAS;QACT,UAAU;QACV,KAAK;QACL,IAAI;QACJ,KAAK;QACL,MAAM;QACN,OAAO;QACP,UAAU;QACV,gBAAgB;QAChB,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;IACR;IAEA,KAAK;IACL,MAAM;QACJ,WAAW;QACX,QAAQ;QACR,MAAM;QACN,MAAM;QACN,MAAM;QACN,MAAM;IACR;IAEA,MAAM;IACN,OAAO;QACL,OAAO;QACP,UAAU;QACV,YAAY;QACZ,gBAAgB;QAChB,WAAW;QACX,UAAU;QACV,aAAa;QACb,gBAAgB;QAChB,cAAc;QACd,aAAa;IACf;IAEA,MAAM;IACN,WAAW;QACT,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,SAAS;QACT,mBAAmB;QACnB,mBAAmB;IACrB;IAEA,OAAO;IACP,MAAM;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,YAAY;QACZ,eAAe;QACf,UAAU;QACV,MAAM;QACN,OAAO;QACP,UAAU;QACV,MAAM;QACN,QAAQ;QACR,eAAe;QACf,eAAe;QACf,eAAe;QACf,qBAAqB;QACrB,iBAAiB;QACjB,kBAAkB;QAClB,qBAAqB;QACrB,kBAAkB;QAClB,cAAc;IAChB;IAEA,OAAO;IACP,MAAM;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,YAAY;QACZ,eAAe;QACf,MAAM;QACN,aAAa;QACb,gBAAgB;QAChB,eAAe;QACf,eAAe;QACf,eAAe;QACf,iBAAiB;QACjB,wBAAwB;QACxB,4BAA4B;IAC9B;IAEA,OAAO;IACP,gBAAgB;QACd,OAAO;QACP,SAAS;QACT,UAAU;QACV,YAAY;QACZ,eAAe;QACf,MAAM;QACN,MAAM;QACN,MAAM;QACN,YAAY;QACZ,OAAO;QACP,UAAU;QACV,eAAe;QACf,eAAe;QACf,eAAe;QACf,iBAAiB;QACjB,iBAAiB;QACjB,kBAAkB;QAClB,wBAAwB;QACxB,kBAAkB;IACpB;IAEA,MAAM;IACN,MAAM;QACJ,SAAS;QACT,OAAO;QACP,aAAa;QACb,UAAU;QACV,gBAAgB;IAClB;AACF", "debugId": null}}, {"offset": {"line": 333, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/i18n/index.ts"], "sourcesContent": ["import i18n from 'i18next';\nimport { initReactI18next } from 'react-i18next';\nimport LanguageDetector from 'i18next-browser-languagedetector';\n\nimport enTranslation from './locales/en';\nimport zhTranslation from './locales/zh';\n\n// 初始化 i18next\ni18n\n  // 检测用户语言\n  .use(LanguageDetector)\n  // 将 i18n 实例传递给 react-i18next\n  .use(initReactI18next)\n  // 初始化 i18next\n  .init({\n    resources: {\n      en: {\n        translation: enTranslation\n      },\n      zh: {\n        translation: zhTranslation\n      }\n    },\n    fallbackLng: 'zh', // 默认语言\n    debug: process.env.NODE_ENV === 'development',\n    interpolation: {\n      escapeValue: false // 不转义 HTML\n    },\n    detection: {\n      order: ['localStorage', 'navigator'],\n      caches: ['localStorage']\n    }\n  });\n\nexport default i18n;\n"], "names": [], "mappings": ";;;AAwBW;AAxBX;AACA;AAAA;AACA;AAEA;AACA;;;;;;AAEA,cAAc;AACd,oJAAA,CAAA,UAAI,AACF,SAAS;CACR,GAAG,CAAC,0MAAA,CAAA,UAAgB,CACrB,6BAA6B;CAC5B,GAAG,CAAC,qKAAA,CAAA,mBAAgB,CACrB,cAAc;CACb,IAAI,CAAC;IACJ,WAAW;QACT,IAAI;YACF,aAAa,+HAAA,CAAA,UAAa;QAC5B;QACA,IAAI;YACF,aAAa,+HAAA,CAAA,UAAa;QAC5B;IACF;IACA,aAAa;IACb,OAAO,oDAAyB;IAChC,eAAe;QACb,aAAa,MAAM,WAAW;IAChC;IACA,WAAW;QACT,OAAO;YAAC;YAAgB;SAAY;QACpC,QAAQ;YAAC;SAAe;IAC1B;AACF;uCAEa,oJAAA,CAAA,UAAI", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/i18n/I18nProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { ReactNode, useEffect, useState } from 'react';\nimport i18n from '../i18n';\n\ninterface I18nProviderProps {\n  children: ReactNode;\n}\n\n// 这个组件用于在客户端初始化 i18n\nexport default function I18nProvider({ children }: I18nProviderProps) {\n  const [initialized, setInitialized] = useState(false);\n\n  useEffect(() => {\n    // 确保 i18n 在客户端初始化，且只初始化一次\n    if (!initialized && i18n.isInitialized) {\n      setInitialized(true);\n    }\n  }, [initialized]);\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAUe,SAAS,aAAa,EAAE,QAAQ,EAAqB;;IAClE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,0BAA0B;YAC1B,IAAI,CAAC,eAAe,uHAAA,CAAA,UAAI,CAAC,aAAa,EAAE;gBACtC,eAAe;YACjB;QACF;iCAAG;QAAC;KAAY;IAEhB,qBAAO;kBAAG;;AACZ;GAXwB;KAAA", "debugId": null}}, {"offset": {"line": 427, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\n\n// API 基础配置\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api';\n\n// 创建 axios 实例\nconst apiClient: AxiosInstance = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 请求拦截器 - 添加认证 token\napiClient.interceptors.request.use(\n  (config) => {\n    // 检查是否在浏览器环境\n    if (typeof window !== 'undefined') {\n      const token = localStorage.getItem('token');\n      if (token && config.headers) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器 - 处理错误\napiClient.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    // 检查是否在浏览器环境\n    if (typeof window !== 'undefined') {\n      // 处理 401 未授权错误\n      if (error.response && error.response.status === 401) {\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n      }\n    }\n    return Promise.reject(error);\n  }\n);\n\n// 通用 API 请求方法\nexport const api = {\n  get: <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {\n    return apiClient.get<T, AxiosResponse<T>>(url, config).then((response) => response.data);\n  },\n  post: <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n    return apiClient.post<T, AxiosResponse<T>>(url, data, config).then((response) => response.data);\n  },\n  put: <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n    return apiClient.put<T, AxiosResponse<T>>(url, data, config).then((response) => response.data);\n  },\n  delete: <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {\n    return apiClient.delete<T, AxiosResponse<T>>(url, config).then((response) => response.data);\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;AAGqB;AAHrB;;AAEA,WAAW;AACX,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAExD,cAAc;AACd,MAAM,YAA2B,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC5C,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,qBAAqB;AACrB,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAChC,CAAC;IACC,aAAa;IACb,wCAAmC;QACjC,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,SAAS,OAAO,OAAO,EAAE;YAC3B,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;QAClD;IACF;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,eAAe;AACf,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC,WAAa,UACd,CAAC;IACC,aAAa;IACb,wCAAmC;QACjC,eAAe;QACf,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;YACnD,aAAa,UAAU,CAAC;YACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,MAAM;IACjB,KAAK,CAAI,KAAa;QACpB,OAAO,UAAU,GAAG,CAAsB,KAAK,QAAQ,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACzF;IACA,MAAM,CAAI,KAAa,MAAY;QACjC,OAAO,UAAU,IAAI,CAAsB,KAAK,MAAM,QAAQ,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IAChG;IACA,KAAK,CAAI,KAAa,MAAY;QAChC,OAAO,UAAU,GAAG,CAAsB,KAAK,MAAM,QAAQ,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IAC/F;IACA,QAAQ,CAAI,KAAa;QACvB,OAAO,UAAU,MAAM,CAAsB,KAAK,QAAQ,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IAC5F;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 492, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/services/auth.service.ts"], "sourcesContent": ["import { AuthResponse, LoginCredentials, User } from '@/types';\nimport api from './api';\n\n// 模拟数据\nconst mockAdminUser: User = {\n  id: '1',\n  username: 'admin',\n  name: '管理员',\n  email: '<EMAIL>',\n  avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',\n  status: 'active',\n  roleIds: ['1'],\n  createdAt: new Date().toISOString(),\n  updatedAt: new Date().toISOString(),\n};\n\nconst mockToken = 'mock-jwt-token';\n\n// 认证服务\nexport const authService = {\n  // 登录\n  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.post<AuthResponse>('/auth/login', credentials);\n\n      // 模拟登录逻辑\n      if (credentials.username === 'admin' && credentials.password === 'admin123') {\n        // 模拟延迟\n        await new Promise(resolve => setTimeout(resolve, 500));\n\n        // 保存 token 到本地存储\n        if (typeof window !== 'undefined') {\n          localStorage.setItem('token', mockToken);\n        }\n\n        return {\n          token: mockToken,\n          user: mockAdminUser\n        };\n      } else {\n        throw new Error('用户名或密码错误');\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 登出\n  logout: async (): Promise<void> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.post<void>('/auth/logout');\n\n      // 模拟登出逻辑\n      if (typeof window !== 'undefined') {\n        localStorage.removeItem('token');\n      }\n      await new Promise(resolve => setTimeout(resolve, 300));\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取当前用户信息\n  getCurrentUser: async (): Promise<User> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.get<User>('/auth/me');\n\n      // 模拟获取用户信息\n      if (typeof window === 'undefined') {\n        throw new Error('服务器端不支持此操作');\n      }\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('未登录');\n      }\n\n      // 模拟延迟\n      await new Promise(resolve => setTimeout(resolve, 300));\n\n      return mockAdminUser;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 检查是否已登录\n  isAuthenticated: (): boolean => {\n    if (typeof window === 'undefined') {\n      return false; // 服务器端渲染时返回 false\n    }\n    return !!localStorage.getItem('token');\n  }\n};\n\nexport default authService;\n"], "names": [], "mappings": ";;;;AAGA,OAAO;AACP,MAAM,gBAAsB;IAC1B,IAAI;IACJ,UAAU;IACV,MAAM;IACN,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,SAAS;QAAC;KAAI;IACd,WAAW,IAAI,OAAO,WAAW;IACjC,WAAW,IAAI,OAAO,WAAW;AACnC;AAEA,MAAM,YAAY;AAGX,MAAM,cAAc;IACzB,KAAK;IACL,OAAO,OAAO;QACZ,IAAI;YACF,oBAAoB;YACpB,6DAA6D;YAE7D,SAAS;YACT,IAAI,YAAY,QAAQ,KAAK,WAAW,YAAY,QAAQ,KAAK,YAAY;gBAC3E,OAAO;gBACP,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,iBAAiB;gBACjB,wCAAmC;oBACjC,aAAa,OAAO,CAAC,SAAS;gBAChC;gBAEA,OAAO;oBACL,OAAO;oBACP,MAAM;gBACR;YACF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,KAAK;IACL,QAAQ;QACN,IAAI;YACF,oBAAoB;YACpB,yCAAyC;YAEzC,SAAS;YACT,wCAAmC;gBACjC,aAAa,UAAU,CAAC;YAC1B;YACA,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,WAAW;IACX,gBAAgB;QACd,IAAI;YACF,oBAAoB;YACpB,oCAAoC;YAEpC,WAAW;YACX,uCAAmC;;YAEnC;YAEA,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO;YACP,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,UAAU;IACV,iBAAiB;QACf,uCAAmC;;QAEnC;QACA,OAAO,CAAC,CAAC,aAAa,OAAO,CAAC;IAChC;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/services/user.service.ts"], "sourcesContent": ["import { PaginatedResponse, PaginationParams, User, UserCreateInput, UserUpdateInput } from '@/types';\nimport api from './api';\n\n// 模拟数据\nconst mockUsers: User[] = [\n  {\n    id: '1',\n    username: 'admin',\n    name: '管理员',\n    email: '<EMAIL>',\n    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',\n    status: 'active',\n    roleIds: ['1'],\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: '2',\n    username: 'user1',\n    name: '普通用户1',\n    email: '<EMAIL>',\n    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user1',\n    status: 'active',\n    roleIds: ['2'],\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: '3',\n    username: 'user2',\n    name: '普通用户2',\n    email: '<EMAIL>',\n    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user2',\n    status: 'inactive',\n    roleIds: ['2'],\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n];\n\n// 用户服务\nexport const userService = {\n  // 获取用户列表\n  getUsers: async (params: PaginationParams): Promise<PaginatedResponse<User>> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.get<PaginatedResponse<User>>('/users', { params });\n      \n      // 模拟获取用户列表\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const { page, pageSize } = params;\n      const startIndex = (page - 1) * pageSize;\n      const endIndex = startIndex + pageSize;\n      const paginatedUsers = mockUsers.slice(startIndex, endIndex);\n      \n      return {\n        data: paginatedUsers,\n        total: mockUsers.length,\n        page,\n        pageSize\n      };\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取单个用户\n  getUser: async (id: string): Promise<User> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.get<User>(`/users/${id}`);\n      \n      // 模拟获取单个用户\n      await new Promise(resolve => setTimeout(resolve, 300));\n      \n      const user = mockUsers.find(user => user.id === id);\n      if (!user) {\n        throw new Error('用户不存在');\n      }\n      \n      return user;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 创建用户\n  createUser: async (userData: UserCreateInput): Promise<User> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.post<User>('/users', userData);\n      \n      // 模拟创建用户\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const newUser: User = {\n        id: String(mockUsers.length + 1),\n        username: userData.username,\n        name: userData.name,\n        email: userData.email,\n        status: userData.status,\n        roleIds: userData.roleIds,\n        avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${userData.username}`,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n      };\n      \n      // 在实际应用中，这里会由后端处理\n      mockUsers.push(newUser);\n      \n      return newUser;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 更新用户\n  updateUser: async (userData: UserUpdateInput): Promise<User> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.put<User>(`/users/${userData.id}`, userData);\n      \n      // 模拟更新用户\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const userIndex = mockUsers.findIndex(user => user.id === userData.id);\n      if (userIndex === -1) {\n        throw new Error('用户不存在');\n      }\n      \n      const updatedUser = {\n        ...mockUsers[userIndex],\n        ...userData,\n        updatedAt: new Date().toISOString(),\n      };\n      \n      // 在实际应用中，这里会由后端处理\n      mockUsers[userIndex] = updatedUser;\n      \n      return updatedUser;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 删除用户\n  deleteUser: async (id: string): Promise<void> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.delete<void>(`/users/${id}`);\n      \n      // 模拟删除用户\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const userIndex = mockUsers.findIndex(user => user.id === id);\n      if (userIndex === -1) {\n        throw new Error('用户不存在');\n      }\n      \n      // 在实际应用中，这里会由后端处理\n      mockUsers.splice(userIndex, 1);\n    } catch (error) {\n      throw error;\n    }\n  },\n};\n\nexport default userService;\n"], "names": [], "mappings": ";;;;AAGA,OAAO;AACP,MAAM,YAAoB;IACxB;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,SAAS;YAAC;SAAI;QACd,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,SAAS;YAAC;SAAI;QACd,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,SAAS;YAAC;SAAI;QACd,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;CACD;AAGM,MAAM,cAAc;IACzB,SAAS;IACT,UAAU,OAAO;QACf,IAAI;YACF,oBAAoB;YACpB,iEAAiE;YAEjE,WAAW;YACX,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG;YAC3B,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;YAChC,MAAM,WAAW,aAAa;YAC9B,MAAM,iBAAiB,UAAU,KAAK,CAAC,YAAY;YAEnD,OAAO;gBACL,MAAM;gBACN,OAAO,UAAU,MAAM;gBACvB;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,SAAS;IACT,SAAS,OAAO;QACd,IAAI;YACF,oBAAoB;YACpB,wCAAwC;YAExC,WAAW;YACX,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAChD,IAAI,CAAC,MAAM;gBACT,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,OAAO;IACP,YAAY,OAAO;QACjB,IAAI;YACF,oBAAoB;YACpB,6CAA6C;YAE7C,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,UAAgB;gBACpB,IAAI,OAAO,UAAU,MAAM,GAAG;gBAC9B,UAAU,SAAS,QAAQ;gBAC3B,MAAM,SAAS,IAAI;gBACnB,OAAO,SAAS,KAAK;gBACrB,QAAQ,SAAS,MAAM;gBACvB,SAAS,SAAS,OAAO;gBACzB,QAAQ,CAAC,gDAAgD,EAAE,SAAS,QAAQ,EAAE;gBAC9E,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,kBAAkB;YAClB,UAAU,IAAI,CAAC;YAEf,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,OAAO;IACP,YAAY,OAAO;QACjB,IAAI;YACF,oBAAoB;YACpB,2DAA2D;YAE3D,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,YAAY,UAAU,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,SAAS,EAAE;YACrE,IAAI,cAAc,CAAC,GAAG;gBACpB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,cAAc;gBAClB,GAAG,SAAS,CAAC,UAAU;gBACvB,GAAG,QAAQ;gBACX,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,kBAAkB;YAClB,SAAS,CAAC,UAAU,GAAG;YAEvB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,OAAO;IACP,YAAY,OAAO;QACjB,IAAI;YACF,oBAAoB;YACpB,2CAA2C;YAE3C,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,YAAY,UAAU,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC1D,IAAI,cAAc,CAAC,GAAG;gBACpB,MAAM,IAAI,MAAM;YAClB;YAEA,kBAAkB;YAClB,UAAU,MAAM,CAAC,WAAW;QAC9B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/services/role.service.ts"], "sourcesContent": ["import { PaginatedResponse, PaginationParams, Role, RoleCreateInput, RoleUpdateInput } from '@/types';\nimport api from './api';\n\n// 模拟数据\nconst mockRoles: Role[] = [\n  {\n    id: '1',\n    name: '超级管理员',\n    description: '拥有所有权限',\n    menuIds: ['1', '2', '3', '4', '5', '6', '7', '8'],\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: '2',\n    name: '普通用户',\n    description: '拥有基本权限',\n    menuIds: ['1', '2'],\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: '3',\n    name: '访客',\n    description: '只有查看权限',\n    menuIds: ['1'],\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n];\n\n// 角色服务\nexport const roleService = {\n  // 获取角色列表\n  getRoles: async (params: PaginationParams): Promise<PaginatedResponse<Role>> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.get<PaginatedResponse<Role>>('/roles', { params });\n      \n      // 模拟获取角色列表\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const { page, pageSize } = params;\n      const startIndex = (page - 1) * pageSize;\n      const endIndex = startIndex + pageSize;\n      const paginatedRoles = mockRoles.slice(startIndex, endIndex);\n      \n      return {\n        data: paginatedRoles,\n        total: mockRoles.length,\n        page,\n        pageSize\n      };\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取所有角色（不分页）\n  getAllRoles: async (): Promise<Role[]> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.get<Role[]>('/roles/all');\n      \n      // 模拟获取所有角色\n      await new Promise(resolve => setTimeout(resolve, 300));\n      \n      return mockRoles;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取单个角色\n  getRole: async (id: string): Promise<Role> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.get<Role>(`/roles/${id}`);\n      \n      // 模拟获取单个角色\n      await new Promise(resolve => setTimeout(resolve, 300));\n      \n      const role = mockRoles.find(role => role.id === id);\n      if (!role) {\n        throw new Error('角色不存在');\n      }\n      \n      return role;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 创建角色\n  createRole: async (roleData: RoleCreateInput): Promise<Role> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.post<Role>('/roles', roleData);\n      \n      // 模拟创建角色\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const newRole: Role = {\n        id: String(mockRoles.length + 1),\n        name: roleData.name,\n        description: roleData.description,\n        menuIds: roleData.menuIds,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n      };\n      \n      // 在实际应用中，这里会由后端处理\n      mockRoles.push(newRole);\n      \n      return newRole;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 更新角色\n  updateRole: async (roleData: RoleUpdateInput): Promise<Role> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.put<Role>(`/roles/${roleData.id}`, roleData);\n      \n      // 模拟更新角色\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const roleIndex = mockRoles.findIndex(role => role.id === roleData.id);\n      if (roleIndex === -1) {\n        throw new Error('角色不存在');\n      }\n      \n      const updatedRole = {\n        ...mockRoles[roleIndex],\n        ...roleData,\n        updatedAt: new Date().toISOString(),\n      };\n      \n      // 在实际应用中，这里会由后端处理\n      mockRoles[roleIndex] = updatedRole;\n      \n      return updatedRole;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 删除角色\n  deleteRole: async (id: string): Promise<void> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.delete<void>(`/roles/${id}`);\n      \n      // 模拟删除角色\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const roleIndex = mockRoles.findIndex(role => role.id === id);\n      if (roleIndex === -1) {\n        throw new Error('角色不存在');\n      }\n      \n      // 在实际应用中，这里会由后端处理\n      mockRoles.splice(roleIndex, 1);\n    } catch (error) {\n      throw error;\n    }\n  },\n};\n\nexport default roleService;\n"], "names": [], "mappings": ";;;;AAGA,OAAO;AACP,MAAM,YAAoB;IACxB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,SAAS;YAAC;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;SAAI;QACjD,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,SAAS;YAAC;YAAK;SAAI;QACnB,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,SAAS;YAAC;SAAI;QACd,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;CACD;AAGM,MAAM,cAAc;IACzB,SAAS;IACT,UAAU,OAAO;QACf,IAAI;YACF,oBAAoB;YACpB,iEAAiE;YAEjE,WAAW;YACX,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG;YAC3B,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;YAChC,MAAM,WAAW,aAAa;YAC9B,MAAM,iBAAiB,UAAU,KAAK,CAAC,YAAY;YAEnD,OAAO;gBACL,MAAM;gBACN,OAAO,UAAU,MAAM;gBACvB;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,cAAc;IACd,aAAa;QACX,IAAI;YACF,oBAAoB;YACpB,wCAAwC;YAExC,WAAW;YACX,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,SAAS;IACT,SAAS,OAAO;QACd,IAAI;YACF,oBAAoB;YACpB,wCAAwC;YAExC,WAAW;YACX,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAChD,IAAI,CAAC,MAAM;gBACT,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,OAAO;IACP,YAAY,OAAO;QACjB,IAAI;YACF,oBAAoB;YACpB,6CAA6C;YAE7C,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,UAAgB;gBACpB,IAAI,OAAO,UAAU,MAAM,GAAG;gBAC9B,MAAM,SAAS,IAAI;gBACnB,aAAa,SAAS,WAAW;gBACjC,SAAS,SAAS,OAAO;gBACzB,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,kBAAkB;YAClB,UAAU,IAAI,CAAC;YAEf,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,OAAO;IACP,YAAY,OAAO;QACjB,IAAI;YACF,oBAAoB;YACpB,2DAA2D;YAE3D,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,YAAY,UAAU,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,SAAS,EAAE;YACrE,IAAI,cAAc,CAAC,GAAG;gBACpB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,cAAc;gBAClB,GAAG,SAAS,CAAC,UAAU;gBACvB,GAAG,QAAQ;gBACX,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,kBAAkB;YAClB,SAAS,CAAC,UAAU,GAAG;YAEvB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,OAAO;IACP,YAAY,OAAO;QACjB,IAAI;YACF,oBAAoB;YACpB,2CAA2C;YAE3C,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,YAAY,UAAU,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC1D,IAAI,cAAc,CAAC,GAAG;gBACpB,MAAM,IAAI,MAAM;YAClB;YAEA,kBAAkB;YAClB,UAAU,MAAM,CAAC,WAAW;QAC9B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 916, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/services/menu.service.ts"], "sourcesContent": ["import { Menu, MenuCreateInput, MenuUpdateInput, PaginatedResponse, PaginationParams } from '@/types';\nimport api from './api';\n\n// 模拟数据\nconst mockMenus: Menu[] = [\n  {\n    id: '1',\n    name: '仪表盘',\n    path: '/dashboard',\n    icon: 'dashboard',\n    parentId: null,\n    order: 1,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: '2',\n    name: '系统管理',\n    path: '/system',\n    icon: 'setting',\n    parentId: null,\n    order: 2,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: '3',\n    name: '用户管理',\n    path: '/system/users',\n    icon: 'user',\n    parentId: '2',\n    order: 1,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: '4',\n    name: '角色管理',\n    path: '/system/roles',\n    icon: 'team',\n    parentId: '2',\n    order: 2,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: '5',\n    name: '菜单管理',\n    path: '/system/menus',\n    icon: 'menu',\n    parentId: '2',\n    order: 3,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n];\n\n// 构建菜单树\nconst buildMenuTree = (menus: Menu[]): Menu[] => {\n  const menuMap = new Map<string, Menu>();\n  const result: Menu[] = [];\n\n  // 先将所有菜单放入 Map 中\n  menus.forEach(menu => {\n    menuMap.set(menu.id, { ...menu, children: [] });\n  });\n\n  // 构建树形结构\n  menus.forEach(menu => {\n    const menuWithChildren = menuMap.get(menu.id)!;\n    \n    if (menu.parentId === null) {\n      // 根菜单\n      result.push(menuWithChildren);\n    } else {\n      // 子菜单\n      const parentMenu = menuMap.get(menu.parentId);\n      if (parentMenu) {\n        if (!parentMenu.children) {\n          parentMenu.children = [];\n        }\n        parentMenu.children.push(menuWithChildren);\n      }\n    }\n  });\n\n  // 对菜单进行排序\n  const sortMenus = (menus: Menu[]): Menu[] => {\n    return menus\n      .sort((a, b) => a.order - b.order)\n      .map(menu => ({\n        ...menu,\n        children: menu.children ? sortMenus(menu.children) : undefined,\n      }));\n  };\n\n  return sortMenus(result);\n};\n\n// 菜单服务\nexport const menuService = {\n  // 获取菜单列表\n  getMenus: async (params: PaginationParams): Promise<PaginatedResponse<Menu>> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.get<PaginatedResponse<Menu>>('/menus', { params });\n      \n      // 模拟获取菜单列表\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const { page, pageSize } = params;\n      const startIndex = (page - 1) * pageSize;\n      const endIndex = startIndex + pageSize;\n      const paginatedMenus = mockMenus.slice(startIndex, endIndex);\n      \n      return {\n        data: paginatedMenus,\n        total: mockMenus.length,\n        page,\n        pageSize\n      };\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取菜单树\n  getMenuTree: async (): Promise<Menu[]> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.get<Menu[]>('/menus/tree');\n      \n      // 模拟获取菜单树\n      await new Promise(resolve => setTimeout(resolve, 300));\n      \n      return buildMenuTree(mockMenus);\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取单个菜单\n  getMenu: async (id: string): Promise<Menu> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.get<Menu>(`/menus/${id}`);\n      \n      // 模拟获取单个菜单\n      await new Promise(resolve => setTimeout(resolve, 300));\n      \n      const menu = mockMenus.find(menu => menu.id === id);\n      if (!menu) {\n        throw new Error('菜单不存在');\n      }\n      \n      return menu;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 创建菜单\n  createMenu: async (menuData: MenuCreateInput): Promise<Menu> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.post<Menu>('/menus', menuData);\n      \n      // 模拟创建菜单\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const newMenu: Menu = {\n        id: String(mockMenus.length + 1),\n        name: menuData.name,\n        path: menuData.path,\n        icon: menuData.icon,\n        parentId: menuData.parentId || null,\n        order: menuData.order || mockMenus.length + 1,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n      };\n      \n      // 在实际应用中，这里会由后端处理\n      mockMenus.push(newMenu);\n      \n      return newMenu;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 更新菜单\n  updateMenu: async (menuData: MenuUpdateInput): Promise<Menu> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.put<Menu>(`/menus/${menuData.id}`, menuData);\n      \n      // 模拟更新菜单\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const menuIndex = mockMenus.findIndex(menu => menu.id === menuData.id);\n      if (menuIndex === -1) {\n        throw new Error('菜单不存在');\n      }\n      \n      const updatedMenu = {\n        ...mockMenus[menuIndex],\n        ...menuData,\n        updatedAt: new Date().toISOString(),\n      };\n      \n      // 在实际应用中，这里会由后端处理\n      mockMenus[menuIndex] = updatedMenu;\n      \n      return updatedMenu;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 删除菜单\n  deleteMenu: async (id: string): Promise<void> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.delete<void>(`/menus/${id}`);\n      \n      // 模拟删除菜单\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const menuIndex = mockMenus.findIndex(menu => menu.id === id);\n      if (menuIndex === -1) {\n        throw new Error('菜单不存在');\n      }\n      \n      // 在实际应用中，这里会由后端处理\n      mockMenus.splice(menuIndex, 1);\n    } catch (error) {\n      throw error;\n    }\n  },\n};\n\nexport default menuService;\n"], "names": [], "mappings": ";;;;AAGA,OAAO;AACP,MAAM,YAAoB;IACxB;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;QACP,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;QACP,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;QACP,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;QACP,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;QACP,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;CACD;AAED,QAAQ;AACR,MAAM,gBAAgB,CAAC;IACrB,MAAM,UAAU,IAAI;IACpB,MAAM,SAAiB,EAAE;IAEzB,iBAAiB;IACjB,MAAM,OAAO,CAAC,CAAA;QACZ,QAAQ,GAAG,CAAC,KAAK,EAAE,EAAE;YAAE,GAAG,IAAI;YAAE,UAAU,EAAE;QAAC;IAC/C;IAEA,SAAS;IACT,MAAM,OAAO,CAAC,CAAA;QACZ,MAAM,mBAAmB,QAAQ,GAAG,CAAC,KAAK,EAAE;QAE5C,IAAI,KAAK,QAAQ,KAAK,MAAM;YAC1B,MAAM;YACN,OAAO,IAAI,CAAC;QACd,OAAO;YACL,MAAM;YACN,MAAM,aAAa,QAAQ,GAAG,CAAC,KAAK,QAAQ;YAC5C,IAAI,YAAY;gBACd,IAAI,CAAC,WAAW,QAAQ,EAAE;oBACxB,WAAW,QAAQ,GAAG,EAAE;gBAC1B;gBACA,WAAW,QAAQ,CAAC,IAAI,CAAC;YAC3B;QACF;IACF;IAEA,UAAU;IACV,MAAM,YAAY,CAAC;QACjB,OAAO,MACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACZ,GAAG,IAAI;gBACP,UAAU,KAAK,QAAQ,GAAG,UAAU,KAAK,QAAQ,IAAI;YACvD,CAAC;IACL;IAEA,OAAO,UAAU;AACnB;AAGO,MAAM,cAAc;IACzB,SAAS;IACT,UAAU,OAAO;QACf,IAAI;YACF,oBAAoB;YACpB,iEAAiE;YAEjE,WAAW;YACX,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG;YAC3B,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;YAChC,MAAM,WAAW,aAAa;YAC9B,MAAM,iBAAiB,UAAU,KAAK,CAAC,YAAY;YAEnD,OAAO;gBACL,MAAM;gBACN,OAAO,UAAU,MAAM;gBACvB;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,QAAQ;IACR,aAAa;QACX,IAAI;YACF,oBAAoB;YACpB,yCAAyC;YAEzC,UAAU;YACV,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,OAAO,cAAc;QACvB,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,SAAS;IACT,SAAS,OAAO;QACd,IAAI;YACF,oBAAoB;YACpB,wCAAwC;YAExC,WAAW;YACX,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAChD,IAAI,CAAC,MAAM;gBACT,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,OAAO;IACP,YAAY,OAAO;QACjB,IAAI;YACF,oBAAoB;YACpB,6CAA6C;YAE7C,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,UAAgB;gBACpB,IAAI,OAAO,UAAU,MAAM,GAAG;gBAC9B,MAAM,SAAS,IAAI;gBACnB,MAAM,SAAS,IAAI;gBACnB,MAAM,SAAS,IAAI;gBACnB,UAAU,SAAS,QAAQ,IAAI;gBAC/B,OAAO,SAAS,KAAK,IAAI,UAAU,MAAM,GAAG;gBAC5C,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,kBAAkB;YAClB,UAAU,IAAI,CAAC;YAEf,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,OAAO;IACP,YAAY,OAAO;QACjB,IAAI;YACF,oBAAoB;YACpB,2DAA2D;YAE3D,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,YAAY,UAAU,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,SAAS,EAAE;YACrE,IAAI,cAAc,CAAC,GAAG;gBACpB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,cAAc;gBAClB,GAAG,SAAS,CAAC,UAAU;gBACvB,GAAG,QAAQ;gBACX,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,kBAAkB;YAClB,SAAS,CAAC,UAAU,GAAG;YAEvB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,OAAO;IACP,YAAY,OAAO;QACjB,IAAI;YACF,oBAAoB;YACpB,2CAA2C;YAE3C,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,YAAY,UAAU,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC1D,IAAI,cAAc,CAAC,GAAG;gBACpB,MAAM,IAAI,MAAM;YAClB;YAEA,kBAAkB;YAClB,UAAU,MAAM,CAAC,WAAW;QAC9B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 1135, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/services/index.ts"], "sourcesContent": ["export * from './api';\nexport * from './auth.service';\nexport * from './user.service';\nexport * from './role.service';\nexport * from './menu.service';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1168, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport { authService } from '@/services';\nimport { LoginCredentials, User } from '@/types';\nimport { createContext, useCallback, useContext, useEffect, useState } from 'react';\n\ninterface AuthContextType {\n  user: User | null;\n  loading: boolean;\n  error: string | null;\n  login: (credentials: LoginCredentials) => Promise<void>;\n  logout: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const AuthProvider = ({ children }: { children: React.ReactNode }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // 获取当前用户信息\n  const fetchCurrentUser = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // 检查是否已登录\n      if (!authService.isAuthenticated()) {\n        setUser(null);\n        setLoading(false);\n        return;\n      }\n\n      // 获取当前用户信息\n      const currentUser = await authService.getCurrentUser();\n      setUser(currentUser);\n    } catch (error) {\n      console.error('获取用户信息失败:', error);\n      setError('获取用户信息失败');\n      // 清除无效的 token\n      if (typeof window !== 'undefined') {\n        localStorage.removeItem('token');\n      }\n      setUser(null);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  // 登录\n  const login = async (credentials: LoginCredentials) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // 调用登录 API\n      const response = await authService.login(credentials);\n\n      // 保存用户信息\n      setUser(response.user);\n    } catch (error: any) {\n      console.error('登录失败:', error);\n      setError(error.message || '登录失败');\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 登出\n  const logout = async () => {\n    try {\n      setLoading(true);\n\n      // 调用登出 API\n      await authService.logout();\n\n      // 清除用户信息\n      setUser(null);\n    } catch (error) {\n      console.error('登出失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 初始化时获取用户信息\n  useEffect(() => {\n    fetchCurrentUser();\n  }, [fetchCurrentUser]);\n\n  return (\n    <AuthContext.Provider value={{ user, loading, error, login, logout }}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// 自定义钩子，用于在组件中访问认证上下文\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "names": [], "mappings": ";;;;;;AAEA;AAAA;AAEA;;;AAJA;;;AAcA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,WAAW;IACX,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YACnC,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,UAAU;gBACV,IAAI,CAAC,qIAAA,CAAA,cAAW,CAAC,eAAe,IAAI;oBAClC,QAAQ;oBACR,WAAW;oBACX;gBACF;gBAEA,WAAW;gBACX,MAAM,cAAc,MAAM,qIAAA,CAAA,cAAW,CAAC,cAAc;gBACpD,QAAQ;YACV,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;gBAC3B,SAAS;gBACT,cAAc;gBACd,wCAAmC;oBACjC,aAAa,UAAU,CAAC;gBAC1B;gBACA,QAAQ;YACV,SAAU;gBACR,WAAW;YACb;QACF;qDAAG,EAAE;IAEL,KAAK;IACL,MAAM,QAAQ,OAAO;QACnB,IAAI;YACF,WAAW;YACX,SAAS;YAET,WAAW;YACX,MAAM,WAAW,MAAM,qIAAA,CAAA,cAAW,CAAC,KAAK,CAAC;YAEzC,SAAS;YACT,QAAQ,SAAS,IAAI;QACvB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,SAAS;YACvB,SAAS,MAAM,OAAO,IAAI;YAC1B,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,KAAK;IACL,MAAM,SAAS;QACb,IAAI;YACF,WAAW;YAEX,WAAW;YACX,MAAM,qIAAA,CAAA,cAAW,CAAC,MAAM;YAExB,SAAS;YACT,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACzB,SAAU;YACR,WAAW;QACb;IACF;IAEA,aAAa;IACb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG;QAAC;KAAiB;IAErB,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAM;YAAS;YAAO;YAAO;QAAO;kBAChE;;;;;;AAGP;GAjFa;KAAA;AAoFN,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa;uCAQE", "debugId": null}}, {"offset": {"line": 1293, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/components/layout/ClientLayout.tsx"], "sourcesContent": ["'use client';\n\nimport I18nProvider from '@/i18n/I18nProvider';\nimport { AuthProvider } from '@/contexts/AuthContext';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport enUS from 'antd/locale/en_US';\nimport React, { useEffect, useState } from 'react';\nimport { useTranslation } from 'react-i18next';\n\ninterface ClientLayoutProps {\n  children: React.ReactNode;\n}\n\nexport default function ClientLayout({ children }: ClientLayoutProps) {\n  const [mounted, setMounted] = useState(false);\n  const { i18n } = useTranslation();\n\n  // 使用 useMemo 缓存 Ant Design 语言包，避免不必要的重新渲染\n  const antdLocale = React.useMemo(() => {\n    return i18n.language === 'en' ? enUS : zhCN;\n  }, [i18n.language]);\n\n  // 解决 hydration 不匹配问题\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  if (!mounted) {\n    return null;\n  }\n\n  return (\n    <I18nProvider>\n      <ConfigProvider\n        locale={antdLocale}\n        theme={{\n        token: {\n          colorPrimary: '#1677ff',\n          // 减少动画效果，提高响应速度\n          motion: {\n            easeInOut: 'cubic-bezier(0.645, 0.045, 0.355, 1)',\n            easeOut: 'cubic-bezier(0.215, 0.61, 0.355, 1)',\n            easeIn: 'cubic-bezier(0.55, 0.055, 0.675, 0.19)',\n          },\n          // 减少过渡时间\n          motionDurationMid: '0.1s',\n          motionDurationSlow: '0.2s',\n        },\n        components: {\n          Table: {\n            // 禁用表格动画，提高性能\n            motion: false,\n          },\n          Menu: {\n            // 减少菜单动画时间\n            motionDurationSlow: '0.1s',\n          },\n          Layout: {\n            // 减少布局动画时间\n            motionDurationMid: '0.1s',\n          },\n        },\n      }}\n    >\n      <AuthProvider>\n        {/* 使用动态导入避免循环依赖 */}\n        {mounted ? (\n          <div id=\"app-container\">\n            {children}\n          </div>\n        ) : null}\n      </AuthProvider>\n    </ConfigProvider>\n    </I18nProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;AARA;;;;;;;;AAce,SAAS,aAAa,EAAE,QAAQ,EAAqB;;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,iBAAc,AAAD;IAE9B,0CAA0C;IAC1C,MAAM,aAAa,6JAAA,CAAA,UAAK,CAAC,OAAO;4CAAC;YAC/B,OAAO,KAAK,QAAQ,KAAK,OAAO,0IAAA,CAAA,UAAI,GAAG,0IAAA,CAAA,UAAI;QAC7C;2CAAG;QAAC,KAAK,QAAQ;KAAC;IAElB,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,WAAW;QACb;iCAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,6LAAC,+HAAA,CAAA,UAAY;kBACX,cAAA,6LAAC,yNAAA,CAAA,iBAAc;YACb,QAAQ;YACR,OAAO;gBACP,OAAO;oBACL,cAAc;oBACd,gBAAgB;oBAChB,QAAQ;wBACN,WAAW;wBACX,SAAS;wBACT,QAAQ;oBACV;oBACA,SAAS;oBACT,mBAAmB;oBACnB,oBAAoB;gBACtB;gBACA,YAAY;oBACV,OAAO;wBACL,cAAc;wBACd,QAAQ;oBACV;oBACA,MAAM;wBACJ,WAAW;wBACX,oBAAoB;oBACtB;oBACA,QAAQ;wBACN,WAAW;wBACX,mBAAmB;oBACrB;gBACF;YACF;sBAEA,cAAA,6LAAC,kIAAA,CAAA,eAAY;0BAEV,wBACC,6LAAC;oBAAI,IAAG;8BACL;;;;;2BAED;;;;;;;;;;;;;;;;AAKZ;GA9DwB;;QAEL,mKAAA,CAAA,iBAAc;;;KAFT", "debugId": null}}]}