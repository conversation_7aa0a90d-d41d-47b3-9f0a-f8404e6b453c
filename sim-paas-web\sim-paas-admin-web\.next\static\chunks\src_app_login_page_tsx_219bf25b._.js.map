{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/app/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/contexts/AuthContext';\nimport { LoginCredentials } from '@/types';\nimport { LockOutlined, UserOutlined } from '@ant-design/icons';\nimport { Al<PERSON>, Button, Card, Form, Input, Typography } from 'antd';\nimport { useRouter } from 'next/navigation';\nimport { useEffect, useState } from 'react';\nimport { useTranslation } from 'react-i18next';\n\nconst { Title } = Typography;\n\nexport default function LoginPage() {\n  const { t } = useTranslation();\n  const { login, user, loading, error } = useAuth();\n  const router = useRouter();\n  const [form] = Form.useForm();\n  const [loginError, setLoginError] = useState<string | null>(null);\n\n  // 如果用户已登录，重定向到仪表盘\n  useEffect(() => {\n    if (user) {\n      router.push('/dashboard');\n    }\n  }, [user, router]);\n\n  // 处理登录\n  const handleLogin = async (values: LoginCredentials) => {\n    try {\n      setLoginError(null);\n      await login(values);\n      router.push('/dashboard');\n    } catch (error: any) {\n      setLoginError(error.message || t('login.loginFailed'));\n    }\n  };\n\n  return (\n    <div style={{\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      minHeight: '100vh',\n      background: `url('/images/login-bg.jpg') no-repeat center center fixed`,\n      backgroundSize: 'cover',\n      position: 'relative'\n    }}>\n      {/* 添加一个半透明的遮罩层，使文字更易读 */}\n      <div style={{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: 'rgba(0, 0, 0, 0.4)',\n        zIndex: 1\n      }} />\n      <Card style={{\n        width: 400,\n        boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',\n        backdropFilter: 'blur(10px)',\n        backgroundColor: 'rgba(255, 255, 255, 0.9)',\n        zIndex: 2\n      }}>\n        <div style={{ textAlign: 'center', marginBottom: 24 }}>\n          <Title level={2} style={{ margin: 0 }}>{t('login.title')}</Title>\n          <p style={{ marginTop: 8, color: 'rgba(0, 0, 0, 0.45)' }}>{t('login.subtitle')}</p>\n        </div>\n\n        {(loginError || error) && (\n          <Alert\n            message={loginError || error}\n            type=\"error\"\n            showIcon\n            style={{ marginBottom: 24 }}\n          />\n        )}\n\n        <Form\n          form={form}\n          name=\"login\"\n          initialValues={{ username: 'admin', password: 'admin123' }}\n          onFinish={handleLogin}\n          autoComplete=\"off\"\n          layout=\"vertical\"\n        >\n          <Form.Item\n            name=\"username\"\n            rules={[{ required: true, message: t('user.pleaseEnterUsername') }]}\n          >\n            <Input\n              prefix={<UserOutlined />}\n              placeholder={t('common.username')}\n              size=\"large\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"password\"\n            rules={[{ required: true, message: t('user.pleaseEnterPassword') }]}\n          >\n            <Input.Password\n              prefix={<LockOutlined />}\n              placeholder={t('common.password')}\n              size=\"large\"\n            />\n          </Form.Item>\n\n          <Form.Item>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading}\n              block\n              size=\"large\"\n            >\n              {t('login.loginButton')}\n            </Button>\n          </Form.Item>\n\n          <div style={{ textAlign: 'center', color: 'rgba(0, 0, 0, 0.45)' }}>\n            <p>{t('login.defaultAccount')}</p>\n          </div>\n        </Form>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;;;AARA;;;;;;;AAUA,MAAM,EAAE,KAAK,EAAE,GAAG,6LAAA,CAAA,aAAU;AAEb,SAAS;;IACtB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC9C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5D,kBAAkB;IAClB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,MAAM;gBACR,OAAO,IAAI,CAAC;YACd;QACF;8BAAG;QAAC;QAAM;KAAO;IAEjB,OAAO;IACP,MAAM,cAAc,OAAO;QACzB,IAAI;YACF,cAAc;YACd,MAAM,MAAM;YACZ,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,cAAc,MAAM,OAAO,IAAI,EAAE;QACnC;IACF;IAEA,qBACE,6LAAC;QAAI,OAAO;YACV,SAAS;YACT,gBAAgB;YAChB,YAAY;YACZ,WAAW;YACX,YAAY,CAAC,yDAAyD,CAAC;YACvE,gBAAgB;YAChB,UAAU;QACZ;;0BAEE,6LAAC;gBAAI,OAAO;oBACV,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,iBAAiB;oBACjB,QAAQ;gBACV;;;;;;0BACA,6LAAC,iLAAA,CAAA,OAAI;gBAAC,OAAO;oBACX,OAAO;oBACP,WAAW;oBACX,gBAAgB;oBAChB,iBAAiB;oBACjB,QAAQ;gBACV;;kCACE,6LAAC;wBAAI,OAAO;4BAAE,WAAW;4BAAU,cAAc;wBAAG;;0CAClD,6LAAC;gCAAM,OAAO;gCAAG,OAAO;oCAAE,QAAQ;gCAAE;0CAAI,EAAE;;;;;;0CAC1C,6LAAC;gCAAE,OAAO;oCAAE,WAAW;oCAAG,OAAO;gCAAsB;0CAAI,EAAE;;;;;;;;;;;;oBAG9D,CAAC,cAAc,KAAK,mBACnB,6LAAC,mLAAA,CAAA,QAAK;wBACJ,SAAS,cAAc;wBACvB,MAAK;wBACL,QAAQ;wBACR,OAAO;4BAAE,cAAc;wBAAG;;;;;;kCAI9B,6LAAC,iLAAA,CAAA,OAAI;wBACH,MAAM;wBACN,MAAK;wBACL,eAAe;4BAAE,UAAU;4BAAS,UAAU;wBAAW;wBACzD,UAAU;wBACV,cAAa;wBACb,QAAO;;0CAEP,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;gCACR,MAAK;gCACL,OAAO;oCAAC;wCAAE,UAAU;wCAAM,SAAS,EAAE;oCAA4B;iCAAE;0CAEnE,cAAA,6LAAC,mLAAA,CAAA,QAAK;oCACJ,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;oCACrB,aAAa,EAAE;oCACf,MAAK;;;;;;;;;;;0CAIT,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;gCACR,MAAK;gCACL,OAAO;oCAAC;wCAAE,UAAU;wCAAM,SAAS,EAAE;oCAA4B;iCAAE;0CAEnE,cAAA,6LAAC,mLAAA,CAAA,QAAK,CAAC,QAAQ;oCACb,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;oCACrB,aAAa,EAAE;oCACf,MAAK;;;;;;;;;;;0CAIT,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;0CACR,cAAA,6LAAC,qMAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAS;oCACT,SAAS;oCACT,KAAK;oCACL,MAAK;8CAEJ,EAAE;;;;;;;;;;;0CAIP,6LAAC;gCAAI,OAAO;oCAAE,WAAW;oCAAU,OAAO;gCAAsB;0CAC9D,cAAA,6LAAC;8CAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlB;GAnHwB;;QACR,mKAAA,CAAA,iBAAc;QACY,kIAAA,CAAA,UAAO;QAChC,qIAAA,CAAA,YAAS;QACT,iLAAA,CAAA,OAAI,CAAC;;;KAJE", "debugId": null}}]}