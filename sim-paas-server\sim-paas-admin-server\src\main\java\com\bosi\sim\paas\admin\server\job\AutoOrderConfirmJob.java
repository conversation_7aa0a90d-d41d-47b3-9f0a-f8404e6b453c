package com.bosi.sim.paas.admin.server.job;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bosi.sim.paas.dao.enums.ConfigKeyEnum;
import com.bosi.sim.paas.dao.enums.sds.SdsOrderStatusEnum;
import com.bosi.sim.paas.dao.mapper.ads.AdsConfigMapper;
import com.bosi.sim.paas.dao.mapper.oms.OmsOrderMapper;
import com.bosi.sim.paas.dao.model.ads.AdsConfig;
import com.bosi.sim.paas.dao.model.sds.OmsOrder;
import com.bosi.sim.paas.admin.server.config.rabbitmq.RabbitMqOrderConfirmProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component("orderAutoConfirmJob")
@Slf4j
public class AutoOrderConfirmJob {

    @Autowired
    private OmsOrderMapper orderMapper;

    @Autowired
    private AdsConfigMapper cmsConfigMapper;

    @Autowired
    private RabbitMqOrderConfirmProducer rabbitMqOrderConfirmProducer;

    public void run() {
        try {
            LambdaQueryWrapper<AdsConfig> cmsConfigLambdaQueryWrapper = new LambdaQueryWrapper<>();
            cmsConfigLambdaQueryWrapper.eq(AdsConfig::getConfigKey, ConfigKeyEnum.ORDER_AUTOCONFIRM_KEY.getKey());
            AdsConfig config = cmsConfigMapper.selectOne(cmsConfigLambdaQueryWrapper);
            int defaultDay = 15;
            if (config != null) {
                defaultDay = Integer.parseInt(config.getConfigValue());
            }
            LambdaQueryWrapper<OmsOrder> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(OmsOrder::getStatus, SdsOrderStatusEnum.DELIVERYED.getOrderStatus());
            queryWrapper.le(OmsOrder::getDeliveryTime, "current_timestamp - interval '" + defaultDay + " days'");
            queryWrapper.last("limit 100");
            List<OmsOrder> orderList = orderMapper.selectList(queryWrapper);
            int result = 0;
            if (CollUtil.isNotEmpty(orderList)) {
                for (OmsOrder omsOrder : orderList) {
                    OmsOrder order = new OmsOrder();
                    order.setStatus(SdsOrderStatusEnum.OVER.getOrderStatus());
                    order.setConfirmTime(new Date());
                    order.setWhetherConfirm(true);
                    LambdaQueryWrapper<OmsOrder> updateWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(OmsOrder::getStatus, SdsOrderStatusEnum.DELIVERYED.getOrderStatus());
                    queryWrapper.eq(OmsOrder::getId, omsOrder.getId());
                    int rs = orderMapper.update(order, updateWrapper);
                    if (rs == 1) {
                        result++;
                        try {
                            JSONObject orderJson = new JSONObject();
                            orderJson.put("orderId", omsOrder.getId());
                            rabbitMqOrderConfirmProducer.sendMessage(orderJson);
                        } catch (Exception e) {
                            log.error("rabbitMqOrderConfirmProducer send msg error", e);
                        }
                    } else {
                        log.info("修改结果集失败,id={}", omsOrder.getId());
                    }
                }
            }
            log.info("自动确认收货任务执行完成，数量:{}", result);
        } catch (Exception e) {
            log.error("自动确认收货任务执行异常", e);
        }
    }


}
