package com.bosi.sim.paas.open.server.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bosi.sim.paas.common.core.api.BizCode;
import com.bosi.sim.paas.common.core.api.SystemCode;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.MessageUtils;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.common.redis.service.RedisService;
import com.bosi.sim.paas.common.security.auth.CacheTokenUser;
import com.bosi.sim.paas.common.security.auth.CurrentAuthorization;
import com.bosi.sim.paas.common.security.service.TokenService;
import com.bosi.sim.paas.common.security.util.PasswordUtils;
import com.bosi.sim.paas.dao.mapper.sds.SdsMemberLevelMapper;
import com.bosi.sim.paas.dao.mapper.sds.SdsMemberMapper;
import com.bosi.sim.paas.dao.model.sds.SdsMember;
import com.bosi.sim.paas.dao.model.sds.SdsMemberLevel;
import com.bosi.sim.paas.open.server.config.google.GmailAPISendMail;
import com.bosi.sim.paas.open.server.domain.dto.MemberParterAuthInfoDto;
import com.bosi.sim.paas.open.server.domain.param.MemberRegisterParam;
import com.bosi.sim.paas.open.server.enums.ShopTokenExtendKeyEnum;
import com.bosi.sim.paas.open.server.enums.VerifyCodeKeyEnum;
import com.bosi.sim.paas.open.server.service.MmsMemberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 会员管理Service实现类
 */
@Service
public class MmsMemberServiceImpl implements MmsMemberService {
    @Autowired
    private SdsMemberMapper memberMapper;

    @Autowired
    private SdsMemberLevelMapper memberLevelMapper;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private GmailAPISendMail gmailAPISendMail;

    @Override
    public SdsMember getByUsername(String username) {
        LambdaQueryWrapper<SdsMember> example = new LambdaQueryWrapper<>();
        example.eq(SdsMember::getUsername, username);
        return memberMapper.selectOne(example);
    }

    @Override
    public SdsMember getById(String id) {
        return memberMapper.selectById(id);
    }

    @Override
    public void register(MemberRegisterParam memberRegisterParam) {
        //验证验证码
        if (verifyAuthCode(VerifyCodeKeyEnum.REGIST_CODE, memberRegisterParam.getAuthCode(), memberRegisterParam.getEmail())) {
            throw BizException.build(BizCode.MEMBER_VERIFY_CODE_ERROR);
        }
        //查询是否已有该用户
        LambdaQueryWrapper<SdsMember> example = new LambdaQueryWrapper<>();
        example.eq(SdsMember::getUsername, memberRegisterParam.getUsername());
        example.or();
        example.eq(SdsMember::getEmail, memberRegisterParam.getEmail());
        long memberNumber = memberMapper.selectCount(example);
        if (memberNumber > 0L) {
            throw BizException.build(BizCode.MEMBER_USERNAME_REPEAT);
        }
        LambdaQueryWrapper<SdsMemberLevel> levelExample = new LambdaQueryWrapper<>();
        levelExample.eq(SdsMemberLevel::getWhetherDefault, true);
        SdsMemberLevel memberLevel = memberLevelMapper.selectOne(levelExample);
        //没有该用户进行添加操作
        SdsMember umsMember = new SdsMember();
        umsMember.setUsername(memberRegisterParam.getUsername());
        umsMember.setEmail(memberRegisterParam.getEmail());
        umsMember.setPassword(PasswordUtils.encryptPassword(memberRegisterParam.getPassword()));
        umsMember.setWhetherEnable(true);
        umsMember.setMemberLevelId(memberLevel.getId());
        memberMapper.insert(umsMember);
    }

    @Override
    public String loginOrRegisterForPartner(MemberParterAuthInfoDto authInfoDto) {
        LambdaQueryWrapper<SdsMember> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.eq(SdsMember::getPartnerUniqueId, authInfoDto.getPartnerUniqueId());
        SdsMember umsMember = memberMapper.selectOne(userWrapper);
        if (umsMember == null) {
            if (StringUtils.isNotEmpty(authInfoDto.getEmail())) {
                LambdaQueryWrapper<SdsMember> emailVerifyWrapper = new LambdaQueryWrapper<>();
                emailVerifyWrapper.eq(SdsMember::getEmail, authInfoDto.getEmail());
                if (memberMapper.selectOne(emailVerifyWrapper) != null) {
                    throw BizException.build(BizCode.MEMBER_EMAIL_REPEAT);
                }
            }
            LambdaQueryWrapper<SdsMemberLevel> levelExample = new LambdaQueryWrapper<>();
            levelExample.eq(SdsMemberLevel::getWhetherDefault, true);
            SdsMemberLevel memberLevel = memberLevelMapper.selectOne(levelExample);
            //没有该用户进行添加操作
            umsMember = new SdsMember();
            umsMember.setUsername(RandomUtil.randomString(16));
            umsMember.setEmail(authInfoDto.getEmail());
            umsMember.setNickname(authInfoDto.getNickname());
            umsMember.setPhone(authInfoDto.getPhone());
            umsMember.setGender(authInfoDto.getGender());
            umsMember.setSourceType(authInfoDto.getSourceType());
            umsMember.setWhetherEnable(true);
            umsMember.setMemberLevelId(memberLevel.getId());
            umsMember.setPartnerUniqueId(authInfoDto.getPartnerUniqueId());
            memberMapper.insert(umsMember);
        }
        CacheTokenUser userInfo = new CacheTokenUser();
        JSONObject extentJson = new JSONObject();
        extentJson.put(ShopTokenExtendKeyEnum.NICKNAME_KEY.getKey(), umsMember.getNickname());
        userInfo.setExtendJson(extentJson);
        userInfo.setUserId(umsMember.getId());
        userInfo.setUsername(umsMember.getUsername());
        return tokenService.generateToken(userInfo);
    }

    @Override
    public void generateAuthCode(String key, String email) {
        VerifyCodeKeyEnum shopRedisKeyEnum = VerifyCodeKeyEnum.getByBusiness(key);
        String code = RandomUtil.randomNumbers(6);
        String msg;
        if (VerifyCodeKeyEnum.REGIST_CODE.equals(shopRedisKeyEnum)) {
            msg = MessageUtils.getMessage(BizCode.MEMBER_REGIST_VERIFY_CODE, code);
        } else if (VerifyCodeKeyEnum.UPDATE_PASSWORD.equals(shopRedisKeyEnum)) {
            msg = MessageUtils.getMessage(BizCode.MEMBER_UPDATEPWD_VERIFY_CODE, code);
        } else {
            throw BizException.build(SystemCode.VALIDATE_FAILED);
        }
        gmailAPISendMail.sendEmail(email, "Verification Code", msg);
        redisService.set(shopRedisKeyEnum, email, code, shopRedisKeyEnum.expireIn(), shopRedisKeyEnum.timeUnit());
    }

    @Override
    public void updatePassword(String username, String password, String authCode) {
        LambdaQueryWrapper<SdsMember> example = new LambdaQueryWrapper<>();
        example.eq(SdsMember::getUsername, username);
        SdsMember member = memberMapper.selectOne(example);
        if (member == null) {
            throw BizException.build(BizCode.MEMBER_ACCOUNT_NOT_FOUND);
        }
        if (verifyAuthCode(VerifyCodeKeyEnum.UPDATE_PASSWORD, authCode, member.getEmail())) {
            throw BizException.build(BizCode.MEMBER_VERIFY_CODE_ERROR);
        }
        member.setPassword(PasswordUtils.encryptPassword(password));
        memberMapper.updateById(member);
    }

    @Override
    public String login(String username, String password) {
        //密码需要客户端加密后传递
        SdsMember mmsMember = getByUsername(username);
        if (mmsMember == null) {
            throw BizException.build(BizCode.MEMBER_LOGIN_ACCOUNT_OR_PASSWORD_ERROR);
        }
        if (!PasswordUtils.matchesPassword(password, mmsMember.getPassword())) {
            throw BizException.build(BizCode.MEMBER_LOGIN_ACCOUNT_OR_PASSWORD_ERROR);
        }
        if (!mmsMember.getWhetherEnable()) {
            throw BizException.build(BizCode.MEMBER_ACCOUNT_DISABLE);
        }
        CacheTokenUser userInfo = new CacheTokenUser();
        JSONObject extentJson = new JSONObject();
        extentJson.put(ShopTokenExtendKeyEnum.NICKNAME_KEY.getKey(), mmsMember.getNickname());
        userInfo.setExtendJson(extentJson);
        userInfo.setUserId(mmsMember.getId());
        userInfo.setUsername(mmsMember.getUsername());
        return tokenService.generateToken(userInfo);
    }


    @Override
    public void refreshToken() {
        tokenService.refreshToken(CurrentAuthorization.getUsername());
    }

    //对输入的验证码进行校验
    private boolean verifyAuthCode(VerifyCodeKeyEnum shopRedisKeyEnum, String authCode, String email) {
        if (StringUtils.isEmpty(authCode)) {
            return true;
        }
        String realAuthCode = redisService.get(shopRedisKeyEnum, email);
        return !authCode.equals(realAuthCode);
    }

}
