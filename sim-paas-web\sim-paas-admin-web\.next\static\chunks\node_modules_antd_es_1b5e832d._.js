(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/antd/es/button/index.js [app-client] (ecmascript) <locals> <export default as Button>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Button": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/button/index.js [app-client] (ecmascript) <locals>");
}}),
"[project]/node_modules/antd/es/grid/hooks/useGutter.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useGutter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$responsiveObserver$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/responsiveObserver.js [app-client] (ecmascript)");
;
function useGutter(gutter, screens) {
    const results = [
        undefined,
        undefined
    ];
    const normalizedGutter = Array.isArray(gutter) ? gutter : [
        gutter,
        undefined
    ];
    // By default use as `xs`
    const mergedScreens = screens || {
        xs: true,
        sm: true,
        md: true,
        lg: true,
        xl: true,
        xxl: true
    };
    normalizedGutter.forEach((g, index)=>{
        if (typeof g === 'object' && g !== null) {
            for(let i = 0; i < __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$responsiveObserver$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["responsiveArray"].length; i++){
                const breakpoint = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$responsiveObserver$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["responsiveArray"][i];
                if (mergedScreens[breakpoint] && g[breakpoint] !== undefined) {
                    results[index] = g[breakpoint];
                    break;
                }
            }
        } else {
            results[index] = g;
        }
    });
    return results;
}
}}),
"[project]/node_modules/antd/es/grid/RowContext.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
const RowContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])({});
const __TURBOPACK__default__export__ = RowContext;
}}),
"[project]/node_modules/antd/es/grid/style/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getMediaSize": (()=>getMediaSize),
    "prepareColComponentToken": (()=>prepareColComponentToken),
    "prepareRowComponentToken": (()=>prepareRowComponentToken),
    "useColStyle": (()=>useColStyle),
    "useRowStyle": (()=>useRowStyle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/util/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/util/genStyleUtils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs-utils/es/util/statistic.js [app-client] (ecmascript) <export merge as mergeToken>");
;
;
// ============================== Row-Shared ==============================
const genGridRowStyle = (token)=>{
    const { componentCls } = token;
    return {
        // Grid system
        [componentCls]: {
            display: 'flex',
            flexFlow: 'row wrap',
            minWidth: 0,
            '&::before, &::after': {
                display: 'flex'
            },
            '&-no-wrap': {
                flexWrap: 'nowrap'
            },
            // The origin of the X-axis
            '&-start': {
                justifyContent: 'flex-start'
            },
            // The center of the X-axis
            '&-center': {
                justifyContent: 'center'
            },
            // The opposite of the X-axis
            '&-end': {
                justifyContent: 'flex-end'
            },
            '&-space-between': {
                justifyContent: 'space-between'
            },
            '&-space-around': {
                justifyContent: 'space-around'
            },
            '&-space-evenly': {
                justifyContent: 'space-evenly'
            },
            // Align at the top
            '&-top': {
                alignItems: 'flex-start'
            },
            // Align at the center
            '&-middle': {
                alignItems: 'center'
            },
            '&-bottom': {
                alignItems: 'flex-end'
            }
        }
    };
};
// ============================== Col-Shared ==============================
const genGridColStyle = (token)=>{
    const { componentCls } = token;
    return {
        // Grid system
        [componentCls]: {
            position: 'relative',
            maxWidth: '100%',
            // Prevent columns from collapsing when empty
            minHeight: 1
        }
    };
};
const genLoopGridColumnsStyle = (token, sizeCls)=>{
    const { prefixCls, componentCls, gridColumns } = token;
    const gridColumnsStyle = {};
    for(let i = gridColumns; i >= 0; i--){
        if (i === 0) {
            gridColumnsStyle[`${componentCls}${sizeCls}-${i}`] = {
                display: 'none'
            };
            gridColumnsStyle[`${componentCls}-push-${i}`] = {
                insetInlineStart: 'auto'
            };
            gridColumnsStyle[`${componentCls}-pull-${i}`] = {
                insetInlineEnd: 'auto'
            };
            gridColumnsStyle[`${componentCls}${sizeCls}-push-${i}`] = {
                insetInlineStart: 'auto'
            };
            gridColumnsStyle[`${componentCls}${sizeCls}-pull-${i}`] = {
                insetInlineEnd: 'auto'
            };
            gridColumnsStyle[`${componentCls}${sizeCls}-offset-${i}`] = {
                marginInlineStart: 0
            };
            gridColumnsStyle[`${componentCls}${sizeCls}-order-${i}`] = {
                order: 0
            };
        } else {
            gridColumnsStyle[`${componentCls}${sizeCls}-${i}`] = [
                // https://github.com/ant-design/ant-design/issues/44456
                // Form set `display: flex` on Col which will override `display: block`.
                // Let's get it from css variable to support override.
                {
                    ['--ant-display']: 'block',
                    // Fallback to display if variable not support
                    display: 'block'
                },
                {
                    display: 'var(--ant-display)',
                    flex: `0 0 ${i / gridColumns * 100}%`,
                    maxWidth: `${i / gridColumns * 100}%`
                }
            ];
            gridColumnsStyle[`${componentCls}${sizeCls}-push-${i}`] = {
                insetInlineStart: `${i / gridColumns * 100}%`
            };
            gridColumnsStyle[`${componentCls}${sizeCls}-pull-${i}`] = {
                insetInlineEnd: `${i / gridColumns * 100}%`
            };
            gridColumnsStyle[`${componentCls}${sizeCls}-offset-${i}`] = {
                marginInlineStart: `${i / gridColumns * 100}%`
            };
            gridColumnsStyle[`${componentCls}${sizeCls}-order-${i}`] = {
                order: i
            };
        }
    }
    // Flex CSS Var
    gridColumnsStyle[`${componentCls}${sizeCls}-flex`] = {
        flex: `var(--${prefixCls}${sizeCls}-flex)`
    };
    return gridColumnsStyle;
};
const genGridStyle = (token, sizeCls)=>genLoopGridColumnsStyle(token, sizeCls);
const genGridMediaStyle = (token, screenSize, sizeCls)=>({
        [`@media (min-width: ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(screenSize)})`]: Object.assign({}, genGridStyle(token, sizeCls))
    });
const prepareRowComponentToken = ()=>({});
const prepareColComponentToken = ()=>({});
const useRowStyle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genStyleHooks"])('Grid', genGridRowStyle, prepareRowComponentToken);
const getMediaSize = (token)=>{
    const mediaSizesMap = {
        xs: token.screenXSMin,
        sm: token.screenSMMin,
        md: token.screenMDMin,
        lg: token.screenLGMin,
        xl: token.screenXLMin,
        xxl: token.screenXXLMin
    };
    return mediaSizesMap;
};
const useColStyle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genStyleHooks"])('Grid', (token)=>{
    const gridToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__["mergeToken"])(token, {
        gridColumns: 24 // Row is divided into 24 parts in Grid
    });
    const gridMediaSizesMap = getMediaSize(gridToken);
    delete gridMediaSizesMap.xs;
    return [
        genGridColStyle(gridToken),
        genGridStyle(gridToken, ''),
        genGridStyle(gridToken, '-xs'),
        Object.keys(gridMediaSizesMap).map((key)=>genGridMediaStyle(gridToken, gridMediaSizesMap[key], `-${key}`)).reduce((pre, cur)=>Object.assign(Object.assign({}, pre), cur), {})
    ];
}, prepareColComponentToken);
}}),
"[project]/node_modules/antd/es/grid/row.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$responsiveObserver$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/responsiveObserver.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$grid$2f$hooks$2f$useBreakpoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/grid/hooks/useBreakpoint.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$grid$2f$hooks$2f$useGutter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/grid/hooks/useGutter.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$grid$2f$RowContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/grid/RowContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$grid$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/grid/style/index.js [app-client] (ecmascript)");
"use client";
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
;
;
;
;
;
;
;
;
const _RowAligns = [
    'top',
    'middle',
    'bottom',
    'stretch'
];
const _RowJustify = [
    'start',
    'end',
    'center',
    'space-around',
    'space-between',
    'space-evenly'
];
function useMergedPropByScreen(oriProp, screen) {
    const [prop, setProp] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(typeof oriProp === 'string' ? oriProp : '');
    const calcMergedAlignOrJustify = ()=>{
        if (typeof oriProp === 'string') {
            setProp(oriProp);
        }
        if (typeof oriProp !== 'object') {
            return;
        }
        for(let i = 0; i < __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$responsiveObserver$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["responsiveArray"].length; i++){
            const breakpoint = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$responsiveObserver$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["responsiveArray"][i];
            // if do not match, do nothing
            if (!screen || !screen[breakpoint]) {
                continue;
            }
            const curVal = oriProp[breakpoint];
            if (curVal !== undefined) {
                setProp(curVal);
                return;
            }
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useMergedPropByScreen.useEffect": ()=>{
            calcMergedAlignOrJustify();
        }
    }["useMergedPropByScreen.useEffect"], [
        JSON.stringify(oriProp),
        screen
    ]);
    return prop;
}
const Row = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((props, ref)=>{
    const { prefixCls: customizePrefixCls, justify, align, className, style, children, gutter = 0, wrap } = props, others = __rest(props, [
        "prefixCls",
        "justify",
        "align",
        "className",
        "style",
        "children",
        "gutter",
        "wrap"
    ]);
    const { getPrefixCls, direction } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfigContext"]);
    const screens = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$grid$2f$hooks$2f$useBreakpoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(true, null);
    const mergedAlign = useMergedPropByScreen(align, screens);
    const mergedJustify = useMergedPropByScreen(justify, screens);
    const prefixCls = getPrefixCls('row', customizePrefixCls);
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$grid$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRowStyle"])(prefixCls);
    const gutters = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$grid$2f$hooks$2f$useGutter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(gutter, screens);
    const classes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, {
        [`${prefixCls}-no-wrap`]: wrap === false,
        [`${prefixCls}-${mergedJustify}`]: mergedJustify,
        [`${prefixCls}-${mergedAlign}`]: mergedAlign,
        [`${prefixCls}-rtl`]: direction === 'rtl'
    }, className, hashId, cssVarCls);
    // Add gutter related style
    const rowStyle = {};
    const horizontalGutter = gutters[0] != null && gutters[0] > 0 ? gutters[0] / -2 : undefined;
    if (horizontalGutter) {
        rowStyle.marginLeft = horizontalGutter;
        rowStyle.marginRight = horizontalGutter;
    }
    // "gutters" is a new array in each rendering phase, it'll make 'React.useMemo' effectless.
    // So we deconstruct "gutters" variable here.
    const [gutterH, gutterV] = gutters;
    rowStyle.rowGap = gutterV;
    const rowContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Row.useMemo[rowContext]": ()=>({
                gutter: [
                    gutterH,
                    gutterV
                ],
                wrap
            })
    }["Row.useMemo[rowContext]"], [
        gutterH,
        gutterV,
        wrap
    ]);
    return wrapCSSVar(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$grid$2f$RowContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Provider, {
        value: rowContext
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", Object.assign({}, others, {
        className: classes,
        style: Object.assign(Object.assign({}, rowStyle), style),
        ref: ref
    }), children)));
});
if ("TURBOPACK compile-time truthy", 1) {
    Row.displayName = 'Row';
}
const __TURBOPACK__default__export__ = Row;
}}),
"[project]/node_modules/antd/es/grid/row.js [app-client] (ecmascript) <export default as Row>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Row": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$grid$2f$row$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$grid$2f$row$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/grid/row.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/antd/es/grid/col.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$grid$2f$RowContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/grid/RowContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$grid$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/grid/style/index.js [app-client] (ecmascript)");
"use client";
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
;
;
;
;
;
function parseFlex(flex) {
    if (typeof flex === 'number') {
        return `${flex} ${flex} auto`;
    }
    if (/^\d+(\.\d+)?(px|em|rem|%)$/.test(flex)) {
        return `0 0 ${flex}`;
    }
    return flex;
}
const sizes = [
    'xs',
    'sm',
    'md',
    'lg',
    'xl',
    'xxl'
];
const Col = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((props, ref)=>{
    const { getPrefixCls, direction } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfigContext"]);
    const { gutter, wrap } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$grid$2f$RowContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
    const { prefixCls: customizePrefixCls, span, order, offset, push, pull, className, children, flex, style } = props, others = __rest(props, [
        "prefixCls",
        "span",
        "order",
        "offset",
        "push",
        "pull",
        "className",
        "children",
        "flex",
        "style"
    ]);
    const prefixCls = getPrefixCls('col', customizePrefixCls);
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$grid$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useColStyle"])(prefixCls);
    // ===================== Size ======================
    const sizeStyle = {};
    let sizeClassObj = {};
    sizes.forEach((size)=>{
        let sizeProps = {};
        const propSize = props[size];
        if (typeof propSize === 'number') {
            sizeProps.span = propSize;
        } else if (typeof propSize === 'object') {
            sizeProps = propSize || {};
        }
        delete others[size];
        sizeClassObj = Object.assign(Object.assign({}, sizeClassObj), {
            [`${prefixCls}-${size}-${sizeProps.span}`]: sizeProps.span !== undefined,
            [`${prefixCls}-${size}-order-${sizeProps.order}`]: sizeProps.order || sizeProps.order === 0,
            [`${prefixCls}-${size}-offset-${sizeProps.offset}`]: sizeProps.offset || sizeProps.offset === 0,
            [`${prefixCls}-${size}-push-${sizeProps.push}`]: sizeProps.push || sizeProps.push === 0,
            [`${prefixCls}-${size}-pull-${sizeProps.pull}`]: sizeProps.pull || sizeProps.pull === 0,
            [`${prefixCls}-rtl`]: direction === 'rtl'
        });
        // Responsive flex layout
        if (sizeProps.flex) {
            sizeClassObj[`${prefixCls}-${size}-flex`] = true;
            sizeStyle[`--${prefixCls}-${size}-flex`] = parseFlex(sizeProps.flex);
        }
    });
    // ==================== Normal =====================
    const classes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, {
        [`${prefixCls}-${span}`]: span !== undefined,
        [`${prefixCls}-order-${order}`]: order,
        [`${prefixCls}-offset-${offset}`]: offset,
        [`${prefixCls}-push-${push}`]: push,
        [`${prefixCls}-pull-${pull}`]: pull
    }, className, sizeClassObj, hashId, cssVarCls);
    const mergedStyle = {};
    // Horizontal gutter use padding
    if (gutter && gutter[0] > 0) {
        const horizontalGutter = gutter[0] / 2;
        mergedStyle.paddingLeft = horizontalGutter;
        mergedStyle.paddingRight = horizontalGutter;
    }
    if (flex) {
        mergedStyle.flex = parseFlex(flex);
        // Hack for Firefox to avoid size issue
        // https://github.com/ant-design/ant-design/pull/20023#issuecomment-564389553
        if (wrap === false && !mergedStyle.minWidth) {
            mergedStyle.minWidth = 0;
        }
    }
    // ==================== Render =====================
    return wrapCSSVar(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", Object.assign({}, others, {
        style: Object.assign(Object.assign(Object.assign({}, mergedStyle), style), sizeStyle),
        className: classes,
        ref: ref
    }), children));
});
if ("TURBOPACK compile-time truthy", 1) {
    Col.displayName = 'Col';
}
const __TURBOPACK__default__export__ = Col;
}}),
"[project]/node_modules/antd/es/_util/getAllowClear.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CloseCircleFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/CloseCircleFilled.js [app-client] (ecmascript)");
"use client";
;
;
const getAllowClear = (allowClear)=>{
    let mergedAllowClear;
    if (typeof allowClear === 'object' && (allowClear === null || allowClear === void 0 ? void 0 : allowClear.clearIcon)) {
        mergedAllowClear = allowClear;
    } else if (allowClear) {
        mergedAllowClear = {
            clearIcon: /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CloseCircleFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null)
        };
    }
    return mergedAllowClear;
};
const __TURBOPACK__default__export__ = getAllowClear;
}}),
"[project]/node_modules/antd/es/_util/statusUtils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getMergedStatus": (()=>getMergedStatus),
    "getStatusClassNames": (()=>getStatusClassNames)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
;
const _InputStatuses = [
    'warning',
    'error',
    ''
];
function getStatusClassNames(prefixCls, status, hasFeedback) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        [`${prefixCls}-status-success`]: status === 'success',
        [`${prefixCls}-status-warning`]: status === 'warning',
        [`${prefixCls}-status-error`]: status === 'error',
        [`${prefixCls}-status-validating`]: status === 'validating',
        [`${prefixCls}-has-feedback`]: hasFeedback
    });
}
const getMergedStatus = (contextStatus, customStatus)=>customStatus || contextStatus;
}}),
"[project]/node_modules/antd/es/_util/ActionButton.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useState$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useState.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/button/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$buttonHelpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/button/buttonHelpers.js [app-client] (ecmascript)");
"use client";
;
;
;
;
function isThenable(thing) {
    return !!(thing === null || thing === void 0 ? void 0 : thing.then);
}
const ActionButton = (props)=>{
    const { type, children, prefixCls, buttonProps, close, autoFocus, emitEvent, isSilent, quitOnNullishReturnValue, actionFn } = props;
    const clickedRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    const buttonRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useState$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false);
    const onInternalClose = function() {
        close === null || close === void 0 ? void 0 : close.apply(void 0, arguments);
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ActionButton.useEffect": ()=>{
            let timeoutId = null;
            if (autoFocus) {
                timeoutId = setTimeout({
                    "ActionButton.useEffect": ()=>{
                        var _a;
                        (_a = buttonRef.current) === null || _a === void 0 ? void 0 : _a.focus({
                            preventScroll: true
                        });
                    }
                }["ActionButton.useEffect"]);
            }
            return ({
                "ActionButton.useEffect": ()=>{
                    if (timeoutId) {
                        clearTimeout(timeoutId);
                    }
                }
            })["ActionButton.useEffect"];
        }
    }["ActionButton.useEffect"], []);
    const handlePromiseOnOk = (returnValueOfOnOk)=>{
        if (!isThenable(returnValueOfOnOk)) {
            return;
        }
        setLoading(true);
        returnValueOfOnOk.then(function() {
            setLoading(false, true);
            onInternalClose.apply(void 0, arguments);
            clickedRef.current = false;
        }, (e)=>{
            // See: https://github.com/ant-design/ant-design/issues/6183
            setLoading(false, true);
            clickedRef.current = false;
            // Do not throw if is `await` mode
            if (isSilent === null || isSilent === void 0 ? void 0 : isSilent()) {
                return;
            }
            return Promise.reject(e);
        });
    };
    const onClick = (e)=>{
        if (clickedRef.current) {
            return;
        }
        clickedRef.current = true;
        if (!actionFn) {
            onInternalClose();
            return;
        }
        let returnValueOfOnOk;
        if (emitEvent) {
            returnValueOfOnOk = actionFn(e);
            if (quitOnNullishReturnValue && !isThenable(returnValueOfOnOk)) {
                clickedRef.current = false;
                onInternalClose(e);
                return;
            }
        } else if (actionFn.length) {
            returnValueOfOnOk = actionFn(close);
            // https://github.com/ant-design/ant-design/issues/23358
            clickedRef.current = false;
        } else {
            returnValueOfOnOk = actionFn();
            if (!isThenable(returnValueOfOnOk)) {
                onInternalClose();
                return;
            }
        }
        handlePromiseOnOk(returnValueOfOnOk);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"], Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$buttonHelpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertLegacyProps"])(type), {
        onClick: onClick,
        loading: loading,
        prefixCls: prefixCls
    }, buttonProps, {
        ref: buttonRef
    }), children);
};
const __TURBOPACK__default__export__ = ActionButton;
}}),
"[project]/node_modules/antd/es/_util/hooks/useClosable.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useClosable),
    "pickClosable": (()=>pickClosable)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CloseOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/CloseOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$pickAttrs$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/pickAttrs.js [app-client] (ecmascript)");
"use client";
;
;
;
function pickClosable(context) {
    if (!context) {
        return undefined;
    }
    return {
        closable: context.closable,
        closeIcon: context.closeIcon
    };
}
/** Convert `closable` and `closeIcon` to config object */ function useClosableConfig(closableCollection) {
    const { closable, closeIcon } = closableCollection || {};
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useMemo({
        "useClosableConfig.useMemo": ()=>{
            if (// If `closable`, whatever rest be should be true
            !closable && (closable === false || closeIcon === false || closeIcon === null)) {
                return false;
            }
            if (closable === undefined && closeIcon === undefined) {
                return null;
            }
            let closableConfig = {
                closeIcon: typeof closeIcon !== 'boolean' && closeIcon !== null ? closeIcon : undefined
            };
            if (closable && typeof closable === 'object') {
                closableConfig = Object.assign(Object.assign({}, closableConfig), closable);
            }
            return closableConfig;
        }
    }["useClosableConfig.useMemo"], [
        closable,
        closeIcon
    ]);
}
/**
 * Assign object without `undefined` field. Will skip if is `false`.
 * This helps to handle both closableConfig or false
 */ function assignWithoutUndefined() {
    const target = {};
    for(var _len = arguments.length, objList = new Array(_len), _key = 0; _key < _len; _key++){
        objList[_key] = arguments[_key];
    }
    objList.forEach((obj)=>{
        if (obj) {
            Object.keys(obj).forEach((key)=>{
                if (obj[key] !== undefined) {
                    target[key] = obj[key];
                }
            });
        }
    });
    return target;
}
/** Use same object to support `useMemo` optimization */ const EmptyFallbackCloseCollection = {};
function useClosable(propCloseCollection, contextCloseCollection) {
    let fallbackCloseCollection = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : EmptyFallbackCloseCollection;
    // Align the `props`, `context` `fallback` to config object first
    const propCloseConfig = useClosableConfig(propCloseCollection);
    const contextCloseConfig = useClosableConfig(contextCloseCollection);
    const closeBtnIsDisabled = typeof propCloseConfig !== 'boolean' ? !!(propCloseConfig === null || propCloseConfig === void 0 ? void 0 : propCloseConfig.disabled) : false;
    const mergedFallbackCloseCollection = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useMemo({
        "useClosable.useMemo[mergedFallbackCloseCollection]": ()=>Object.assign({
                closeIcon: /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CloseOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null)
            }, fallbackCloseCollection)
    }["useClosable.useMemo[mergedFallbackCloseCollection]"], [
        fallbackCloseCollection
    ]);
    // Use fallback logic to fill the config
    const mergedClosableConfig = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useMemo({
        "useClosable.useMemo[mergedClosableConfig]": ()=>{
            // ================ Props First ================
            // Skip if prop is disabled
            if (propCloseConfig === false) {
                return false;
            }
            if (propCloseConfig) {
                return assignWithoutUndefined(mergedFallbackCloseCollection, contextCloseConfig, propCloseConfig);
            }
            // =============== Context Second ==============
            // Skip if context is disabled
            if (contextCloseConfig === false) {
                return false;
            }
            if (contextCloseConfig) {
                return assignWithoutUndefined(mergedFallbackCloseCollection, contextCloseConfig);
            }
            // ============= Fallback Default ==============
            return !mergedFallbackCloseCollection.closable ? false : mergedFallbackCloseCollection;
        }
    }["useClosable.useMemo[mergedClosableConfig]"], [
        propCloseConfig,
        contextCloseConfig,
        mergedFallbackCloseCollection
    ]);
    // Calculate the final closeIcon
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useMemo({
        "useClosable.useMemo": ()=>{
            if (mergedClosableConfig === false) {
                return [
                    false,
                    null,
                    closeBtnIsDisabled
                ];
            }
            const { closeIconRender } = mergedFallbackCloseCollection;
            const { closeIcon } = mergedClosableConfig;
            let mergedCloseIcon = closeIcon;
            if (mergedCloseIcon !== null && mergedCloseIcon !== undefined) {
                // Wrap the closeIcon if needed
                if (closeIconRender) {
                    mergedCloseIcon = closeIconRender(closeIcon);
                }
                // Wrap the closeIcon with aria props
                const ariaProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$pickAttrs$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(mergedClosableConfig, true);
                if (Object.keys(ariaProps).length) {
                    mergedCloseIcon = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isValidElement(mergedCloseIcon) ? /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].cloneElement(mergedCloseIcon, ariaProps) : /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("span", Object.assign({}, ariaProps), mergedCloseIcon);
                }
            }
            return [
                true,
                mergedCloseIcon,
                closeBtnIsDisabled
            ];
        }
    }["useClosable.useMemo"], [
        mergedClosableConfig,
        mergedFallbackCloseCollection
    ]);
}
}}),
"[project]/node_modules/antd/es/_util/styleChecker.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "canUseDocElement": (()=>canUseDocElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$canUseDom$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/Dom/canUseDom.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$styleChecker$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/Dom/styleChecker.js [app-client] (ecmascript)");
;
;
const canUseDocElement = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$canUseDom$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])() && window.document.documentElement;
;
}}),
"[project]/node_modules/antd/es/_util/hooks/usePatchElement.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>usePatchElement)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
function usePatchElement() {
    const [elements, setElements] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const patchElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "usePatchElement.useCallback[patchElement]": (element)=>{
            // append a new element to elements (and create a new ref)
            setElements({
                "usePatchElement.useCallback[patchElement]": (originElements)=>[].concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(originElements), [
                        element
                    ])
            }["usePatchElement.useCallback[patchElement]"]);
            // return a function that removes the new element out of elements (and create a new ref)
            // it works a little like useEffect
            return ({
                "usePatchElement.useCallback[patchElement]": ()=>{
                    setElements({
                        "usePatchElement.useCallback[patchElement]": (originElements)=>originElements.filter({
                                "usePatchElement.useCallback[patchElement]": (ele)=>ele !== element
                            }["usePatchElement.useCallback[patchElement]"])
                    }["usePatchElement.useCallback[patchElement]"]);
                }
            })["usePatchElement.useCallback[patchElement]"];
        }
    }["usePatchElement.useCallback[patchElement]"], []);
    return [
        elements,
        patchElement
    ];
}
}}),
"[project]/node_modules/antd/es/_util/hooks/useMultipleSelect.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useMultipleSelect)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
function useMultipleSelect(getKey) {
    const [prevSelectedIndex, setPrevSelectedIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const multipleSelect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useMultipleSelect.useCallback[multipleSelect]": (currentSelectedIndex, data, selectedKeys)=>{
            const configPrevSelectedIndex = prevSelectedIndex !== null && prevSelectedIndex !== void 0 ? prevSelectedIndex : currentSelectedIndex;
            // add/delete the selected range
            const startIndex = Math.min(configPrevSelectedIndex || 0, currentSelectedIndex);
            const endIndex = Math.max(configPrevSelectedIndex || 0, currentSelectedIndex);
            const rangeKeys = data.slice(startIndex, endIndex + 1).map({
                "useMultipleSelect.useCallback[multipleSelect].rangeKeys": (item)=>getKey(item)
            }["useMultipleSelect.useCallback[multipleSelect].rangeKeys"]);
            const shouldSelected = rangeKeys.some({
                "useMultipleSelect.useCallback[multipleSelect].shouldSelected": (rangeKey)=>!selectedKeys.has(rangeKey)
            }["useMultipleSelect.useCallback[multipleSelect].shouldSelected"]);
            const changedKeys = [];
            rangeKeys.forEach({
                "useMultipleSelect.useCallback[multipleSelect]": (item)=>{
                    if (shouldSelected) {
                        if (!selectedKeys.has(item)) {
                            changedKeys.push(item);
                        }
                        selectedKeys.add(item);
                    } else {
                        selectedKeys.delete(item);
                        changedKeys.push(item);
                    }
                }
            }["useMultipleSelect.useCallback[multipleSelect]"]);
            setPrevSelectedIndex(shouldSelected ? endIndex : null);
            return changedKeys;
        }
    }["useMultipleSelect.useCallback[multipleSelect]"], [
        prevSelectedIndex
    ]);
    const updatePrevSelectedIndex = (val)=>{
        setPrevSelectedIndex(val);
    };
    return [
        multipleSelect,
        updatePrevSelectedIndex
    ];
}
}}),
"[project]/node_modules/antd/es/_util/hooks/useProxyImperativeHandle.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Proxy the dom ref with `{ nativeElement, otherFn }` type
// ref: https://github.com/ant-design/ant-design/discussions/45242
__turbopack_context__.s({
    "default": (()=>useProxyImperativeHandle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
function fillProxy(element, handler) {
    element._antProxy = element._antProxy || {};
    Object.keys(handler).forEach((key)=>{
        if (!(key in element._antProxy)) {
            const ori = element[key];
            element._antProxy[key] = ori;
            element[key] = handler[key];
        }
    });
    return element;
}
function useProxyImperativeHandle(ref, init) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, {
        "useProxyImperativeHandle.useImperativeHandle": ()=>{
            const refObj = init();
            const { nativeElement } = refObj;
            if (typeof Proxy !== 'undefined') {
                return new Proxy(nativeElement, {
                    get (obj, prop) {
                        if (refObj[prop]) {
                            return refObj[prop];
                        }
                        return Reflect.get(obj, prop);
                    }
                });
            }
            // Fallback of IE
            return fillProxy(nativeElement, refObj);
        }
    }["useProxyImperativeHandle.useImperativeHandle"]);
}
}}),
"[project]/node_modules/antd/es/_util/easings.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "easeInOutCubic": (()=>easeInOutCubic)
});
function easeInOutCubic(t, b, c, d) {
    const cc = c - b;
    // biome-ignore lint: it is a common easing function
    t /= d / 2;
    if (t < 1) {
        return cc / 2 * t * t * t + b;
    }
    // biome-ignore lint: it is a common easing function
    return cc / 2 * ((t -= 2) * t * t + 2) + b;
}
}}),
"[project]/node_modules/antd/es/_util/getScroll.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "isWindow": (()=>isWindow)
});
function isWindow(obj) {
    return obj !== null && obj !== undefined && obj === obj.window;
}
const getScroll = (target)=>{
    var _a, _b;
    if (typeof window === 'undefined') {
        return 0;
    }
    let result = 0;
    if (isWindow(target)) {
        result = target.pageYOffset;
    } else if (target instanceof Document) {
        result = target.documentElement.scrollTop;
    } else if (target instanceof HTMLElement) {
        result = target.scrollTop;
    } else if (target) {
        // According to the type inference, the `target` is `never` type.
        // Since we configured the loose mode type checking, and supports mocking the target with such shape below::
        //    `{ documentElement: { scrollLeft: 200, scrollTop: 400 } }`,
        //    the program may falls into this branch.
        // Check the corresponding tests for details. Don't sure what is the real scenario this happens.
        /* biome-ignore lint/complexity/useLiteralKeys: target is a never type */ /* eslint-disable-next-line dot-notation */ result = target['scrollTop'];
    }
    if (target && !isWindow(target) && typeof result !== 'number') {
        result = (_b = ((_a = target.ownerDocument) !== null && _a !== void 0 ? _a : target).documentElement) === null || _b === void 0 ? void 0 : _b.scrollTop;
    }
    return result;
};
const __TURBOPACK__default__export__ = getScroll;
}}),
"[project]/node_modules/antd/es/_util/scrollTo.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>scrollTo)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$raf$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/raf.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$easings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/easings.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$getScroll$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/getScroll.js [app-client] (ecmascript)");
;
;
;
function scrollTo(y) {
    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    const { getContainer = ()=>window, callback, duration = 450 } = options;
    const container = getContainer();
    const scrollTop = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$getScroll$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(container);
    const startTime = Date.now();
    const frameFunc = ()=>{
        const timestamp = Date.now();
        const time = timestamp - startTime;
        const nextScrollTop = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$easings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["easeInOutCubic"])(time > duration ? duration : time, scrollTop, y, duration);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$getScroll$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isWindow"])(container)) {
            container.scrollTo(window.pageXOffset, nextScrollTop);
        } else if (container instanceof Document || container.constructor.name === 'HTMLDocument') {
            container.documentElement.scrollTop = nextScrollTop;
        } else {
            container.scrollTop = nextScrollTop;
        }
        if (time < duration) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$raf$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(frameFunc);
        } else if (typeof callback === 'function') {
            callback();
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$raf$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(frameFunc);
}
}}),
"[project]/node_modules/antd/es/_util/extendsObject.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const extendsObject = function() {
    const result = Object.assign({}, arguments.length <= 0 ? undefined : arguments[0]);
    for(let i = 1; i < arguments.length; i++){
        const obj = i < 0 || arguments.length <= i ? undefined : arguments[i];
        if (obj) {
            Object.keys(obj).forEach((key)=>{
                const val = obj[key];
                if (val !== undefined) {
                    result[key] = val;
                }
            });
        }
    }
    return result;
};
const __TURBOPACK__default__export__ = extendsObject;
}}),
"[project]/node_modules/antd/es/_util/hooks/useSyncState.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useSyncState)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$hooks$2f$useForceUpdate$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/hooks/useForceUpdate.js [app-client] (ecmascript)");
;
;
function useSyncState(initialValue) {
    const ref = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(initialValue);
    const forceUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$hooks$2f$useForceUpdate$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    return [
        ()=>ref.current,
        (newValue)=>{
            ref.current = newValue;
            // re-render
            forceUpdate();
        }
    ];
}
}}),
"[project]/node_modules/antd/es/_util/capitalize.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>capitalize)
});
function capitalize(str) {
    if (typeof str !== 'string') {
        return str;
    }
    const ret = str.charAt(0).toUpperCase() + str.slice(1);
    return ret;
}
}}),
"[project]/node_modules/antd/es/skeleton/Element.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
"use client";
;
;
const Element = (props)=>{
    const { prefixCls, className, style, size, shape } = props;
    const sizeCls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        [`${prefixCls}-lg`]: size === 'large',
        [`${prefixCls}-sm`]: size === 'small'
    });
    const shapeCls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        [`${prefixCls}-circle`]: shape === 'circle',
        [`${prefixCls}-square`]: shape === 'square',
        [`${prefixCls}-round`]: shape === 'round'
    });
    const sizeStyle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Element.useMemo[sizeStyle]": ()=>typeof size === 'number' ? {
                width: size,
                height: size,
                lineHeight: `${size}px`
            } : {}
    }["Element.useMemo[sizeStyle]"], [
        size
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("span", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, sizeCls, shapeCls, className),
        style: Object.assign(Object.assign({}, sizeStyle), style)
    });
};
const __TURBOPACK__default__export__ = Element;
}}),
"[project]/node_modules/antd/es/skeleton/style/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "prepareComponentToken": (()=>prepareComponentToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$Keyframes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Keyframes$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/Keyframes.js [app-client] (ecmascript) <export default as Keyframes>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/util/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/util/genStyleUtils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs-utils/es/util/statistic.js [app-client] (ecmascript) <export merge as mergeToken>");
;
;
const skeletonClsLoading = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$Keyframes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Keyframes$3e$__["Keyframes"](`ant-skeleton-loading`, {
    '0%': {
        backgroundPosition: '100% 50%'
    },
    '100%': {
        backgroundPosition: '0 50%'
    }
});
const genSkeletonElementCommonSize = (size)=>({
        height: size,
        lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(size)
    });
const genSkeletonElementAvatarSize = (size)=>Object.assign({
        width: size
    }, genSkeletonElementCommonSize(size));
const genSkeletonColor = (token)=>({
        background: token.skeletonLoadingBackground,
        backgroundSize: '400% 100%',
        animationName: skeletonClsLoading,
        animationDuration: token.skeletonLoadingMotionDuration,
        animationTimingFunction: 'ease',
        animationIterationCount: 'infinite'
    });
const genSkeletonElementInputSize = (size, calc)=>Object.assign({
        width: calc(size).mul(5).equal(),
        minWidth: calc(size).mul(5).equal()
    }, genSkeletonElementCommonSize(size));
const genSkeletonElementAvatar = (token)=>{
    const { skeletonAvatarCls, gradientFromColor, controlHeight, controlHeightLG, controlHeightSM } = token;
    return {
        [skeletonAvatarCls]: Object.assign({
            display: 'inline-block',
            verticalAlign: 'top',
            background: gradientFromColor
        }, genSkeletonElementAvatarSize(controlHeight)),
        [`${skeletonAvatarCls}${skeletonAvatarCls}-circle`]: {
            borderRadius: '50%'
        },
        [`${skeletonAvatarCls}${skeletonAvatarCls}-lg`]: Object.assign({}, genSkeletonElementAvatarSize(controlHeightLG)),
        [`${skeletonAvatarCls}${skeletonAvatarCls}-sm`]: Object.assign({}, genSkeletonElementAvatarSize(controlHeightSM))
    };
};
const genSkeletonElementInput = (token)=>{
    const { controlHeight, borderRadiusSM, skeletonInputCls, controlHeightLG, controlHeightSM, gradientFromColor, calc } = token;
    return {
        [skeletonInputCls]: Object.assign({
            display: 'inline-block',
            verticalAlign: 'top',
            background: gradientFromColor,
            borderRadius: borderRadiusSM
        }, genSkeletonElementInputSize(controlHeight, calc)),
        [`${skeletonInputCls}-lg`]: Object.assign({}, genSkeletonElementInputSize(controlHeightLG, calc)),
        [`${skeletonInputCls}-sm`]: Object.assign({}, genSkeletonElementInputSize(controlHeightSM, calc))
    };
};
const genSkeletonElementImageSize = (size)=>Object.assign({
        width: size
    }, genSkeletonElementCommonSize(size));
const genSkeletonElementImage = (token)=>{
    const { skeletonImageCls, imageSizeBase, gradientFromColor, borderRadiusSM, calc } = token;
    return {
        [skeletonImageCls]: Object.assign(Object.assign({
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center',
            verticalAlign: 'middle',
            background: gradientFromColor,
            borderRadius: borderRadiusSM
        }, genSkeletonElementImageSize(calc(imageSizeBase).mul(2).equal())), {
            [`${skeletonImageCls}-path`]: {
                fill: '#bfbfbf'
            },
            [`${skeletonImageCls}-svg`]: Object.assign(Object.assign({}, genSkeletonElementImageSize(imageSizeBase)), {
                maxWidth: calc(imageSizeBase).mul(4).equal(),
                maxHeight: calc(imageSizeBase).mul(4).equal()
            }),
            [`${skeletonImageCls}-svg${skeletonImageCls}-svg-circle`]: {
                borderRadius: '50%'
            }
        }),
        [`${skeletonImageCls}${skeletonImageCls}-circle`]: {
            borderRadius: '50%'
        }
    };
};
const genSkeletonElementButtonShape = (token, size, buttonCls)=>{
    const { skeletonButtonCls } = token;
    return {
        [`${buttonCls}${skeletonButtonCls}-circle`]: {
            width: size,
            minWidth: size,
            borderRadius: '50%'
        },
        [`${buttonCls}${skeletonButtonCls}-round`]: {
            borderRadius: size
        }
    };
};
const genSkeletonElementButtonSize = (size, calc)=>Object.assign({
        width: calc(size).mul(2).equal(),
        minWidth: calc(size).mul(2).equal()
    }, genSkeletonElementCommonSize(size));
const genSkeletonElementButton = (token)=>{
    const { borderRadiusSM, skeletonButtonCls, controlHeight, controlHeightLG, controlHeightSM, gradientFromColor, calc } = token;
    return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({
        [skeletonButtonCls]: Object.assign({
            display: 'inline-block',
            verticalAlign: 'top',
            background: gradientFromColor,
            borderRadius: borderRadiusSM,
            width: calc(controlHeight).mul(2).equal(),
            minWidth: calc(controlHeight).mul(2).equal()
        }, genSkeletonElementButtonSize(controlHeight, calc))
    }, genSkeletonElementButtonShape(token, controlHeight, skeletonButtonCls)), {
        [`${skeletonButtonCls}-lg`]: Object.assign({}, genSkeletonElementButtonSize(controlHeightLG, calc))
    }), genSkeletonElementButtonShape(token, controlHeightLG, `${skeletonButtonCls}-lg`)), {
        [`${skeletonButtonCls}-sm`]: Object.assign({}, genSkeletonElementButtonSize(controlHeightSM, calc))
    }), genSkeletonElementButtonShape(token, controlHeightSM, `${skeletonButtonCls}-sm`));
};
// =============================== Base ===============================
const genBaseStyle = (token)=>{
    const { componentCls, skeletonAvatarCls, skeletonTitleCls, skeletonParagraphCls, skeletonButtonCls, skeletonInputCls, skeletonImageCls, controlHeight, controlHeightLG, controlHeightSM, gradientFromColor, padding, marginSM, borderRadius, titleHeight, blockRadius, paragraphLiHeight, controlHeightXS, paragraphMarginTop } = token;
    return {
        [componentCls]: {
            display: 'table',
            width: '100%',
            [`${componentCls}-header`]: {
                display: 'table-cell',
                paddingInlineEnd: padding,
                verticalAlign: 'top',
                // Avatar
                [skeletonAvatarCls]: Object.assign({
                    display: 'inline-block',
                    verticalAlign: 'top',
                    background: gradientFromColor
                }, genSkeletonElementAvatarSize(controlHeight)),
                [`${skeletonAvatarCls}-circle`]: {
                    borderRadius: '50%'
                },
                [`${skeletonAvatarCls}-lg`]: Object.assign({}, genSkeletonElementAvatarSize(controlHeightLG)),
                [`${skeletonAvatarCls}-sm`]: Object.assign({}, genSkeletonElementAvatarSize(controlHeightSM))
            },
            [`${componentCls}-content`]: {
                display: 'table-cell',
                width: '100%',
                verticalAlign: 'top',
                // Title
                [skeletonTitleCls]: {
                    width: '100%',
                    height: titleHeight,
                    background: gradientFromColor,
                    borderRadius: blockRadius,
                    [`+ ${skeletonParagraphCls}`]: {
                        marginBlockStart: controlHeightSM
                    }
                },
                // paragraph
                [skeletonParagraphCls]: {
                    padding: 0,
                    '> li': {
                        width: '100%',
                        height: paragraphLiHeight,
                        listStyle: 'none',
                        background: gradientFromColor,
                        borderRadius: blockRadius,
                        '+ li': {
                            marginBlockStart: controlHeightXS
                        }
                    }
                },
                [`${skeletonParagraphCls}> li:last-child:not(:first-child):not(:nth-child(2))`]: {
                    width: '61%'
                }
            },
            [`&-round ${componentCls}-content`]: {
                [`${skeletonTitleCls}, ${skeletonParagraphCls} > li`]: {
                    borderRadius
                }
            }
        },
        [`${componentCls}-with-avatar ${componentCls}-content`]: {
            // Title
            [skeletonTitleCls]: {
                marginBlockStart: marginSM,
                [`+ ${skeletonParagraphCls}`]: {
                    marginBlockStart: paragraphMarginTop
                }
            }
        },
        // Skeleton element
        [`${componentCls}${componentCls}-element`]: Object.assign(Object.assign(Object.assign(Object.assign({
            display: 'inline-block',
            width: 'auto'
        }, genSkeletonElementButton(token)), genSkeletonElementAvatar(token)), genSkeletonElementInput(token)), genSkeletonElementImage(token)),
        // Skeleton Block Button, Input
        [`${componentCls}${componentCls}-block`]: {
            width: '100%',
            [skeletonButtonCls]: {
                width: '100%'
            },
            [skeletonInputCls]: {
                width: '100%'
            }
        },
        // With active animation
        [`${componentCls}${componentCls}-active`]: {
            [`
        ${skeletonTitleCls},
        ${skeletonParagraphCls} > li,
        ${skeletonAvatarCls},
        ${skeletonButtonCls},
        ${skeletonInputCls},
        ${skeletonImageCls}
      `]: Object.assign({}, genSkeletonColor(token))
        }
    };
};
const prepareComponentToken = (token)=>{
    const { colorFillContent, colorFill } = token;
    const gradientFromColor = colorFillContent;
    const gradientToColor = colorFill;
    return {
        color: gradientFromColor,
        colorGradientEnd: gradientToColor,
        gradientFromColor,
        gradientToColor,
        titleHeight: token.controlHeight / 2,
        blockRadius: token.borderRadiusSM,
        paragraphMarginTop: token.marginLG + token.marginXXS,
        paragraphLiHeight: token.controlHeight / 2
    };
};
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genStyleHooks"])('Skeleton', (token)=>{
    const { componentCls, calc } = token;
    const skeletonToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__["mergeToken"])(token, {
        skeletonAvatarCls: `${componentCls}-avatar`,
        skeletonTitleCls: `${componentCls}-title`,
        skeletonParagraphCls: `${componentCls}-paragraph`,
        skeletonButtonCls: `${componentCls}-button`,
        skeletonInputCls: `${componentCls}-input`,
        skeletonImageCls: `${componentCls}-image`,
        imageSizeBase: calc(token.controlHeight).mul(1.5).equal(),
        borderRadius: 100,
        // Large number to make capsule shape
        skeletonLoadingBackground: `linear-gradient(90deg, ${token.gradientFromColor} 25%, ${token.gradientToColor} 37%, ${token.gradientFromColor} 63%)`,
        skeletonLoadingMotionDuration: '1.4s'
    });
    return [
        genBaseStyle(skeletonToken)
    ];
}, prepareComponentToken, {
    deprecatedTokens: [
        [
            'color',
            'gradientFromColor'
        ],
        [
            'colorGradientEnd',
            'gradientToColor'
        ]
    ]
});
}}),
"[project]/node_modules/antd/es/skeleton/Avatar.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$omit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/omit.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/skeleton/Element.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/skeleton/style/index.js [app-client] (ecmascript)");
"use client";
;
;
;
;
;
;
const SkeletonAvatar = (props)=>{
    const { prefixCls: customizePrefixCls, className, rootClassName, active, shape = 'circle', size = 'default' } = props;
    const { getPrefixCls } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfigContext"]);
    const prefixCls = getPrefixCls('skeleton', customizePrefixCls);
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls);
    const otherProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$omit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props, [
        'prefixCls',
        'className'
    ]);
    const cls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, `${prefixCls}-element`, {
        [`${prefixCls}-active`]: active
    }, className, rootClassName, hashId, cssVarCls);
    return wrapCSSVar(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: cls
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], Object.assign({
        prefixCls: `${prefixCls}-avatar`,
        shape: shape,
        size: size
    }, otherProps))));
};
const __TURBOPACK__default__export__ = SkeletonAvatar;
}}),
"[project]/node_modules/antd/es/skeleton/Button.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$omit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/omit.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/skeleton/Element.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/skeleton/style/index.js [app-client] (ecmascript)");
"use client";
;
;
;
;
;
;
const SkeletonButton = (props)=>{
    const { prefixCls: customizePrefixCls, className, rootClassName, active, block = false, size = 'default' } = props;
    const { getPrefixCls } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfigContext"]);
    const prefixCls = getPrefixCls('skeleton', customizePrefixCls);
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls);
    const otherProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$omit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props, [
        'prefixCls'
    ]);
    const cls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, `${prefixCls}-element`, {
        [`${prefixCls}-active`]: active,
        [`${prefixCls}-block`]: block
    }, className, rootClassName, hashId, cssVarCls);
    return wrapCSSVar(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: cls
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], Object.assign({
        prefixCls: `${prefixCls}-button`,
        size: size
    }, otherProps))));
};
const __TURBOPACK__default__export__ = SkeletonButton;
}}),
"[project]/node_modules/antd/es/skeleton/Image.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/skeleton/style/index.js [app-client] (ecmascript)");
"use client";
;
;
;
;
const path = 'M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z';
const SkeletonImage = (props)=>{
    const { prefixCls: customizePrefixCls, className, rootClassName, style, active } = props;
    const { getPrefixCls } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfigContext"]);
    const prefixCls = getPrefixCls('skeleton', customizePrefixCls);
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls);
    const cls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, `${prefixCls}-element`, {
        [`${prefixCls}-active`]: active
    }, className, rootClassName, hashId, cssVarCls);
    return wrapCSSVar(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: cls
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(`${prefixCls}-image`, className),
        style: style
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("svg", {
        viewBox: "0 0 1098 1024",
        xmlns: "http://www.w3.org/2000/svg",
        className: `${prefixCls}-image-svg`
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("title", null, "Image placeholder"), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
        d: path,
        className: `${prefixCls}-image-path`
    })))));
};
const __TURBOPACK__default__export__ = SkeletonImage;
}}),
"[project]/node_modules/antd/es/skeleton/Input.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$omit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/omit.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/skeleton/Element.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/skeleton/style/index.js [app-client] (ecmascript)");
"use client";
;
;
;
;
;
;
const SkeletonInput = (props)=>{
    const { prefixCls: customizePrefixCls, className, rootClassName, active, block, size = 'default' } = props;
    const { getPrefixCls } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfigContext"]);
    const prefixCls = getPrefixCls('skeleton', customizePrefixCls);
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls);
    const otherProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$omit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props, [
        'prefixCls'
    ]);
    const cls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, `${prefixCls}-element`, {
        [`${prefixCls}-active`]: active,
        [`${prefixCls}-block`]: block
    }, className, rootClassName, hashId, cssVarCls);
    return wrapCSSVar(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: cls
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], Object.assign({
        prefixCls: `${prefixCls}-input`,
        size: size
    }, otherProps))));
};
const __TURBOPACK__default__export__ = SkeletonInput;
}}),
"[project]/node_modules/antd/es/skeleton/Node.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/skeleton/style/index.js [app-client] (ecmascript)");
"use client";
;
;
;
;
const SkeletonNode = (props)=>{
    const { prefixCls: customizePrefixCls, className, rootClassName, style, active, children } = props;
    const { getPrefixCls } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfigContext"]);
    const prefixCls = getPrefixCls('skeleton', customizePrefixCls);
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls);
    const cls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, `${prefixCls}-element`, {
        [`${prefixCls}-active`]: active
    }, hashId, className, rootClassName, cssVarCls);
    return wrapCSSVar(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: cls
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(`${prefixCls}-image`, className),
        style: style
    }, children)));
};
const __TURBOPACK__default__export__ = SkeletonNode;
}}),
"[project]/node_modules/antd/es/skeleton/Paragraph.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
"use client";
;
;
const getWidth = (index, props)=>{
    const { width, rows = 2 } = props;
    if (Array.isArray(width)) {
        return width[index];
    }
    // last paragraph
    if (rows - 1 === index) {
        return width;
    }
    return undefined;
};
const Paragraph = (props)=>{
    const { prefixCls, className, style, rows = 0 } = props;
    const rowList = Array.from({
        length: rows
    }).map((_, index)=>/*#__PURE__*/ // eslint-disable-next-line react/no-array-index-key
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("li", {
            key: index,
            style: {
                width: getWidth(index, props)
            }
        }));
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("ul", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, className),
        style: style
    }, rowList);
};
const __TURBOPACK__default__export__ = Paragraph;
}}),
"[project]/node_modules/antd/es/skeleton/Title.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
/* eslint-disable jsx-a11y/heading-has-content */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
"use client";
;
;
const Title = (_ref)=>{
    let { prefixCls, className, width, style } = _ref;
    return(/*#__PURE__*/ // biome-ignore lint/a11y/useHeadingContent: HOC here
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("h3", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, className),
        style: Object.assign({
            width
        }, style)
    }));
};
const __TURBOPACK__default__export__ = Title;
}}),
"[project]/node_modules/antd/es/skeleton/Skeleton.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Avatar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/skeleton/Avatar.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/skeleton/Button.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/skeleton/Element.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/skeleton/Image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Input$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/skeleton/Input.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Node$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/skeleton/Node.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Paragraph$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/skeleton/Paragraph.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/skeleton/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Title$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/skeleton/Title.js [app-client] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
function getComponentProps(prop) {
    if (prop && typeof prop === 'object') {
        return prop;
    }
    return {};
}
function getAvatarBasicProps(hasTitle, hasParagraph) {
    if (hasTitle && !hasParagraph) {
        // Square avatar
        return {
            size: 'large',
            shape: 'square'
        };
    }
    return {
        size: 'large',
        shape: 'circle'
    };
}
function getTitleBasicProps(hasAvatar, hasParagraph) {
    if (!hasAvatar && hasParagraph) {
        return {
            width: '38%'
        };
    }
    if (hasAvatar && hasParagraph) {
        return {
            width: '50%'
        };
    }
    return {};
}
function getParagraphBasicProps(hasAvatar, hasTitle) {
    const basicProps = {};
    // Width
    if (!hasAvatar || !hasTitle) {
        basicProps.width = '61%';
    }
    // Rows
    if (!hasAvatar && hasTitle) {
        basicProps.rows = 3;
    } else {
        basicProps.rows = 2;
    }
    return basicProps;
}
const Skeleton = (props)=>{
    const { prefixCls: customizePrefixCls, loading, className, rootClassName, style, children, avatar = false, title = true, paragraph = true, active, round } = props;
    const { getPrefixCls, direction, className: contextClassName, style: contextStyle } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useComponentConfig"])('skeleton');
    const prefixCls = getPrefixCls('skeleton', customizePrefixCls);
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls);
    if (loading || !('loading' in props)) {
        const hasAvatar = !!avatar;
        const hasTitle = !!title;
        const hasParagraph = !!paragraph;
        // Avatar
        let avatarNode;
        if (hasAvatar) {
            const avatarProps = Object.assign(Object.assign({
                prefixCls: `${prefixCls}-avatar`
            }, getAvatarBasicProps(hasTitle, hasParagraph)), getComponentProps(avatar));
            // We direct use SkeletonElement as avatar in skeleton internal.
            avatarNode = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
                className: `${prefixCls}-header`
            }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], Object.assign({}, avatarProps)));
        }
        let contentNode;
        if (hasTitle || hasParagraph) {
            // Title
            let $title;
            if (hasTitle) {
                const titleProps = Object.assign(Object.assign({
                    prefixCls: `${prefixCls}-title`
                }, getTitleBasicProps(hasAvatar, hasParagraph)), getComponentProps(title));
                $title = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Title$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], Object.assign({}, titleProps));
            }
            // Paragraph
            let paragraphNode;
            if (hasParagraph) {
                const paragraphProps = Object.assign(Object.assign({
                    prefixCls: `${prefixCls}-paragraph`
                }, getParagraphBasicProps(hasAvatar, hasTitle)), getComponentProps(paragraph));
                paragraphNode = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Paragraph$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], Object.assign({}, paragraphProps));
            }
            contentNode = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
                className: `${prefixCls}-content`
            }, $title, paragraphNode);
        }
        const cls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, {
            [`${prefixCls}-with-avatar`]: hasAvatar,
            [`${prefixCls}-active`]: active,
            [`${prefixCls}-rtl`]: direction === 'rtl',
            [`${prefixCls}-round`]: round
        }, contextClassName, className, rootClassName, hashId, cssVarCls);
        return wrapCSSVar(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
            className: cls,
            style: Object.assign(Object.assign({}, contextStyle), style)
        }, avatarNode, contentNode));
    }
    return children !== null && children !== void 0 ? children : null;
};
Skeleton.Button = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Button$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
Skeleton.Avatar = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Avatar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
Skeleton.Input = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Input$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
Skeleton.Image = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
Skeleton.Node = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Node$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
if ("TURBOPACK compile-time truthy", 1) {
    Skeleton.displayName = 'Skeleton';
}
const __TURBOPACK__default__export__ = Skeleton;
}}),
"[project]/node_modules/antd/es/skeleton/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/skeleton/Skeleton.js [app-client] (ecmascript)");
"use client";
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$skeleton$2f$Skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
}}),
"[project]/node_modules/antd/es/watermark/context.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "usePanelRef": (()=>usePanelRef)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useEvent.js [app-client] (ecmascript)");
;
;
function voidFunc() {}
const WatermarkContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])({
    add: voidFunc,
    remove: voidFunc
});
function usePanelRef(panelSelector) {
    const watermark = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(WatermarkContext);
    const panelEleRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const panelRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        "usePanelRef.useEvent[panelRef]": (ele)=>{
            if (ele) {
                const innerContentEle = panelSelector ? ele.querySelector(panelSelector) : ele;
                watermark.add(innerContentEle);
                panelEleRef.current = innerContentEle;
            } else {
                watermark.remove(panelEleRef.current);
            }
        }
    }["usePanelRef.useEvent[panelRef]"]);
    return panelRef;
}
const __TURBOPACK__default__export__ = WatermarkContext;
}}),
"[project]/node_modules/antd/es/style/motion/fade.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "fadeIn": (()=>fadeIn),
    "fadeOut": (()=>fadeOut),
    "initFadeMotion": (()=>initFadeMotion)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$Keyframes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Keyframes$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/Keyframes.js [app-client] (ecmascript) <export default as Keyframes>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$motion$2f$motion$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/style/motion/motion.js [app-client] (ecmascript)");
;
;
const fadeIn = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$Keyframes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Keyframes$3e$__["Keyframes"]('antFadeIn', {
    '0%': {
        opacity: 0
    },
    '100%': {
        opacity: 1
    }
});
const fadeOut = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$Keyframes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Keyframes$3e$__["Keyframes"]('antFadeOut', {
    '0%': {
        opacity: 1
    },
    '100%': {
        opacity: 0
    }
});
const initFadeMotion = function(token) {
    let sameLevel = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
    const { antCls } = token;
    const motionCls = `${antCls}-fade`;
    const sameLevelPrefix = sameLevel ? '&' : '';
    return [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$motion$2f$motion$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initMotion"])(motionCls, fadeIn, fadeOut, token.motionDurationMid, sameLevel),
        {
            [`
        ${sameLevelPrefix}${motionCls}-enter,
        ${sameLevelPrefix}${motionCls}-appear
      `]: {
                opacity: 0,
                animationTimingFunction: 'linear'
            },
            [`${sameLevelPrefix}${motionCls}-leave`]: {
                animationTimingFunction: 'linear'
            }
        }
    ];
};
}}),
"[project]/node_modules/antd/es/popconfirm/style/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "prepareComponentToken": (()=>prepareComponentToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/util/genStyleUtils.js [app-client] (ecmascript)");
;
// =============================== Base ===============================
const genBaseStyle = (token)=>{
    const { componentCls, iconCls, antCls, zIndexPopup, colorText, colorWarning, marginXXS, marginXS, fontSize, fontWeightStrong, colorTextHeading } = token;
    return {
        [componentCls]: {
            zIndex: zIndexPopup,
            [`&${antCls}-popover`]: {
                fontSize
            },
            [`${componentCls}-message`]: {
                marginBottom: marginXS,
                display: 'flex',
                flexWrap: 'nowrap',
                alignItems: 'start',
                [`> ${componentCls}-message-icon ${iconCls}`]: {
                    color: colorWarning,
                    fontSize,
                    lineHeight: 1,
                    marginInlineEnd: marginXS
                },
                [`${componentCls}-title`]: {
                    fontWeight: fontWeightStrong,
                    color: colorTextHeading,
                    '&:only-child': {
                        fontWeight: 'normal'
                    }
                },
                [`${componentCls}-description`]: {
                    marginTop: marginXXS,
                    color: colorText
                }
            },
            [`${componentCls}-buttons`]: {
                textAlign: 'end',
                whiteSpace: 'nowrap',
                button: {
                    marginInlineStart: marginXS
                }
            }
        }
    };
};
const prepareComponentToken = (token)=>{
    const { zIndexPopupBase } = token;
    return {
        zIndexPopup: zIndexPopupBase + 60
    };
};
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genStyleHooks"])('Popconfirm', (token)=>genBaseStyle(token), prepareComponentToken, {
    resetStyle: false
});
}}),
"[project]/node_modules/antd/es/popconfirm/PurePanel.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Overlay": (()=>Overlay),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$ExclamationCircleFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/ExclamationCircleFilled.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$ActionButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/ActionButton.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$getRenderPropValue$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/getRenderPropValue.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/button/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$buttonHelpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/button/buttonHelpers.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$locale$2f$useLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useLocale$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/locale/useLocale.js [app-client] (ecmascript) <export default as useLocale>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$locale$2f$en_US$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/locale/en_US.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$popover$2f$PurePanel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/popover/PurePanel.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$popconfirm$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/popconfirm/style/index.js [app-client] (ecmascript)");
"use client";
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
;
;
;
;
;
;
;
;
;
;
;
;
const Overlay = (props)=>{
    const { prefixCls, okButtonProps, cancelButtonProps, title, description, cancelText, okText, okType = 'primary', icon = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$ExclamationCircleFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null), showCancel = true, close, onConfirm, onCancel, onPopupClick } = props;
    const { getPrefixCls } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfigContext"]);
    const [contextLocale] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$locale$2f$useLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useLocale$3e$__["useLocale"])('Popconfirm', __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$locale$2f$en_US$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Popconfirm);
    const titleNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$getRenderPropValue$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getRenderPropValue"])(title);
    const descriptionNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$getRenderPropValue$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getRenderPropValue"])(description);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: `${prefixCls}-inner-content`,
        onClick: onPopupClick
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: `${prefixCls}-message`
    }, icon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("span", {
        className: `${prefixCls}-message-icon`
    }, icon), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: `${prefixCls}-message-text`
    }, titleNode && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: `${prefixCls}-title`
    }, titleNode), descriptionNode && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: `${prefixCls}-description`
    }, descriptionNode))), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: `${prefixCls}-buttons`
    }, showCancel && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"], Object.assign({
        onClick: onCancel,
        size: "small"
    }, cancelButtonProps), cancelText || (contextLocale === null || contextLocale === void 0 ? void 0 : contextLocale.cancelText)), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$ActionButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        buttonProps: Object.assign(Object.assign({
            size: 'small'
        }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$button$2f$buttonHelpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertLegacyProps"])(okType)), okButtonProps),
        actionFn: onConfirm,
        close: close,
        prefixCls: getPrefixCls('btn'),
        quitOnNullishReturnValue: true,
        emitEvent: true
    }, okText || (contextLocale === null || contextLocale === void 0 ? void 0 : contextLocale.okText))));
};
const PurePanel = (props)=>{
    const { prefixCls: customizePrefixCls, placement, className, style } = props, restProps = __rest(props, [
        "prefixCls",
        "placement",
        "className",
        "style"
    ]);
    const { getPrefixCls } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfigContext"]);
    const prefixCls = getPrefixCls('popconfirm', customizePrefixCls);
    const [wrapCSSVar] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$popconfirm$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls);
    return wrapCSSVar(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$popover$2f$PurePanel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        placement: placement,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, className),
        style: style,
        content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(Overlay, Object.assign({
            prefixCls: prefixCls
        }, restProps))
    }));
};
const __TURBOPACK__default__export__ = PurePanel;
}}),
"[project]/node_modules/antd/es/popconfirm/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$ExclamationCircleFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/ExclamationCircleFilled.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useMergedState$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useMergedState.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$omit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/omit.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$popover$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/popover/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$popconfirm$2f$PurePanel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/popconfirm/PurePanel.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$popconfirm$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/popconfirm/style/index.js [app-client] (ecmascript)");
"use client";
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
;
;
;
;
;
;
;
;
;
const InternalPopconfirm = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((props, ref)=>{
    var _a, _b;
    const { prefixCls: customizePrefixCls, placement = 'top', trigger = 'click', okType = 'primary', icon = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$ExclamationCircleFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null), children, overlayClassName, onOpenChange, onVisibleChange, overlayStyle, styles, classNames: popconfirmClassNames } = props, restProps = __rest(props, [
        "prefixCls",
        "placement",
        "trigger",
        "okType",
        "icon",
        "children",
        "overlayClassName",
        "onOpenChange",
        "onVisibleChange",
        "overlayStyle",
        "styles",
        "classNames"
    ]);
    const { getPrefixCls, className: contextClassName, style: contextStyle, classNames: contextClassNames, styles: contextStyles } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useComponentConfig"])('popconfirm');
    const [open, setOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useMergedState$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, {
        value: (_a = props.open) !== null && _a !== void 0 ? _a : props.visible,
        defaultValue: (_b = props.defaultOpen) !== null && _b !== void 0 ? _b : props.defaultVisible
    });
    const settingOpen = (value, e)=>{
        setOpen(value, true);
        onVisibleChange === null || onVisibleChange === void 0 ? void 0 : onVisibleChange(value);
        onOpenChange === null || onOpenChange === void 0 ? void 0 : onOpenChange(value, e);
    };
    const close = (e)=>{
        settingOpen(false, e);
    };
    const onConfirm = (e)=>{
        var _a;
        return (_a = props.onConfirm) === null || _a === void 0 ? void 0 : _a.call(this, e);
    };
    const onCancel = (e)=>{
        var _a;
        settingOpen(false, e);
        (_a = props.onCancel) === null || _a === void 0 ? void 0 : _a.call(this, e);
    };
    const onInternalOpenChange = (value, e)=>{
        const { disabled = false } = props;
        if (disabled) {
            return;
        }
        settingOpen(value, e);
    };
    const prefixCls = getPrefixCls('popconfirm', customizePrefixCls);
    const rootClassNames = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, contextClassName, overlayClassName, contextClassNames.root, popconfirmClassNames === null || popconfirmClassNames === void 0 ? void 0 : popconfirmClassNames.root);
    const bodyClassNames = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(contextClassNames.body, popconfirmClassNames === null || popconfirmClassNames === void 0 ? void 0 : popconfirmClassNames.body);
    const [wrapCSSVar] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$popconfirm$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls);
    return wrapCSSVar(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$popover$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$omit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(restProps, [
        'title'
    ]), {
        trigger: trigger,
        placement: placement,
        onOpenChange: onInternalOpenChange,
        open: open,
        ref: ref,
        classNames: {
            root: rootClassNames,
            body: bodyClassNames
        },
        styles: {
            root: Object.assign(Object.assign(Object.assign(Object.assign({}, contextStyles.root), contextStyle), overlayStyle), styles === null || styles === void 0 ? void 0 : styles.root),
            body: Object.assign(Object.assign({}, contextStyles.body), styles === null || styles === void 0 ? void 0 : styles.body)
        },
        content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$popconfirm$2f$PurePanel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Overlay"], Object.assign({
            okType: okType,
            icon: icon
        }, props, {
            prefixCls: prefixCls,
            close: close,
            onConfirm: onConfirm,
            onCancel: onCancel
        })),
        "data-popover-inject": true
    }), children));
});
const Popconfirm = InternalPopconfirm;
// We don't care debug panel
/* istanbul ignore next */ Popconfirm._InternalPanelDoNotUseOrYouWillBeFired = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$popconfirm$2f$PurePanel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
if ("TURBOPACK compile-time truthy", 1) {
    Popconfirm.displayName = 'Popconfirm';
}
const __TURBOPACK__default__export__ = Popconfirm;
}}),
"[project]/node_modules/antd/es/popconfirm/index.js [app-client] (ecmascript) <export default as Popconfirm>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Popconfirm": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$popconfirm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$popconfirm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/popconfirm/index.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/antd/es/checkbox/GroupContext.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
const GroupContext = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createContext(null);
const __TURBOPACK__default__export__ = GroupContext;
}}),
"[project]/node_modules/antd/es/checkbox/style/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "genCheckboxStyle": (()=>genCheckboxStyle),
    "getStyle": (()=>getStyle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/util/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/util/genStyleUtils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs-utils/es/util/statistic.js [app-client] (ecmascript) <export merge as mergeToken>");
;
;
;
const genCheckboxStyle = (token)=>{
    const { checkboxCls } = token;
    const wrapperCls = `${checkboxCls}-wrapper`;
    return [
        // ===================== Basic =====================
        {
            // Group
            [`${checkboxCls}-group`]: Object.assign(Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resetComponent"])(token)), {
                display: 'inline-flex',
                flexWrap: 'wrap',
                columnGap: token.marginXS,
                // Group > Grid
                [`> ${token.antCls}-row`]: {
                    flex: 1
                }
            }),
            // Wrapper
            [wrapperCls]: Object.assign(Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resetComponent"])(token)), {
                display: 'inline-flex',
                alignItems: 'baseline',
                cursor: 'pointer',
                // Fix checkbox & radio in flex align #30260
                '&:after': {
                    display: 'inline-block',
                    width: 0,
                    overflow: 'hidden',
                    content: "'\\a0'"
                },
                // Checkbox near checkbox
                [`& + ${wrapperCls}`]: {
                    marginInlineStart: 0
                },
                [`&${wrapperCls}-in-form-item`]: {
                    'input[type="checkbox"]': {
                        width: 14,
                        // FIXME: magic
                        height: 14 // FIXME: magic
                    }
                }
            }),
            // Wrapper > Checkbox
            [checkboxCls]: Object.assign(Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resetComponent"])(token)), {
                position: 'relative',
                whiteSpace: 'nowrap',
                lineHeight: 1,
                cursor: 'pointer',
                borderRadius: token.borderRadiusSM,
                // To make alignment right when `controlHeight` is changed
                // Ref: https://github.com/ant-design/ant-design/issues/41564
                alignSelf: 'center',
                // Wrapper > Checkbox > input
                [`${checkboxCls}-input`]: {
                    position: 'absolute',
                    // Since baseline align will get additional space offset,
                    // we need to move input to top to make it align with text.
                    // Ref: https://github.com/ant-design/ant-design/issues/38926#issuecomment-1486137799
                    inset: 0,
                    zIndex: 1,
                    cursor: 'pointer',
                    opacity: 0,
                    margin: 0,
                    [`&:focus-visible + ${checkboxCls}-inner`]: Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genFocusOutline"])(token))
                },
                // Wrapper > Checkbox > inner
                [`${checkboxCls}-inner`]: {
                    boxSizing: 'border-box',
                    display: 'block',
                    width: token.checkboxSize,
                    height: token.checkboxSize,
                    direction: 'ltr',
                    backgroundColor: token.colorBgContainer,
                    border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.lineWidth)} ${token.lineType} ${token.colorBorder}`,
                    borderRadius: token.borderRadiusSM,
                    borderCollapse: 'separate',
                    transition: `all ${token.motionDurationSlow}`,
                    '&:after': {
                        boxSizing: 'border-box',
                        position: 'absolute',
                        top: '50%',
                        insetInlineStart: '25%',
                        display: 'table',
                        width: token.calc(token.checkboxSize).div(14).mul(5).equal(),
                        height: token.calc(token.checkboxSize).div(14).mul(8).equal(),
                        border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.lineWidthBold)} solid ${token.colorWhite}`,
                        borderTop: 0,
                        borderInlineStart: 0,
                        transform: 'rotate(45deg) scale(0) translate(-50%,-50%)',
                        opacity: 0,
                        content: '""',
                        transition: `all ${token.motionDurationFast} ${token.motionEaseInBack}, opacity ${token.motionDurationFast}`
                    }
                },
                // Wrapper > Checkbox + Text
                '& + span': {
                    paddingInlineStart: token.paddingXS,
                    paddingInlineEnd: token.paddingXS
                }
            })
        },
        // ===================== Hover =====================
        {
            // Wrapper & Wrapper > Checkbox
            [`
        ${wrapperCls}:not(${wrapperCls}-disabled),
        ${checkboxCls}:not(${checkboxCls}-disabled)
      `]: {
                [`&:hover ${checkboxCls}-inner`]: {
                    borderColor: token.colorPrimary
                }
            },
            [`${wrapperCls}:not(${wrapperCls}-disabled)`]: {
                [`&:hover ${checkboxCls}-checked:not(${checkboxCls}-disabled) ${checkboxCls}-inner`]: {
                    backgroundColor: token.colorPrimaryHover,
                    borderColor: 'transparent'
                },
                [`&:hover ${checkboxCls}-checked:not(${checkboxCls}-disabled):after`]: {
                    borderColor: token.colorPrimaryHover
                }
            }
        },
        // ==================== Checked ====================
        {
            // Wrapper > Checkbox
            [`${checkboxCls}-checked`]: {
                [`${checkboxCls}-inner`]: {
                    backgroundColor: token.colorPrimary,
                    borderColor: token.colorPrimary,
                    '&:after': {
                        opacity: 1,
                        transform: 'rotate(45deg) scale(1) translate(-50%,-50%)',
                        transition: `all ${token.motionDurationMid} ${token.motionEaseOutBack} ${token.motionDurationFast}`
                    }
                }
            },
            [`
        ${wrapperCls}-checked:not(${wrapperCls}-disabled),
        ${checkboxCls}-checked:not(${checkboxCls}-disabled)
      `]: {
                [`&:hover ${checkboxCls}-inner`]: {
                    backgroundColor: token.colorPrimaryHover,
                    borderColor: 'transparent'
                }
            }
        },
        // ================= Indeterminate =================
        {
            [checkboxCls]: {
                '&-indeterminate': {
                    // Wrapper > Checkbox > inner
                    [`${checkboxCls}-inner`]: {
                        backgroundColor: `${token.colorBgContainer} !important`,
                        borderColor: `${token.colorBorder} !important`,
                        '&:after': {
                            top: '50%',
                            insetInlineStart: '50%',
                            width: token.calc(token.fontSizeLG).div(2).equal(),
                            height: token.calc(token.fontSizeLG).div(2).equal(),
                            backgroundColor: token.colorPrimary,
                            border: 0,
                            transform: 'translate(-50%, -50%) scale(1)',
                            opacity: 1,
                            content: '""'
                        }
                    },
                    // https://github.com/ant-design/ant-design/issues/50074
                    [`&:hover ${checkboxCls}-inner`]: {
                        backgroundColor: `${token.colorBgContainer} !important`,
                        borderColor: `${token.colorPrimary} !important`
                    }
                }
            }
        },
        // ==================== Disable ====================
        {
            // Wrapper
            [`${wrapperCls}-disabled`]: {
                cursor: 'not-allowed'
            },
            // Wrapper > Checkbox
            [`${checkboxCls}-disabled`]: {
                // Wrapper > Checkbox > input
                [`&, ${checkboxCls}-input`]: {
                    cursor: 'not-allowed',
                    // Disabled for native input to enable Tooltip event handler
                    // ref: https://github.com/ant-design/ant-design/issues/39822#issuecomment-1365075901
                    pointerEvents: 'none'
                },
                // Wrapper > Checkbox > inner
                [`${checkboxCls}-inner`]: {
                    background: token.colorBgContainerDisabled,
                    borderColor: token.colorBorder,
                    '&:after': {
                        borderColor: token.colorTextDisabled
                    }
                },
                '&:after': {
                    display: 'none'
                },
                '& + span': {
                    color: token.colorTextDisabled
                },
                [`&${checkboxCls}-indeterminate ${checkboxCls}-inner::after`]: {
                    background: token.colorTextDisabled
                }
            }
        }
    ];
};
function getStyle(prefixCls, token) {
    const checkboxToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__["mergeToken"])(token, {
        checkboxCls: `.${prefixCls}`,
        checkboxSize: token.controlInteractiveSize
    });
    return [
        genCheckboxStyle(checkboxToken)
    ];
}
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genStyleHooks"])('Checkbox', (token, _ref)=>{
    let { prefixCls } = _ref;
    return [
        getStyle(prefixCls, token)
    ];
});
}}),
"[project]/node_modules/antd/es/checkbox/useBubbleLock.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useBubbleLock)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$raf$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/raf.js [app-client] (ecmascript)");
;
;
function useBubbleLock(onOriginInputClick) {
    const labelClickLockRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useRef(null);
    const clearLock = ()=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$raf$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].cancel(labelClickLockRef.current);
        labelClickLockRef.current = null;
    };
    const onLabelClick = ()=>{
        clearLock();
        labelClickLockRef.current = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$raf$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>{
            labelClickLockRef.current = null;
        });
    };
    const onInputClick = (e)=>{
        if (labelClickLockRef.current) {
            e.stopPropagation();
            clearLock();
        }
        onOriginInputClick === null || onOriginInputClick === void 0 ? void 0 : onOriginInputClick(e);
    };
    return [
        onLabelClick,
        onInputClick
    ];
}
}}),
"[project]/node_modules/antd/es/checkbox/Checkbox.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$checkbox$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-checkbox/es/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/ref.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$wave$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/wave/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$wave$2f$interface$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/wave/interface.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$DisabledContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/DisabledContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useCSSVarCls$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/hooks/useCSSVarCls.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$checkbox$2f$GroupContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/checkbox/GroupContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$checkbox$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/checkbox/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$checkbox$2f$useBubbleLock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/checkbox/useBubbleLock.js [app-client] (ecmascript)");
"use client";
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const InternalCheckbox = (props, ref)=>{
    var _a;
    const { prefixCls: customizePrefixCls, className, rootClassName, children, indeterminate = false, style, onMouseEnter, onMouseLeave, skipGroup = false, disabled } = props, restProps = __rest(props, [
        "prefixCls",
        "className",
        "rootClassName",
        "children",
        "indeterminate",
        "style",
        "onMouseEnter",
        "onMouseLeave",
        "skipGroup",
        "disabled"
    ]);
    const { getPrefixCls, direction, checkbox } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfigContext"]);
    const checkboxGroup = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$checkbox$2f$GroupContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
    const { isFormItemInput } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormItemInputContext"]);
    const contextDisabled = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$DisabledContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
    const mergedDisabled = (_a = (checkboxGroup === null || checkboxGroup === void 0 ? void 0 : checkboxGroup.disabled) || disabled) !== null && _a !== void 0 ? _a : contextDisabled;
    const prevValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(restProps.value);
    const checkboxRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const mergedRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["composeRef"])(ref, checkboxRef);
    if ("TURBOPACK compile-time truthy", 1) {
        const warning = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["devUseWarning"])('Checkbox');
        ("TURBOPACK compile-time truthy", 1) ? warning('checked' in restProps || !!checkboxGroup || !('value' in restProps), 'usage', '`value` is not a valid prop, do you mean `checked`?') : ("TURBOPACK unreachable", undefined);
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InternalCheckbox.useEffect": ()=>{
            checkboxGroup === null || checkboxGroup === void 0 ? void 0 : checkboxGroup.registerValue(restProps.value);
        }
    }["InternalCheckbox.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InternalCheckbox.useEffect": ()=>{
            if (skipGroup) {
                return;
            }
            if (restProps.value !== prevValue.current) {
                checkboxGroup === null || checkboxGroup === void 0 ? void 0 : checkboxGroup.cancelValue(prevValue.current);
                checkboxGroup === null || checkboxGroup === void 0 ? void 0 : checkboxGroup.registerValue(restProps.value);
                prevValue.current = restProps.value;
            }
            return ({
                "InternalCheckbox.useEffect": ()=>checkboxGroup === null || checkboxGroup === void 0 ? void 0 : checkboxGroup.cancelValue(restProps.value)
            })["InternalCheckbox.useEffect"];
        }
    }["InternalCheckbox.useEffect"], [
        restProps.value
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InternalCheckbox.useEffect": ()=>{
            var _a;
            if ((_a = checkboxRef.current) === null || _a === void 0 ? void 0 : _a.input) {
                checkboxRef.current.input.indeterminate = indeterminate;
            }
        }
    }["InternalCheckbox.useEffect"], [
        indeterminate
    ]);
    const prefixCls = getPrefixCls('checkbox', customizePrefixCls);
    const rootCls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useCSSVarCls$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls);
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$checkbox$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, rootCls);
    const checkboxProps = Object.assign({}, restProps);
    if (checkboxGroup && !skipGroup) {
        checkboxProps.onChange = function() {
            if (restProps.onChange) {
                restProps.onChange.apply(restProps, arguments);
            }
            if (checkboxGroup.toggleOption) {
                checkboxGroup.toggleOption({
                    label: children,
                    value: restProps.value
                });
            }
        };
        checkboxProps.name = checkboxGroup.name;
        checkboxProps.checked = checkboxGroup.value.includes(restProps.value);
    }
    const classString = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(`${prefixCls}-wrapper`, {
        [`${prefixCls}-rtl`]: direction === 'rtl',
        [`${prefixCls}-wrapper-checked`]: checkboxProps.checked,
        [`${prefixCls}-wrapper-disabled`]: mergedDisabled,
        [`${prefixCls}-wrapper-in-form-item`]: isFormItemInput
    }, checkbox === null || checkbox === void 0 ? void 0 : checkbox.className, className, rootClassName, cssVarCls, rootCls, hashId);
    const checkboxClass = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        [`${prefixCls}-indeterminate`]: indeterminate
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$wave$2f$interface$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TARGET_CLS"], hashId);
    // ============================ Event Lock ============================
    const [onLabelClick, onInputClick] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$checkbox$2f$useBubbleLock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(checkboxProps.onClick);
    // ============================== Render ==============================
    return wrapCSSVar(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$wave$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        component: "Checkbox",
        disabled: mergedDisabled
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("label", {
        className: classString,
        style: Object.assign(Object.assign({}, checkbox === null || checkbox === void 0 ? void 0 : checkbox.style), style),
        onMouseEnter: onMouseEnter,
        onMouseLeave: onMouseLeave,
        onClick: onLabelClick
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$checkbox$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], Object.assign({}, checkboxProps, {
        onClick: onInputClick,
        prefixCls: prefixCls,
        className: checkboxClass,
        disabled: mergedDisabled,
        ref: mergedRef
    })), children !== undefined && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("span", {
        className: `${prefixCls}-label`
    }, children))));
};
const Checkbox = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(InternalCheckbox);
if ("TURBOPACK compile-time truthy", 1) {
    Checkbox.displayName = 'Checkbox';
}
const __TURBOPACK__default__export__ = Checkbox;
}}),
"[project]/node_modules/antd/es/checkbox/Group.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$omit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/omit.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useCSSVarCls$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/hooks/useCSSVarCls.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$checkbox$2f$Checkbox$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/checkbox/Checkbox.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$checkbox$2f$GroupContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/checkbox/GroupContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$checkbox$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/checkbox/style/index.js [app-client] (ecmascript)");
"use client";
;
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
;
;
;
;
;
;
;
;
const CheckboxGroup = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((props, ref)=>{
    const { defaultValue, children, options = [], prefixCls: customizePrefixCls, className, rootClassName, style, onChange } = props, restProps = __rest(props, [
        "defaultValue",
        "children",
        "options",
        "prefixCls",
        "className",
        "rootClassName",
        "style",
        "onChange"
    ]);
    const { getPrefixCls, direction } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfigContext"]);
    const [value, setValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(restProps.value || defaultValue || []);
    const [registeredValues, setRegisteredValues] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CheckboxGroup.useEffect": ()=>{
            if ('value' in restProps) {
                setValue(restProps.value || []);
            }
        }
    }["CheckboxGroup.useEffect"], [
        restProps.value
    ]);
    const memoizedOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "CheckboxGroup.useMemo[memoizedOptions]": ()=>options.map({
                "CheckboxGroup.useMemo[memoizedOptions]": (option)=>{
                    if (typeof option === 'string' || typeof option === 'number') {
                        return {
                            label: option,
                            value: option
                        };
                    }
                    return option;
                }
            }["CheckboxGroup.useMemo[memoizedOptions]"])
    }["CheckboxGroup.useMemo[memoizedOptions]"], [
        options
    ]);
    const cancelValue = (val)=>{
        setRegisteredValues((prevValues)=>prevValues.filter((v)=>v !== val));
    };
    const registerValue = (val)=>{
        setRegisteredValues((prevValues)=>[].concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prevValues), [
                val
            ]));
    };
    const toggleOption = (option)=>{
        const optionIndex = value.indexOf(option.value);
        const newValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(value);
        if (optionIndex === -1) {
            newValue.push(option.value);
        } else {
            newValue.splice(optionIndex, 1);
        }
        if (!('value' in restProps)) {
            setValue(newValue);
        }
        onChange === null || onChange === void 0 ? void 0 : onChange(newValue.filter((val)=>registeredValues.includes(val)).sort((a, b)=>{
            const indexA = memoizedOptions.findIndex((opt)=>opt.value === a);
            const indexB = memoizedOptions.findIndex((opt)=>opt.value === b);
            return indexA - indexB;
        }));
    };
    const prefixCls = getPrefixCls('checkbox', customizePrefixCls);
    const groupPrefixCls = `${prefixCls}-group`;
    const rootCls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useCSSVarCls$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls);
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$checkbox$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, rootCls);
    const domProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$omit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(restProps, [
        'value',
        'disabled'
    ]);
    const childrenNode = options.length ? memoizedOptions.map((option)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$checkbox$2f$Checkbox$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            prefixCls: prefixCls,
            key: option.value.toString(),
            disabled: 'disabled' in option ? option.disabled : restProps.disabled,
            value: option.value,
            checked: value.includes(option.value),
            onChange: option.onChange,
            className: `${groupPrefixCls}-item`,
            style: option.style,
            title: option.title,
            id: option.id,
            required: option.required
        }, option.label)) : children;
    const context = {
        toggleOption,
        value,
        disabled: restProps.disabled,
        name: restProps.name,
        // https://github.com/ant-design/ant-design/issues/16376
        registerValue,
        cancelValue
    };
    const classString = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(groupPrefixCls, {
        [`${groupPrefixCls}-rtl`]: direction === 'rtl'
    }, className, rootClassName, cssVarCls, rootCls, hashId);
    return wrapCSSVar(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", Object.assign({
        className: classString,
        style: style
    }, domProps, {
        ref: ref
    }), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$checkbox$2f$GroupContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Provider, {
        value: context
    }, childrenNode)));
});
;
const __TURBOPACK__default__export__ = CheckboxGroup;
}}),
"[project]/node_modules/antd/es/checkbox/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$checkbox$2f$Checkbox$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/checkbox/Checkbox.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$checkbox$2f$Group$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/checkbox/Group.js [app-client] (ecmascript) <locals>");
"use client";
;
;
const Checkbox = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$checkbox$2f$Checkbox$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
Checkbox.Group = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$checkbox$2f$Group$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"];
Checkbox.__ANT_CHECKBOX = true;
if ("TURBOPACK compile-time truthy", 1) {
    Checkbox.displayName = 'Checkbox';
}
const __TURBOPACK__default__export__ = Checkbox;
}}),
"[project]/node_modules/antd/es/radio/context.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "RadioGroupContextProvider": (()=>RadioGroupContextProvider),
    "RadioOptionTypeContext": (()=>RadioOptionTypeContext),
    "RadioOptionTypeContextProvider": (()=>RadioOptionTypeContextProvider),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
const RadioGroupContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
const RadioGroupContextProvider = RadioGroupContext.Provider;
const __TURBOPACK__default__export__ = RadioGroupContext;
const RadioOptionTypeContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
const RadioOptionTypeContextProvider = RadioOptionTypeContext.Provider;
}}),
"[project]/node_modules/antd/es/radio/style/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "prepareComponentToken": (()=>prepareComponentToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/util/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/util/genStyleUtils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs-utils/es/util/statistic.js [app-client] (ecmascript) <export merge as mergeToken>");
;
;
;
// ============================== Styles ==============================
// styles from RadioGroup only
const getGroupRadioStyle = (token)=>{
    const { componentCls, antCls } = token;
    const groupPrefixCls = `${componentCls}-group`;
    return {
        [groupPrefixCls]: Object.assign(Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resetComponent"])(token)), {
            display: 'inline-block',
            fontSize: 0,
            // RTL
            [`&${groupPrefixCls}-rtl`]: {
                direction: 'rtl'
            },
            [`&${groupPrefixCls}-block`]: {
                display: 'flex'
            },
            [`${antCls}-badge ${antCls}-badge-count`]: {
                zIndex: 1
            },
            [`> ${antCls}-badge:not(:first-child) > ${antCls}-button-wrapper`]: {
                borderInlineStart: 'none'
            }
        })
    };
};
// Styles from radio-wrapper
const getRadioBasicStyle = (token)=>{
    const { componentCls, wrapperMarginInlineEnd, colorPrimary, radioSize, motionDurationSlow, motionDurationMid, motionEaseInOutCirc, colorBgContainer, colorBorder, lineWidth, colorBgContainerDisabled, colorTextDisabled, paddingXS, dotColorDisabled, lineType, radioColor, radioBgColor, calc } = token;
    const radioInnerPrefixCls = `${componentCls}-inner`;
    const dotPadding = 4;
    const radioDotDisabledSize = calc(radioSize).sub(calc(dotPadding).mul(2));
    const radioSizeCalc = calc(1).mul(radioSize).equal({
        unit: true
    });
    return {
        [`${componentCls}-wrapper`]: Object.assign(Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resetComponent"])(token)), {
            display: 'inline-flex',
            alignItems: 'baseline',
            marginInlineStart: 0,
            marginInlineEnd: wrapperMarginInlineEnd,
            cursor: 'pointer',
            '&:last-child': {
                marginInlineEnd: 0
            },
            // RTL
            [`&${componentCls}-wrapper-rtl`]: {
                direction: 'rtl'
            },
            '&-disabled': {
                cursor: 'not-allowed',
                color: token.colorTextDisabled
            },
            '&::after': {
                display: 'inline-block',
                width: 0,
                overflow: 'hidden',
                content: '"\\a0"'
            },
            '&-block': {
                flex: 1,
                justifyContent: 'center'
            },
            // hashId 在 wrapper 上，只能铺平
            [`${componentCls}-checked::after`]: {
                position: 'absolute',
                insetBlockStart: 0,
                insetInlineStart: 0,
                width: '100%',
                height: '100%',
                border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(lineWidth)} ${lineType} ${colorPrimary}`,
                borderRadius: '50%',
                visibility: 'hidden',
                opacity: 0,
                content: '""'
            },
            [componentCls]: Object.assign(Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resetComponent"])(token)), {
                position: 'relative',
                display: 'inline-block',
                outline: 'none',
                cursor: 'pointer',
                alignSelf: 'center',
                borderRadius: '50%'
            }),
            [`${componentCls}-wrapper:hover &,
        &:hover ${radioInnerPrefixCls}`]: {
                borderColor: colorPrimary
            },
            [`${componentCls}-input:focus-visible + ${radioInnerPrefixCls}`]: Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genFocusOutline"])(token)),
            [`${componentCls}:hover::after, ${componentCls}-wrapper:hover &::after`]: {
                visibility: 'visible'
            },
            [`${componentCls}-inner`]: {
                '&::after': {
                    boxSizing: 'border-box',
                    position: 'absolute',
                    insetBlockStart: '50%',
                    insetInlineStart: '50%',
                    display: 'block',
                    width: radioSizeCalc,
                    height: radioSizeCalc,
                    marginBlockStart: calc(1).mul(radioSize).div(-2).equal({
                        unit: true
                    }),
                    marginInlineStart: calc(1).mul(radioSize).div(-2).equal({
                        unit: true
                    }),
                    backgroundColor: radioColor,
                    borderBlockStart: 0,
                    borderInlineStart: 0,
                    borderRadius: radioSizeCalc,
                    transform: 'scale(0)',
                    opacity: 0,
                    transition: `all ${motionDurationSlow} ${motionEaseInOutCirc}`,
                    content: '""'
                },
                boxSizing: 'border-box',
                position: 'relative',
                insetBlockStart: 0,
                insetInlineStart: 0,
                display: 'block',
                width: radioSizeCalc,
                height: radioSizeCalc,
                backgroundColor: colorBgContainer,
                borderColor: colorBorder,
                borderStyle: 'solid',
                borderWidth: lineWidth,
                borderRadius: '50%',
                transition: `all ${motionDurationMid}`
            },
            [`${componentCls}-input`]: {
                position: 'absolute',
                inset: 0,
                zIndex: 1,
                cursor: 'pointer',
                opacity: 0
            },
            // 选中状态
            [`${componentCls}-checked`]: {
                [radioInnerPrefixCls]: {
                    borderColor: colorPrimary,
                    backgroundColor: radioBgColor,
                    '&::after': {
                        transform: `scale(${token.calc(token.dotSize).div(radioSize).equal()})`,
                        opacity: 1,
                        transition: `all ${motionDurationSlow} ${motionEaseInOutCirc}`
                    }
                }
            },
            [`${componentCls}-disabled`]: {
                cursor: 'not-allowed',
                [radioInnerPrefixCls]: {
                    backgroundColor: colorBgContainerDisabled,
                    borderColor: colorBorder,
                    cursor: 'not-allowed',
                    '&::after': {
                        backgroundColor: dotColorDisabled
                    }
                },
                [`${componentCls}-input`]: {
                    cursor: 'not-allowed'
                },
                [`${componentCls}-disabled + span`]: {
                    color: colorTextDisabled,
                    cursor: 'not-allowed'
                },
                [`&${componentCls}-checked`]: {
                    [radioInnerPrefixCls]: {
                        '&::after': {
                            transform: `scale(${calc(radioDotDisabledSize).div(radioSize).equal()})`
                        }
                    }
                }
            },
            [`span${componentCls} + *`]: {
                paddingInlineStart: paddingXS,
                paddingInlineEnd: paddingXS
            }
        })
    };
};
// Styles from radio-button
const getRadioButtonStyle = (token)=>{
    const { buttonColor, controlHeight, componentCls, lineWidth, lineType, colorBorder, motionDurationSlow, motionDurationMid, buttonPaddingInline, fontSize, buttonBg, fontSizeLG, controlHeightLG, controlHeightSM, paddingXS, borderRadius, borderRadiusSM, borderRadiusLG, buttonCheckedBg, buttonSolidCheckedColor, colorTextDisabled, colorBgContainerDisabled, buttonCheckedBgDisabled, buttonCheckedColorDisabled, colorPrimary, colorPrimaryHover, colorPrimaryActive, buttonSolidCheckedBg, buttonSolidCheckedHoverBg, buttonSolidCheckedActiveBg, calc } = token;
    return {
        [`${componentCls}-button-wrapper`]: {
            position: 'relative',
            display: 'inline-block',
            height: controlHeight,
            margin: 0,
            paddingInline: buttonPaddingInline,
            paddingBlock: 0,
            color: buttonColor,
            fontSize,
            lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(calc(controlHeight).sub(calc(lineWidth).mul(2)).equal()),
            background: buttonBg,
            border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(lineWidth)} ${lineType} ${colorBorder}`,
            // strange align fix for chrome but works
            // https://gw.alipayobjects.com/zos/rmsportal/VFTfKXJuogBAXcvfAUWJ.gif
            borderBlockStartWidth: calc(lineWidth).add(0.02).equal(),
            borderInlineStartWidth: 0,
            borderInlineEndWidth: lineWidth,
            cursor: 'pointer',
            transition: [
                `color ${motionDurationMid}`,
                `background ${motionDurationMid}`,
                `box-shadow ${motionDurationMid}`
            ].join(','),
            a: {
                color: buttonColor
            },
            [`> ${componentCls}-button`]: {
                position: 'absolute',
                insetBlockStart: 0,
                insetInlineStart: 0,
                zIndex: -1,
                width: '100%',
                height: '100%'
            },
            '&:not(:first-child)': {
                '&::before': {
                    position: 'absolute',
                    insetBlockStart: calc(lineWidth).mul(-1).equal(),
                    insetInlineStart: calc(lineWidth).mul(-1).equal(),
                    display: 'block',
                    boxSizing: 'content-box',
                    width: 1,
                    height: '100%',
                    paddingBlock: lineWidth,
                    paddingInline: 0,
                    backgroundColor: colorBorder,
                    transition: `background-color ${motionDurationSlow}`,
                    content: '""'
                }
            },
            '&:first-child': {
                borderInlineStart: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(lineWidth)} ${lineType} ${colorBorder}`,
                borderStartStartRadius: borderRadius,
                borderEndStartRadius: borderRadius
            },
            '&:last-child': {
                borderStartEndRadius: borderRadius,
                borderEndEndRadius: borderRadius
            },
            '&:first-child:last-child': {
                borderRadius
            },
            [`${componentCls}-group-large &`]: {
                height: controlHeightLG,
                fontSize: fontSizeLG,
                lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(calc(controlHeightLG).sub(calc(lineWidth).mul(2)).equal()),
                '&:first-child': {
                    borderStartStartRadius: borderRadiusLG,
                    borderEndStartRadius: borderRadiusLG
                },
                '&:last-child': {
                    borderStartEndRadius: borderRadiusLG,
                    borderEndEndRadius: borderRadiusLG
                }
            },
            [`${componentCls}-group-small &`]: {
                height: controlHeightSM,
                paddingInline: calc(paddingXS).sub(lineWidth).equal(),
                paddingBlock: 0,
                lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(calc(controlHeightSM).sub(calc(lineWidth).mul(2)).equal()),
                '&:first-child': {
                    borderStartStartRadius: borderRadiusSM,
                    borderEndStartRadius: borderRadiusSM
                },
                '&:last-child': {
                    borderStartEndRadius: borderRadiusSM,
                    borderEndEndRadius: borderRadiusSM
                }
            },
            '&:hover': {
                position: 'relative',
                color: colorPrimary
            },
            '&:has(:focus-visible)': Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genFocusOutline"])(token)),
            [`${componentCls}-inner, input[type='checkbox'], input[type='radio']`]: {
                width: 0,
                height: 0,
                opacity: 0,
                pointerEvents: 'none'
            },
            [`&-checked:not(${componentCls}-button-wrapper-disabled)`]: {
                zIndex: 1,
                color: colorPrimary,
                background: buttonCheckedBg,
                borderColor: colorPrimary,
                '&::before': {
                    backgroundColor: colorPrimary
                },
                '&:first-child': {
                    borderColor: colorPrimary
                },
                '&:hover': {
                    color: colorPrimaryHover,
                    borderColor: colorPrimaryHover,
                    '&::before': {
                        backgroundColor: colorPrimaryHover
                    }
                },
                '&:active': {
                    color: colorPrimaryActive,
                    borderColor: colorPrimaryActive,
                    '&::before': {
                        backgroundColor: colorPrimaryActive
                    }
                }
            },
            [`${componentCls}-group-solid &-checked:not(${componentCls}-button-wrapper-disabled)`]: {
                color: buttonSolidCheckedColor,
                background: buttonSolidCheckedBg,
                borderColor: buttonSolidCheckedBg,
                '&:hover': {
                    color: buttonSolidCheckedColor,
                    background: buttonSolidCheckedHoverBg,
                    borderColor: buttonSolidCheckedHoverBg
                },
                '&:active': {
                    color: buttonSolidCheckedColor,
                    background: buttonSolidCheckedActiveBg,
                    borderColor: buttonSolidCheckedActiveBg
                }
            },
            '&-disabled': {
                color: colorTextDisabled,
                backgroundColor: colorBgContainerDisabled,
                borderColor: colorBorder,
                cursor: 'not-allowed',
                '&:first-child, &:hover': {
                    color: colorTextDisabled,
                    backgroundColor: colorBgContainerDisabled,
                    borderColor: colorBorder
                }
            },
            [`&-disabled${componentCls}-button-wrapper-checked`]: {
                color: buttonCheckedColorDisabled,
                backgroundColor: buttonCheckedBgDisabled,
                borderColor: colorBorder,
                boxShadow: 'none'
            },
            '&-block': {
                flex: 1,
                textAlign: 'center'
            }
        }
    };
};
const prepareComponentToken = (token)=>{
    const { wireframe, padding, marginXS, lineWidth, fontSizeLG, colorText, colorBgContainer, colorTextDisabled, controlItemBgActiveDisabled, colorTextLightSolid, colorPrimary, colorPrimaryHover, colorPrimaryActive, colorWhite } = token;
    const dotPadding = 4; // Fixed value
    const radioSize = fontSizeLG;
    const radioDotSize = wireframe ? radioSize - dotPadding * 2 : radioSize - (dotPadding + lineWidth) * 2;
    return {
        // Radio
        radioSize,
        dotSize: radioDotSize,
        dotColorDisabled: colorTextDisabled,
        // Radio buttons
        buttonSolidCheckedColor: colorTextLightSolid,
        buttonSolidCheckedBg: colorPrimary,
        buttonSolidCheckedHoverBg: colorPrimaryHover,
        buttonSolidCheckedActiveBg: colorPrimaryActive,
        buttonBg: colorBgContainer,
        buttonCheckedBg: colorBgContainer,
        buttonColor: colorText,
        buttonCheckedBgDisabled: controlItemBgActiveDisabled,
        buttonCheckedColorDisabled: colorTextDisabled,
        buttonPaddingInline: padding - lineWidth,
        wrapperMarginInlineEnd: marginXS,
        // internal
        radioColor: wireframe ? colorPrimary : colorWhite,
        radioBgColor: wireframe ? colorBgContainer : colorPrimary
    };
};
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genStyleHooks"])('Radio', (token)=>{
    const { controlOutline, controlOutlineWidth } = token;
    const radioFocusShadow = `0 0 0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(controlOutlineWidth)} ${controlOutline}`;
    const radioButtonFocusShadow = radioFocusShadow;
    const radioToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__["mergeToken"])(token, {
        radioFocusShadow,
        radioButtonFocusShadow
    });
    return [
        getGroupRadioStyle(radioToken),
        getRadioBasicStyle(radioToken),
        getRadioButtonStyle(radioToken)
    ];
}, prepareComponentToken, {
    unitless: {
        radioSize: true,
        dotSize: true
    }
});
}}),
"[project]/node_modules/antd/es/radio/radio.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$checkbox$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-checkbox/es/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/ref.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$wave$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/wave/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$wave$2f$interface$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/wave/interface.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$checkbox$2f$useBubbleLock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/checkbox/useBubbleLock.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$DisabledContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/DisabledContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useCSSVarCls$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/hooks/useCSSVarCls.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$radio$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/radio/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$radio$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/radio/style/index.js [app-client] (ecmascript)");
"use client";
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const InternalRadio = (props, ref)=>{
    var _a, _b;
    const groupContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$radio$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
    const radioOptionTypeContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$radio$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RadioOptionTypeContext"]);
    const { getPrefixCls, direction, radio } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfigContext"]);
    const innerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const mergedRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["composeRef"])(ref, innerRef);
    const { isFormItemInput } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormItemInputContext"]);
    if ("TURBOPACK compile-time truthy", 1) {
        const warning = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["devUseWarning"])('Radio');
        ("TURBOPACK compile-time truthy", 1) ? warning(!('optionType' in props), 'usage', '`optionType` is only support in Radio.Group.') : ("TURBOPACK unreachable", undefined);
    }
    const onChange = (e)=>{
        var _a, _b;
        (_a = props.onChange) === null || _a === void 0 ? void 0 : _a.call(props, e);
        (_b = groupContext === null || groupContext === void 0 ? void 0 : groupContext.onChange) === null || _b === void 0 ? void 0 : _b.call(groupContext, e);
    };
    const { prefixCls: customizePrefixCls, className, rootClassName, children, style, title } = props, restProps = __rest(props, [
        "prefixCls",
        "className",
        "rootClassName",
        "children",
        "style",
        "title"
    ]);
    const radioPrefixCls = getPrefixCls('radio', customizePrefixCls);
    const isButtonType = ((groupContext === null || groupContext === void 0 ? void 0 : groupContext.optionType) || radioOptionTypeContext) === 'button';
    const prefixCls = isButtonType ? `${radioPrefixCls}-button` : radioPrefixCls;
    // Style
    const rootCls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useCSSVarCls$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(radioPrefixCls);
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$radio$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(radioPrefixCls, rootCls);
    const radioProps = Object.assign({}, restProps);
    // ===================== Disabled =====================
    const disabled = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$DisabledContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
    if (groupContext) {
        radioProps.name = groupContext.name;
        radioProps.onChange = onChange;
        radioProps.checked = props.value === groupContext.value;
        radioProps.disabled = (_a = radioProps.disabled) !== null && _a !== void 0 ? _a : groupContext.disabled;
    }
    radioProps.disabled = (_b = radioProps.disabled) !== null && _b !== void 0 ? _b : disabled;
    const wrapperClassString = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(`${prefixCls}-wrapper`, {
        [`${prefixCls}-wrapper-checked`]: radioProps.checked,
        [`${prefixCls}-wrapper-disabled`]: radioProps.disabled,
        [`${prefixCls}-wrapper-rtl`]: direction === 'rtl',
        [`${prefixCls}-wrapper-in-form-item`]: isFormItemInput,
        [`${prefixCls}-wrapper-block`]: !!(groupContext === null || groupContext === void 0 ? void 0 : groupContext.block)
    }, radio === null || radio === void 0 ? void 0 : radio.className, className, rootClassName, hashId, cssVarCls, rootCls);
    // ============================ Event Lock ============================
    const [onLabelClick, onInputClick] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$checkbox$2f$useBubbleLock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(radioProps.onClick);
    // ============================== Render ==============================
    return wrapCSSVar(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$wave$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        component: "Radio",
        disabled: radioProps.disabled
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("label", {
        className: wrapperClassString,
        style: Object.assign(Object.assign({}, radio === null || radio === void 0 ? void 0 : radio.style), style),
        onMouseEnter: props.onMouseEnter,
        onMouseLeave: props.onMouseLeave,
        title: title,
        onClick: onLabelClick
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$checkbox$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], Object.assign({}, radioProps, {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(radioProps.className, {
            [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$wave$2f$interface$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TARGET_CLS"]]: !isButtonType
        }),
        type: "radio",
        prefixCls: prefixCls,
        ref: mergedRef,
        onClick: onInputClick
    })), children !== undefined ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("span", {
        className: `${prefixCls}-label`
    }, children) : null)));
};
const Radio = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(InternalRadio);
if ("TURBOPACK compile-time truthy", 1) {
    Radio.displayName = 'Radio';
}
const __TURBOPACK__default__export__ = Radio;
}}),
"[project]/node_modules/antd/es/radio/group.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useMergedState$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useMergedState.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$pickAttrs$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/pickAttrs.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useCSSVarCls$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/hooks/useCSSVarCls.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useSize$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/hooks/useSize.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$radio$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/radio/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$radio$2f$radio$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/radio/radio.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$radio$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/radio/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useId$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useId.js [app-client] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
const RadioGroup = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((props, ref)=>{
    const { getPrefixCls, direction } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfigContext"]);
    const defaultName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useId$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    const { prefixCls: customizePrefixCls, className, rootClassName, options, buttonStyle = 'outline', disabled, children, size: customizeSize, style, id, optionType, name = defaultName, defaultValue, value: customizedValue, block = false, onChange, onMouseEnter, onMouseLeave, onFocus, onBlur } = props;
    const [value, setValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useMergedState$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(defaultValue, {
        value: customizedValue
    });
    const onRadioChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "RadioGroup.useCallback[onRadioChange]": (event)=>{
            const lastValue = value;
            const val = event.target.value;
            if (!('value' in props)) {
                setValue(val);
            }
            if (val !== lastValue) {
                onChange === null || onChange === void 0 ? void 0 : onChange(event);
            }
        }
    }["RadioGroup.useCallback[onRadioChange]"], [
        value,
        setValue,
        onChange
    ]);
    const prefixCls = getPrefixCls('radio', customizePrefixCls);
    const groupPrefixCls = `${prefixCls}-group`;
    // Style
    const rootCls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useCSSVarCls$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls);
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$radio$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, rootCls);
    let childrenToRender = children;
    // 如果存在 options, 优先使用
    if (options && options.length > 0) {
        childrenToRender = options.map((option)=>{
            if (typeof option === 'string' || typeof option === 'number') {
                // 此处类型自动推导为 string
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$radio$2f$radio$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    key: option.toString(),
                    prefixCls: prefixCls,
                    disabled: disabled,
                    value: option,
                    checked: value === option
                }, option);
            }
            // 此处类型自动推导为 { label: string value: string }
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$radio$2f$radio$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                key: `radio-group-value-options-${option.value}`,
                prefixCls: prefixCls,
                disabled: option.disabled || disabled,
                value: option.value,
                checked: value === option.value,
                title: option.title,
                style: option.style,
                id: option.id,
                required: option.required
            }, option.label);
        });
    }
    const mergedSize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useSize$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(customizeSize);
    const classString = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(groupPrefixCls, `${groupPrefixCls}-${buttonStyle}`, {
        [`${groupPrefixCls}-${mergedSize}`]: mergedSize,
        [`${groupPrefixCls}-rtl`]: direction === 'rtl',
        [`${groupPrefixCls}-block`]: block
    }, className, rootClassName, hashId, cssVarCls, rootCls);
    const memoizedValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "RadioGroup.useMemo[memoizedValue]": ()=>({
                onChange: onRadioChange,
                value,
                disabled,
                name,
                optionType,
                block
            })
    }["RadioGroup.useMemo[memoizedValue]"], [
        onRadioChange,
        value,
        disabled,
        name,
        optionType,
        block
    ]);
    return wrapCSSVar(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$pickAttrs$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props, {
        aria: true,
        data: true
    }), {
        className: classString,
        style: style,
        onMouseEnter: onMouseEnter,
        onMouseLeave: onMouseLeave,
        onFocus: onFocus,
        onBlur: onBlur,
        id: id,
        ref: ref
    }), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$radio$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RadioGroupContextProvider"], {
        value: memoizedValue
    }, childrenToRender)));
});
const __TURBOPACK__default__export__ = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(RadioGroup);
}}),
"[project]/node_modules/antd/es/radio/radioButton.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$radio$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/radio/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$radio$2f$radio$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/radio/radio.js [app-client] (ecmascript)");
"use client";
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
;
;
;
;
const RadioButton = (props, ref)=>{
    const { getPrefixCls } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfigContext"]);
    const { prefixCls: customizePrefixCls } = props, radioProps = __rest(props, [
        "prefixCls"
    ]);
    const prefixCls = getPrefixCls('radio', customizePrefixCls);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$radio$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RadioOptionTypeContextProvider"], {
        value: "button"
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$radio$2f$radio$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], Object.assign({
        prefixCls: prefixCls
    }, radioProps, {
        type: "radio",
        ref: ref
    })));
};
const __TURBOPACK__default__export__ = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(RadioButton);
}}),
"[project]/node_modules/antd/es/radio/index.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$radio$2f$group$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/radio/group.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$radio$2f$radio$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/radio/radio.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$radio$2f$radioButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/radio/radioButton.js [app-client] (ecmascript)");
"use client";
;
;
;
;
const Radio = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$radio$2f$radio$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
Radio.Button = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$radio$2f$radioButton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
Radio.Group = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$radio$2f$group$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
Radio.__ANT_RADIO = true;
const __TURBOPACK__default__export__ = Radio;
}}),
"[project]/node_modules/antd/es/empty/empty.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$fast$2d$color$2f$es$2f$FastColor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/fast-color/es/FastColor.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$locale$2f$useLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useLocale$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/locale/useLocale.js [app-client] (ecmascript) <export default as useLocale>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$useToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useToken$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/useToken.js [app-client] (ecmascript) <export default as useToken>");
"use client";
;
;
;
;
const Empty = ()=>{
    const [, token] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$useToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useToken$3e$__["useToken"])();
    const [locale] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$locale$2f$useLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useLocale$3e$__["useLocale"])('Empty');
    const bgColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$fast$2d$color$2f$es$2f$FastColor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FastColor"](token.colorBgBase);
    // Dark Theme need more dark of this
    const themeStyle = bgColor.toHsl().l < 0.5 ? {
        opacity: 0.65
    } : {};
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("svg", {
        style: themeStyle,
        width: "184",
        height: "152",
        viewBox: "0 0 184 152",
        xmlns: "http://www.w3.org/2000/svg"
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("title", null, (locale === null || locale === void 0 ? void 0 : locale.description) || 'Empty'), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("g", {
        fill: "none",
        fillRule: "evenodd"
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("g", {
        transform: "translate(24 31.67)"
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("ellipse", {
        fillOpacity: ".8",
        fill: "#F5F5F7",
        cx: "67.797",
        cy: "106.89",
        rx: "67.797",
        ry: "12.668"
    }), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
        d: "M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z",
        fill: "#AEB8C2"
    }), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
        d: "M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z",
        fill: "url(#linearGradient-1)",
        transform: "translate(13.56)"
    }), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
        d: "M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z",
        fill: "#F5F5F7"
    }), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
        d: "M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z",
        fill: "#DCE0E6"
    })), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
        d: "M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z",
        fill: "#DCE0E6"
    }), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("g", {
        transform: "translate(149.65 15.383)",
        fill: "#FFF"
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("ellipse", {
        cx: "20.654",
        cy: "3.167",
        rx: "2.849",
        ry: "2.815"
    }), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
        d: "M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z"
    }))));
};
if ("TURBOPACK compile-time truthy", 1) {
    Empty.displayName = 'EmptyImage';
}
const __TURBOPACK__default__export__ = Empty;
}}),
"[project]/node_modules/antd/es/empty/simple.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$fast$2d$color$2f$es$2f$FastColor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/fast-color/es/FastColor.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$useToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useToken$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/useToken.js [app-client] (ecmascript) <export default as useToken>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$locale$2f$useLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useLocale$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/locale/useLocale.js [app-client] (ecmascript) <export default as useLocale>");
"use client";
;
;
;
;
;
const Simple = ()=>{
    const [, token] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$useToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useToken$3e$__["useToken"])();
    const [locale] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$locale$2f$useLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useLocale$3e$__["useLocale"])('Empty');
    const { colorFill, colorFillTertiary, colorFillQuaternary, colorBgContainer } = token;
    const { borderColor, shadowColor, contentColor } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Simple.useMemo": ()=>({
                borderColor: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$fast$2d$color$2f$es$2f$FastColor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FastColor"](colorFill).onBackground(colorBgContainer).toHexString(),
                shadowColor: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$fast$2d$color$2f$es$2f$FastColor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FastColor"](colorFillTertiary).onBackground(colorBgContainer).toHexString(),
                contentColor: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$fast$2d$color$2f$es$2f$FastColor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FastColor"](colorFillQuaternary).onBackground(colorBgContainer).toHexString()
            })
    }["Simple.useMemo"], [
        colorFill,
        colorFillTertiary,
        colorFillQuaternary,
        colorBgContainer
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("svg", {
        width: "64",
        height: "41",
        viewBox: "0 0 64 41",
        xmlns: "http://www.w3.org/2000/svg"
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("title", null, (locale === null || locale === void 0 ? void 0 : locale.description) || 'Empty'), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("g", {
        transform: "translate(0 1)",
        fill: "none",
        fillRule: "evenodd"
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("ellipse", {
        fill: shadowColor,
        cx: "32",
        cy: "33",
        rx: "32",
        ry: "7"
    }), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("g", {
        fillRule: "nonzero",
        stroke: borderColor
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
        d: "M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
    }), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("path", {
        d: "M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z",
        fill: contentColor
    }))));
};
if ("TURBOPACK compile-time truthy", 1) {
    Simple.displayName = 'SimpleImage';
}
const __TURBOPACK__default__export__ = Simple;
}}),
"[project]/node_modules/antd/es/empty/style/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/util/genStyleUtils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs-utils/es/util/statistic.js [app-client] (ecmascript) <export merge as mergeToken>");
;
// ============================== Shared ==============================
const genSharedEmptyStyle = (token)=>{
    const { componentCls, margin, marginXS, marginXL, fontSize, lineHeight } = token;
    return {
        [componentCls]: {
            marginInline: marginXS,
            fontSize,
            lineHeight,
            textAlign: 'center',
            // 原来 &-image 没有父子结构，现在为了外层承担我们的 hashId，改成父子结构
            [`${componentCls}-image`]: {
                height: token.emptyImgHeight,
                marginBottom: marginXS,
                opacity: token.opacityImage,
                img: {
                    height: '100%'
                },
                svg: {
                    maxWidth: '100%',
                    height: '100%',
                    margin: 'auto'
                }
            },
            [`${componentCls}-description`]: {
                color: token.colorTextDescription
            },
            // 原来 &-footer 没有父子结构，现在为了外层承担我们的 hashId，改成父子结构
            [`${componentCls}-footer`]: {
                marginTop: margin
            },
            '&-normal': {
                marginBlock: marginXL,
                color: token.colorTextDescription,
                [`${componentCls}-description`]: {
                    color: token.colorTextDescription
                },
                [`${componentCls}-image`]: {
                    height: token.emptyImgHeightMD
                }
            },
            '&-small': {
                marginBlock: marginXS,
                color: token.colorTextDescription,
                [`${componentCls}-image`]: {
                    height: token.emptyImgHeightSM
                }
            }
        }
    };
};
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genStyleHooks"])('Empty', (token)=>{
    const { componentCls, controlHeightLG, calc } = token;
    const emptyToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__["mergeToken"])(token, {
        emptyImgCls: `${componentCls}-img`,
        emptyImgHeight: calc(controlHeightLG).mul(2.5).equal(),
        emptyImgHeightMD: controlHeightLG,
        emptyImgHeightSM: calc(controlHeightLG).mul(0.875).equal()
    });
    return [
        genSharedEmptyStyle(emptyToken)
    ];
});
}}),
"[project]/node_modules/antd/es/empty/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$locale$2f$useLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useLocale$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/locale/useLocale.js [app-client] (ecmascript) <export default as useLocale>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$empty$2f$empty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/empty/empty.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$empty$2f$simple$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/empty/simple.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$empty$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/empty/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
"use client";
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
;
;
;
;
;
;
;
;
const defaultEmptyImg = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$empty$2f$empty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null);
const simpleEmptyImg = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$empty$2f$simple$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null);
const Empty = (props)=>{
    const { className, rootClassName, prefixCls: customizePrefixCls, image = defaultEmptyImg, description, children, imageStyle, style, classNames: emptyClassNames, styles } = props, restProps = __rest(props, [
        "className",
        "rootClassName",
        "prefixCls",
        "image",
        "description",
        "children",
        "imageStyle",
        "style",
        "classNames",
        "styles"
    ]);
    const { getPrefixCls, direction, className: contextClassName, style: contextStyle, classNames: contextClassNames, styles: contextStyles } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useComponentConfig"])('empty');
    const prefixCls = getPrefixCls('empty', customizePrefixCls);
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$empty$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls);
    const [locale] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$locale$2f$useLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useLocale$3e$__["useLocale"])('Empty');
    const des = typeof description !== 'undefined' ? description : locale === null || locale === void 0 ? void 0 : locale.description;
    const alt = typeof des === 'string' ? des : 'empty';
    let imageNode = null;
    if (typeof image === 'string') {
        imageNode = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("img", {
            alt: alt,
            src: image
        });
    } else {
        imageNode = image;
    }
    // ============================= Warning ==============================
    if ("TURBOPACK compile-time truthy", 1) {
        const warning = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["devUseWarning"])('Empty');
        [
            [
                'imageStyle',
                'styles: { image: {} }'
            ]
        ].forEach((_ref)=>{
            let [deprecatedName, newName] = _ref;
            warning.deprecated(!(deprecatedName in props), deprecatedName, newName);
        });
    }
    return wrapCSSVar(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", Object.assign({
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(hashId, cssVarCls, prefixCls, contextClassName, {
            [`${prefixCls}-normal`]: image === simpleEmptyImg,
            [`${prefixCls}-rtl`]: direction === 'rtl'
        }, className, rootClassName, contextClassNames.root, emptyClassNames === null || emptyClassNames === void 0 ? void 0 : emptyClassNames.root),
        style: Object.assign(Object.assign(Object.assign(Object.assign({}, contextStyles.root), contextStyle), styles === null || styles === void 0 ? void 0 : styles.root), style)
    }, restProps), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(`${prefixCls}-image`, contextClassNames.image, emptyClassNames === null || emptyClassNames === void 0 ? void 0 : emptyClassNames.image),
        style: Object.assign(Object.assign(Object.assign({}, imageStyle), contextStyles.image), styles === null || styles === void 0 ? void 0 : styles.image)
    }, imageNode), des && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(`${prefixCls}-description`, contextClassNames.description, emptyClassNames === null || emptyClassNames === void 0 ? void 0 : emptyClassNames.description),
        style: Object.assign(Object.assign({}, contextStyles.description), styles === null || styles === void 0 ? void 0 : styles.description)
    }, des), children && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(`${prefixCls}-footer`, contextClassNames.footer, emptyClassNames === null || emptyClassNames === void 0 ? void 0 : emptyClassNames.footer),
        style: Object.assign(Object.assign({}, contextStyles.footer), styles === null || styles === void 0 ? void 0 : styles.footer)
    }, children)));
};
Empty.PRESENTED_IMAGE_DEFAULT = defaultEmptyImg;
Empty.PRESENTED_IMAGE_SIMPLE = simpleEmptyImg;
if ("TURBOPACK compile-time truthy", 1) {
    Empty.displayName = 'Empty';
}
const __TURBOPACK__default__export__ = Empty;
}}),
"[project]/node_modules/antd/es/config-provider/defaultRenderEmpty.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$empty$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/empty/index.js [app-client] (ecmascript)");
"use client";
;
;
;
const DefaultRenderEmpty = (props)=>{
    const { componentName } = props;
    const { getPrefixCls } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfigContext"]);
    const prefix = getPrefixCls('empty');
    switch(componentName){
        case 'Table':
        case 'List':
            return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$empty$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                image: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$empty$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].PRESENTED_IMAGE_SIMPLE
            });
        case 'Select':
        case 'TreeSelect':
        case 'Cascader':
        case 'Transfer':
        case 'Mentions':
            return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$empty$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                image: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$empty$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].PRESENTED_IMAGE_SIMPLE,
                className: `${prefix}-small`
            });
        /**
     * This type of component should satisfy the nullish coalescing operator(??) on the left-hand side.
     * to let the component itself implement the logic.
     * For example `Table.filter`.
     */ case 'Table.filter':
            // why `null`? legacy react16 node type `undefined` is not allowed.
            return null;
        default:
            // Should never hit if we take all the component into consider.
            return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$empty$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null);
    }
};
const __TURBOPACK__default__export__ = DefaultRenderEmpty;
}}),
"[project]/node_modules/antd/es/select/mergedBuiltinPlacements.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const getBuiltInPlacements = (popupOverflow)=>{
    const htmlRegion = popupOverflow === 'scroll' ? 'scroll' : 'visible';
    const sharedConfig = {
        overflow: {
            adjustX: true,
            adjustY: true,
            shiftY: true
        },
        htmlRegion,
        dynamicInset: true
    };
    return {
        bottomLeft: Object.assign(Object.assign({}, sharedConfig), {
            points: [
                'tl',
                'bl'
            ],
            offset: [
                0,
                4
            ]
        }),
        bottomRight: Object.assign(Object.assign({}, sharedConfig), {
            points: [
                'tr',
                'br'
            ],
            offset: [
                0,
                4
            ]
        }),
        topLeft: Object.assign(Object.assign({}, sharedConfig), {
            points: [
                'bl',
                'tl'
            ],
            offset: [
                0,
                -4
            ]
        }),
        topRight: Object.assign(Object.assign({}, sharedConfig), {
            points: [
                'br',
                'tr'
            ],
            offset: [
                0,
                -4
            ]
        })
    };
};
function mergedBuiltinPlacements(buildInPlacements, popupOverflow) {
    return buildInPlacements || getBuiltInPlacements(popupOverflow);
}
const __TURBOPACK__default__export__ = mergedBuiltinPlacements;
}}),
"[project]/node_modules/antd/es/select/style/dropdown.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$motion$2f$move$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/style/motion/move.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$motion$2f$slide$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/style/motion/slide.js [app-client] (ecmascript)");
;
;
const genItemStyle = (token)=>{
    const { optionHeight, optionFontSize, optionLineHeight, optionPadding } = token;
    return {
        position: 'relative',
        display: 'block',
        minHeight: optionHeight,
        padding: optionPadding,
        color: token.colorText,
        fontWeight: 'normal',
        fontSize: optionFontSize,
        lineHeight: optionLineHeight,
        boxSizing: 'border-box'
    };
};
const genSingleStyle = (token)=>{
    const { antCls, componentCls } = token;
    const selectItemCls = `${componentCls}-item`;
    const slideUpEnterActive = `&${antCls}-slide-up-enter${antCls}-slide-up-enter-active`;
    const slideUpAppearActive = `&${antCls}-slide-up-appear${antCls}-slide-up-appear-active`;
    const slideUpLeaveActive = `&${antCls}-slide-up-leave${antCls}-slide-up-leave-active`;
    const dropdownPlacementCls = `${componentCls}-dropdown-placement-`;
    const selectedItemCls = `${selectItemCls}-option-selected`;
    return [
        {
            [`${componentCls}-dropdown`]: Object.assign(Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resetComponent"])(token)), {
                position: 'absolute',
                top: -9999,
                zIndex: token.zIndexPopup,
                boxSizing: 'border-box',
                padding: token.paddingXXS,
                overflow: 'hidden',
                fontSize: token.fontSize,
                // Fix select render lag of long text in chrome
                // https://github.com/ant-design/ant-design/issues/11456
                // https://github.com/ant-design/ant-design/issues/11843
                fontVariant: 'initial',
                backgroundColor: token.colorBgElevated,
                borderRadius: token.borderRadiusLG,
                outline: 'none',
                boxShadow: token.boxShadowSecondary,
                [`
          ${slideUpEnterActive}${dropdownPlacementCls}bottomLeft,
          ${slideUpAppearActive}${dropdownPlacementCls}bottomLeft
        `]: {
                    animationName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$motion$2f$slide$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["slideUpIn"]
                },
                [`
          ${slideUpEnterActive}${dropdownPlacementCls}topLeft,
          ${slideUpAppearActive}${dropdownPlacementCls}topLeft,
          ${slideUpEnterActive}${dropdownPlacementCls}topRight,
          ${slideUpAppearActive}${dropdownPlacementCls}topRight
        `]: {
                    animationName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$motion$2f$slide$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["slideDownIn"]
                },
                [`${slideUpLeaveActive}${dropdownPlacementCls}bottomLeft`]: {
                    animationName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$motion$2f$slide$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["slideUpOut"]
                },
                [`
          ${slideUpLeaveActive}${dropdownPlacementCls}topLeft,
          ${slideUpLeaveActive}${dropdownPlacementCls}topRight
        `]: {
                    animationName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$motion$2f$slide$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["slideDownOut"]
                },
                '&-hidden': {
                    display: 'none'
                },
                [selectItemCls]: Object.assign(Object.assign({}, genItemStyle(token)), {
                    cursor: 'pointer',
                    transition: `background ${token.motionDurationSlow} ease`,
                    borderRadius: token.borderRadiusSM,
                    // =========== Group ============
                    '&-group': {
                        color: token.colorTextDescription,
                        fontSize: token.fontSizeSM,
                        cursor: 'default'
                    },
                    // =========== Option ===========
                    '&-option': {
                        display: 'flex',
                        '&-content': Object.assign({
                            flex: 'auto'
                        }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["textEllipsis"]),
                        '&-state': {
                            flex: 'none',
                            display: 'flex',
                            alignItems: 'center'
                        },
                        [`&-active:not(${selectItemCls}-option-disabled)`]: {
                            backgroundColor: token.optionActiveBg
                        },
                        [`&-selected:not(${selectItemCls}-option-disabled)`]: {
                            color: token.optionSelectedColor,
                            fontWeight: token.optionSelectedFontWeight,
                            backgroundColor: token.optionSelectedBg,
                            [`${selectItemCls}-option-state`]: {
                                color: token.colorPrimary
                            }
                        },
                        '&-disabled': {
                            [`&${selectItemCls}-option-selected`]: {
                                backgroundColor: token.colorBgContainerDisabled
                            },
                            color: token.colorTextDisabled,
                            cursor: 'not-allowed'
                        },
                        '&-grouped': {
                            paddingInlineStart: token.calc(token.controlPaddingHorizontal).mul(2).equal()
                        }
                    },
                    '&-empty': Object.assign(Object.assign({}, genItemStyle(token)), {
                        color: token.colorTextDisabled
                    })
                }),
                // https://github.com/ant-design/ant-design/pull/46646
                [`${selectedItemCls}:has(+ ${selectedItemCls})`]: {
                    borderEndStartRadius: 0,
                    borderEndEndRadius: 0,
                    [`& + ${selectedItemCls}`]: {
                        borderStartStartRadius: 0,
                        borderStartEndRadius: 0
                    }
                },
                // =========================== RTL ===========================
                '&-rtl': {
                    direction: 'rtl'
                }
            })
        },
        // Follow code may reuse in other components
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$motion$2f$slide$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initSlideMotion"])(token, 'slide-up'),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$motion$2f$slide$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initSlideMotion"])(token, 'slide-down'),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$motion$2f$move$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initMoveMotion"])(token, 'move-up'),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$motion$2f$move$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initMoveMotion"])(token, 'move-down')
    ];
};
const __TURBOPACK__default__export__ = genSingleStyle;
}}),
"[project]/node_modules/antd/es/select/style/multiple.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "genOverflowStyle": (()=>genOverflowStyle),
    "getMultipleSelectorUnit": (()=>getMultipleSelectorUnit)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/util/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs-utils/es/util/statistic.js [app-client] (ecmascript) <export merge as mergeToken>");
;
;
;
const getMultipleSelectorUnit = (token)=>{
    const { multipleSelectItemHeight, paddingXXS, lineWidth, INTERNAL_FIXED_ITEM_MARGIN } = token;
    const basePadding = token.max(token.calc(paddingXXS).sub(lineWidth).equal(), 0);
    const containerPadding = token.max(token.calc(basePadding).sub(INTERNAL_FIXED_ITEM_MARGIN).equal(), 0);
    return {
        basePadding,
        containerPadding,
        itemHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(multipleSelectItemHeight),
        itemLineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.calc(multipleSelectItemHeight).sub(token.calc(token.lineWidth).mul(2)).equal())
    };
};
const getSelectItemStyle = (token)=>{
    const { multipleSelectItemHeight, selectHeight, lineWidth } = token;
    const selectItemDist = token.calc(selectHeight).sub(multipleSelectItemHeight).div(2).sub(lineWidth).equal();
    return selectItemDist;
};
const genOverflowStyle = (token)=>{
    const { componentCls, iconCls, borderRadiusSM, motionDurationSlow, paddingXS, multipleItemColorDisabled, multipleItemBorderColorDisabled, colorIcon, colorIconHover, INTERNAL_FIXED_ITEM_MARGIN } = token;
    const selectOverflowPrefixCls = `${componentCls}-selection-overflow`;
    return {
        /**
     * Do not merge `height` & `line-height` under style with `selection` & `search`, since chrome
     * may update to redesign with its align logic.
     */ // =========================== Overflow ===========================
        [selectOverflowPrefixCls]: {
            position: 'relative',
            display: 'flex',
            flex: 'auto',
            flexWrap: 'wrap',
            maxWidth: '100%',
            '&-item': {
                flex: 'none',
                alignSelf: 'center',
                maxWidth: '100%',
                display: 'inline-flex'
            },
            // ======================== Selections ==========================
            [`${componentCls}-selection-item`]: {
                display: 'flex',
                alignSelf: 'center',
                flex: 'none',
                boxSizing: 'border-box',
                maxWidth: '100%',
                marginBlock: INTERNAL_FIXED_ITEM_MARGIN,
                borderRadius: borderRadiusSM,
                cursor: 'default',
                transition: `font-size ${motionDurationSlow}, line-height ${motionDurationSlow}, height ${motionDurationSlow}`,
                marginInlineEnd: token.calc(INTERNAL_FIXED_ITEM_MARGIN).mul(2).equal(),
                paddingInlineStart: paddingXS,
                paddingInlineEnd: token.calc(paddingXS).div(2).equal(),
                [`${componentCls}-disabled&`]: {
                    color: multipleItemColorDisabled,
                    borderColor: multipleItemBorderColorDisabled,
                    cursor: 'not-allowed'
                },
                // It's ok not to do this, but 24px makes bottom narrow in view should adjust
                '&-content': {
                    display: 'inline-block',
                    marginInlineEnd: token.calc(paddingXS).div(2).equal(),
                    overflow: 'hidden',
                    whiteSpace: 'pre',
                    // fix whitespace wrapping. custom tags display all whitespace within.
                    textOverflow: 'ellipsis'
                },
                '&-remove': Object.assign(Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resetIcon"])()), {
                    display: 'inline-flex',
                    alignItems: 'center',
                    color: colorIcon,
                    fontWeight: 'bold',
                    fontSize: 10,
                    lineHeight: 'inherit',
                    cursor: 'pointer',
                    [`> ${iconCls}`]: {
                        verticalAlign: '-0.2em'
                    },
                    '&:hover': {
                        color: colorIconHover
                    }
                })
            }
        }
    };
};
const genSelectionStyle = (token, suffix)=>{
    const { componentCls, INTERNAL_FIXED_ITEM_MARGIN } = token;
    const selectOverflowPrefixCls = `${componentCls}-selection-overflow`;
    const selectItemHeight = token.multipleSelectItemHeight;
    const selectItemDist = getSelectItemStyle(token);
    const suffixCls = suffix ? `${componentCls}-${suffix}` : '';
    const multipleSelectorUnit = getMultipleSelectorUnit(token);
    return {
        [`${componentCls}-multiple${suffixCls}`]: Object.assign(Object.assign({}, genOverflowStyle(token)), {
            // ========================= Selector =========================
            [`${componentCls}-selector`]: {
                display: 'flex',
                alignItems: 'center',
                width: '100%',
                height: '100%',
                // Multiple is little different that horizontal is follow the vertical
                paddingInline: multipleSelectorUnit.basePadding,
                paddingBlock: multipleSelectorUnit.containerPadding,
                borderRadius: token.borderRadius,
                [`${componentCls}-disabled&`]: {
                    background: token.multipleSelectorBgDisabled,
                    cursor: 'not-allowed'
                },
                '&:after': {
                    display: 'inline-block',
                    width: 0,
                    margin: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(INTERNAL_FIXED_ITEM_MARGIN)} 0`,
                    lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(selectItemHeight),
                    visibility: 'hidden',
                    content: '"\\a0"'
                }
            },
            // ======================== Selections ========================
            [`${componentCls}-selection-item`]: {
                height: multipleSelectorUnit.itemHeight,
                lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(multipleSelectorUnit.itemLineHeight)
            },
            // ========================== Wrap ===========================
            [`${componentCls}-selection-wrap`]: {
                alignSelf: 'flex-start',
                '&:after': {
                    lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(selectItemHeight),
                    marginBlock: INTERNAL_FIXED_ITEM_MARGIN
                }
            },
            // ========================== Input ==========================
            [`${componentCls}-prefix`]: {
                marginInlineStart: token.calc(token.inputPaddingHorizontalBase).sub(multipleSelectorUnit.basePadding).equal()
            },
            [`${selectOverflowPrefixCls}-item + ${selectOverflowPrefixCls}-item,
        ${componentCls}-prefix + ${componentCls}-selection-wrap
      `]: {
                [`${componentCls}-selection-search`]: {
                    marginInlineStart: 0
                },
                [`${componentCls}-selection-placeholder`]: {
                    insetInlineStart: 0
                }
            },
            // https://github.com/ant-design/ant-design/issues/44754
            // Same as `wrap:after`
            [`${selectOverflowPrefixCls}-item-suffix`]: {
                minHeight: multipleSelectorUnit.itemHeight,
                marginBlock: INTERNAL_FIXED_ITEM_MARGIN
            },
            [`${componentCls}-selection-search`]: {
                display: 'inline-flex',
                position: 'relative',
                maxWidth: '100%',
                marginInlineStart: token.calc(token.inputPaddingHorizontalBase).sub(selectItemDist).equal(),
                [`
          &-input,
          &-mirror
        `]: {
                    height: selectItemHeight,
                    fontFamily: token.fontFamily,
                    lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(selectItemHeight),
                    transition: `all ${token.motionDurationSlow}`
                },
                '&-input': {
                    width: '100%',
                    minWidth: 4.1 // fix search cursor missing
                },
                '&-mirror': {
                    position: 'absolute',
                    top: 0,
                    insetInlineStart: 0,
                    insetInlineEnd: 'auto',
                    zIndex: 999,
                    whiteSpace: 'pre',
                    // fix whitespace wrapping caused width calculation bug
                    visibility: 'hidden'
                }
            },
            // ======================= Placeholder =======================
            [`${componentCls}-selection-placeholder`]: {
                position: 'absolute',
                top: '50%',
                insetInlineStart: token.calc(token.inputPaddingHorizontalBase).sub(multipleSelectorUnit.basePadding).equal(),
                insetInlineEnd: token.inputPaddingHorizontalBase,
                transform: 'translateY(-50%)',
                transition: `all ${token.motionDurationSlow}`
            }
        })
    };
};
function genSizeStyle(token, suffix) {
    const { componentCls } = token;
    const suffixCls = suffix ? `${componentCls}-${suffix}` : '';
    const rawStyle = {
        [`${componentCls}-multiple${suffixCls}`]: {
            fontSize: token.fontSize,
            // ========================= Selector =========================
            [`${componentCls}-selector`]: {
                [`${componentCls}-show-search&`]: {
                    cursor: 'text'
                }
            },
            [`
        &${componentCls}-show-arrow ${componentCls}-selector,
        &${componentCls}-allow-clear ${componentCls}-selector
      `]: {
                paddingInlineEnd: token.calc(token.fontSizeIcon).add(token.controlPaddingHorizontal).equal()
            }
        }
    };
    return [
        genSelectionStyle(token, suffix),
        rawStyle
    ];
}
const genMultipleStyle = (token)=>{
    const { componentCls } = token;
    const smallToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__["mergeToken"])(token, {
        selectHeight: token.controlHeightSM,
        multipleSelectItemHeight: token.multipleItemHeightSM,
        borderRadius: token.borderRadiusSM,
        borderRadiusSM: token.borderRadiusXS
    });
    const largeToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__["mergeToken"])(token, {
        fontSize: token.fontSizeLG,
        selectHeight: token.controlHeightLG,
        multipleSelectItemHeight: token.multipleItemHeightLG,
        borderRadius: token.borderRadiusLG,
        borderRadiusSM: token.borderRadius
    });
    return [
        genSizeStyle(token),
        // ======================== Small ========================
        genSizeStyle(smallToken, 'sm'),
        // Padding
        {
            [`${componentCls}-multiple${componentCls}-sm`]: {
                [`${componentCls}-selection-placeholder`]: {
                    insetInline: token.calc(token.controlPaddingHorizontalSM).sub(token.lineWidth).equal()
                },
                // https://github.com/ant-design/ant-design/issues/29559
                [`${componentCls}-selection-search`]: {
                    marginInlineStart: 2 // Magic Number
                }
            }
        },
        // ======================== Large ========================
        genSizeStyle(largeToken, 'lg')
    ];
};
const __TURBOPACK__default__export__ = genMultipleStyle;
}}),
"[project]/node_modules/antd/es/select/style/single.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>genSingleStyle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/util/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs-utils/es/util/statistic.js [app-client] (ecmascript) <export merge as mergeToken>");
;
;
;
function genSizeStyle(token, suffix) {
    const { componentCls, inputPaddingHorizontalBase, borderRadius } = token;
    const selectHeightWithoutBorder = token.calc(token.controlHeight).sub(token.calc(token.lineWidth).mul(2)).equal();
    const suffixCls = suffix ? `${componentCls}-${suffix}` : '';
    return {
        [`${componentCls}-single${suffixCls}`]: {
            fontSize: token.fontSize,
            height: token.controlHeight,
            // ========================= Selector =========================
            [`${componentCls}-selector`]: Object.assign(Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resetComponent"])(token, true)), {
                display: 'flex',
                borderRadius,
                flex: '1 1 auto',
                [`${componentCls}-selection-wrap:after`]: {
                    lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(selectHeightWithoutBorder)
                },
                [`${componentCls}-selection-search`]: {
                    position: 'absolute',
                    inset: 0,
                    width: '100%',
                    '&-input': {
                        width: '100%',
                        WebkitAppearance: 'textfield'
                    }
                },
                [`
          ${componentCls}-selection-item,
          ${componentCls}-selection-placeholder
        `]: {
                    display: 'block',
                    padding: 0,
                    lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(selectHeightWithoutBorder),
                    transition: `all ${token.motionDurationSlow}, visibility 0s`,
                    alignSelf: 'center'
                },
                [`${componentCls}-selection-placeholder`]: {
                    transition: 'none',
                    pointerEvents: 'none'
                },
                // For common baseline align
                [[
                    '&:after',
                    /* For '' value baseline align */ `${componentCls}-selection-item:empty:after`,
                    /* For undefined value baseline align */ `${componentCls}-selection-placeholder:empty:after`
                ].join(',')]: {
                    display: 'inline-block',
                    width: 0,
                    visibility: 'hidden',
                    content: '"\\a0"'
                }
            }),
            [`
        &${componentCls}-show-arrow ${componentCls}-selection-item,
        &${componentCls}-show-arrow ${componentCls}-selection-search,
        &${componentCls}-show-arrow ${componentCls}-selection-placeholder
      `]: {
                paddingInlineEnd: token.showArrowPaddingInlineEnd
            },
            // Opacity selection if open
            [`&${componentCls}-open ${componentCls}-selection-item`]: {
                color: token.colorTextPlaceholder
            },
            // ========================== Input ==========================
            // We only change the style of non-customize input which is only support by `combobox` mode.
            // Not customize
            [`&:not(${componentCls}-customize-input)`]: {
                [`${componentCls}-selector`]: {
                    width: '100%',
                    height: '100%',
                    alignItems: 'center',
                    padding: `0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(inputPaddingHorizontalBase)}`,
                    [`${componentCls}-selection-search-input`]: {
                        height: selectHeightWithoutBorder,
                        fontSize: token.fontSize
                    },
                    '&:after': {
                        lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(selectHeightWithoutBorder)
                    }
                }
            },
            [`&${componentCls}-customize-input`]: {
                [`${componentCls}-selector`]: {
                    '&:after': {
                        display: 'none'
                    },
                    [`${componentCls}-selection-search`]: {
                        position: 'static',
                        width: '100%'
                    },
                    [`${componentCls}-selection-placeholder`]: {
                        position: 'absolute',
                        insetInlineStart: 0,
                        insetInlineEnd: 0,
                        padding: `0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(inputPaddingHorizontalBase)}`,
                        '&:after': {
                            display: 'none'
                        }
                    }
                }
            }
        }
    };
}
function genSingleStyle(token) {
    const { componentCls } = token;
    const inputPaddingHorizontalSM = token.calc(token.controlPaddingHorizontalSM).sub(token.lineWidth).equal();
    return [
        genSizeStyle(token),
        // ======================== Small ========================
        // Shared
        genSizeStyle((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__["mergeToken"])(token, {
            controlHeight: token.controlHeightSM,
            borderRadius: token.borderRadiusSM
        }), 'sm'),
        // padding
        {
            [`${componentCls}-single${componentCls}-sm`]: {
                [`&:not(${componentCls}-customize-input)`]: {
                    [`${componentCls}-selector`]: {
                        padding: `0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(inputPaddingHorizontalSM)}`
                    },
                    // With arrow should provides `padding-right` to show the arrow
                    [`&${componentCls}-show-arrow ${componentCls}-selection-search`]: {
                        insetInlineEnd: token.calc(inputPaddingHorizontalSM).add(token.calc(token.fontSize).mul(1.5)).equal()
                    },
                    [`
            &${componentCls}-show-arrow ${componentCls}-selection-item,
            &${componentCls}-show-arrow ${componentCls}-selection-placeholder
          `]: {
                        paddingInlineEnd: token.calc(token.fontSize).mul(1.5).equal()
                    }
                }
            }
        },
        // ======================== Large ========================
        // Shared
        genSizeStyle((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__["mergeToken"])(token, {
            controlHeight: token.singleItemHeightLG,
            fontSize: token.fontSizeLG,
            borderRadius: token.borderRadiusLG
        }), 'lg')
    ];
}
}}),
"[project]/node_modules/antd/es/select/style/token.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prepareComponentToken": (()=>prepareComponentToken)
});
const prepareComponentToken = (token)=>{
    const { fontSize, lineHeight, lineWidth, controlHeight, controlHeightSM, controlHeightLG, paddingXXS, controlPaddingHorizontal, zIndexPopupBase, colorText, fontWeightStrong, controlItemBgActive, controlItemBgHover, colorBgContainer, colorFillSecondary, colorBgContainerDisabled, colorTextDisabled, colorPrimaryHover, colorPrimary, controlOutline } = token;
    // Item height default use `controlHeight - 2 * paddingXXS`,
    // but some case `paddingXXS=0`.
    // Let's fallback it.
    const dblPaddingXXS = paddingXXS * 2;
    const dblLineWidth = lineWidth * 2;
    const multipleItemHeight = Math.min(controlHeight - dblPaddingXXS, controlHeight - dblLineWidth);
    const multipleItemHeightSM = Math.min(controlHeightSM - dblPaddingXXS, controlHeightSM - dblLineWidth);
    const multipleItemHeightLG = Math.min(controlHeightLG - dblPaddingXXS, controlHeightLG - dblLineWidth);
    // FIXED_ITEM_MARGIN is a hardcode calculation since calc not support rounding
    const INTERNAL_FIXED_ITEM_MARGIN = Math.floor(paddingXXS / 2);
    return {
        INTERNAL_FIXED_ITEM_MARGIN,
        zIndexPopup: zIndexPopupBase + 50,
        optionSelectedColor: colorText,
        optionSelectedFontWeight: fontWeightStrong,
        optionSelectedBg: controlItemBgActive,
        optionActiveBg: controlItemBgHover,
        optionPadding: `${(controlHeight - fontSize * lineHeight) / 2}px ${controlPaddingHorizontal}px`,
        optionFontSize: fontSize,
        optionLineHeight: lineHeight,
        optionHeight: controlHeight,
        selectorBg: colorBgContainer,
        clearBg: colorBgContainer,
        singleItemHeightLG: controlHeightLG,
        multipleItemBg: colorFillSecondary,
        multipleItemBorderColor: 'transparent',
        multipleItemHeight,
        multipleItemHeightSM,
        multipleItemHeightLG,
        multipleSelectorBgDisabled: colorBgContainerDisabled,
        multipleItemColorDisabled: colorTextDisabled,
        multipleItemBorderColorDisabled: 'transparent',
        showArrowPaddingInlineEnd: Math.ceil(token.fontSize * 1.25),
        hoverBorderColor: colorPrimaryHover,
        activeBorderColor: colorPrimary,
        activeOutlineColor: controlOutline,
        selectAffixPadding: paddingXXS
    };
};
}}),
"[project]/node_modules/antd/es/select/style/variants.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/util/index.js [app-client] (ecmascript)");
;
// =====================================================
// ==                  Outlined                       ==
// =====================================================
const genBaseOutlinedStyle = (token, options)=>{
    const { componentCls, antCls, controlOutlineWidth } = token;
    return {
        [`&:not(${componentCls}-customize-input) ${componentCls}-selector`]: {
            border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.lineWidth)} ${token.lineType} ${options.borderColor}`,
            background: token.selectorBg
        },
        [`&:not(${componentCls}-disabled):not(${componentCls}-customize-input):not(${antCls}-pagination-size-changer)`]: {
            [`&:hover ${componentCls}-selector`]: {
                borderColor: options.hoverBorderHover
            },
            [`${componentCls}-focused& ${componentCls}-selector`]: {
                borderColor: options.activeBorderColor,
                boxShadow: `0 0 0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(controlOutlineWidth)} ${options.activeOutlineColor}`,
                outline: 0
            },
            [`${componentCls}-prefix`]: {
                color: options.color
            }
        }
    };
};
const genOutlinedStatusStyle = (token, options)=>({
        [`&${token.componentCls}-status-${options.status}`]: Object.assign({}, genBaseOutlinedStyle(token, options))
    });
const genOutlinedStyle = (token)=>({
        '&-outlined': Object.assign(Object.assign(Object.assign(Object.assign({}, genBaseOutlinedStyle(token, {
            borderColor: token.colorBorder,
            hoverBorderHover: token.hoverBorderColor,
            activeBorderColor: token.activeBorderColor,
            activeOutlineColor: token.activeOutlineColor,
            color: token.colorText
        })), genOutlinedStatusStyle(token, {
            status: 'error',
            borderColor: token.colorError,
            hoverBorderHover: token.colorErrorHover,
            activeBorderColor: token.colorError,
            activeOutlineColor: token.colorErrorOutline,
            color: token.colorError
        })), genOutlinedStatusStyle(token, {
            status: 'warning',
            borderColor: token.colorWarning,
            hoverBorderHover: token.colorWarningHover,
            activeBorderColor: token.colorWarning,
            activeOutlineColor: token.colorWarningOutline,
            color: token.colorWarning
        })), {
            [`&${token.componentCls}-disabled`]: {
                [`&:not(${token.componentCls}-customize-input) ${token.componentCls}-selector`]: {
                    background: token.colorBgContainerDisabled,
                    color: token.colorTextDisabled
                }
            },
            [`&${token.componentCls}-multiple ${token.componentCls}-selection-item`]: {
                background: token.multipleItemBg,
                border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.lineWidth)} ${token.lineType} ${token.multipleItemBorderColor}`
            }
        })
    });
// =====================================================
// ==                   Filled                        ==
// =====================================================
const genBaseFilledStyle = (token, options)=>{
    const { componentCls, antCls } = token;
    return {
        [`&:not(${componentCls}-customize-input) ${componentCls}-selector`]: {
            background: options.bg,
            border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.lineWidth)} ${token.lineType} transparent`,
            color: options.color
        },
        [`&:not(${componentCls}-disabled):not(${componentCls}-customize-input):not(${antCls}-pagination-size-changer)`]: {
            [`&:hover ${componentCls}-selector`]: {
                background: options.hoverBg
            },
            [`${componentCls}-focused& ${componentCls}-selector`]: {
                background: token.selectorBg,
                borderColor: options.activeBorderColor,
                outline: 0
            }
        }
    };
};
const genFilledStatusStyle = (token, options)=>({
        [`&${token.componentCls}-status-${options.status}`]: Object.assign({}, genBaseFilledStyle(token, options))
    });
const genFilledStyle = (token)=>({
        '&-filled': Object.assign(Object.assign(Object.assign(Object.assign({}, genBaseFilledStyle(token, {
            bg: token.colorFillTertiary,
            hoverBg: token.colorFillSecondary,
            activeBorderColor: token.activeBorderColor,
            color: token.colorText
        })), genFilledStatusStyle(token, {
            status: 'error',
            bg: token.colorErrorBg,
            hoverBg: token.colorErrorBgHover,
            activeBorderColor: token.colorError,
            color: token.colorError
        })), genFilledStatusStyle(token, {
            status: 'warning',
            bg: token.colorWarningBg,
            hoverBg: token.colorWarningBgHover,
            activeBorderColor: token.colorWarning,
            color: token.colorWarning
        })), {
            [`&${token.componentCls}-disabled`]: {
                [`&:not(${token.componentCls}-customize-input) ${token.componentCls}-selector`]: {
                    borderColor: token.colorBorder,
                    background: token.colorBgContainerDisabled,
                    color: token.colorTextDisabled
                }
            },
            [`&${token.componentCls}-multiple ${token.componentCls}-selection-item`]: {
                background: token.colorBgContainer,
                border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.lineWidth)} ${token.lineType} ${token.colorSplit}`
            }
        })
    });
// =====================================================
// ==                 Borderless                      ==
// =====================================================
const genBorderlessStyle = (token)=>({
        '&-borderless': {
            [`${token.componentCls}-selector`]: {
                background: 'transparent',
                border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.lineWidth)} ${token.lineType} transparent`
            },
            [`&${token.componentCls}-disabled`]: {
                [`&:not(${token.componentCls}-customize-input) ${token.componentCls}-selector`]: {
                    color: token.colorTextDisabled
                }
            },
            [`&${token.componentCls}-multiple ${token.componentCls}-selection-item`]: {
                background: token.multipleItemBg,
                border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.lineWidth)} ${token.lineType} ${token.multipleItemBorderColor}`
            },
            // Status
            [`&${token.componentCls}-status-error`]: {
                [`${token.componentCls}-prefix, ${token.componentCls}-selection-item`]: {
                    color: token.colorError
                }
            },
            [`&${token.componentCls}-status-warning`]: {
                [`${token.componentCls}-prefix, ${token.componentCls}-selection-item`]: {
                    color: token.colorWarning
                }
            }
        }
    });
// =====================================================
// ==                 Underlined                      ==
// =====================================================
const genBaseUnderlinedStyle = (token, options)=>{
    const { componentCls, antCls } = token;
    return {
        [`&:not(${componentCls}-customize-input) ${componentCls}-selector`]: {
            borderWidth: `0 0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.lineWidth)} 0`,
            borderStyle: `none none ${token.lineType} none`,
            borderColor: options.borderColor,
            background: token.selectorBg,
            borderRadius: 0
        },
        [`&:not(${componentCls}-disabled):not(${componentCls}-customize-input):not(${antCls}-pagination-size-changer)`]: {
            [`&:hover ${componentCls}-selector`]: {
                borderColor: options.hoverBorderHover
            },
            [`${componentCls}-focused& ${componentCls}-selector`]: {
                borderColor: options.activeBorderColor,
                outline: 0
            },
            [`${componentCls}-prefix`]: {
                color: options.color
            }
        }
    };
};
const genUnderlinedStatusStyle = (token, options)=>({
        [`&${token.componentCls}-status-${options.status}`]: Object.assign({}, genBaseUnderlinedStyle(token, options))
    });
const genUnderlinedStyle = (token)=>({
        '&-underlined': Object.assign(Object.assign(Object.assign(Object.assign({}, genBaseUnderlinedStyle(token, {
            borderColor: token.colorBorder,
            hoverBorderHover: token.hoverBorderColor,
            activeBorderColor: token.activeBorderColor,
            activeOutlineColor: token.activeOutlineColor,
            color: token.colorText
        })), genUnderlinedStatusStyle(token, {
            status: 'error',
            borderColor: token.colorError,
            hoverBorderHover: token.colorErrorHover,
            activeBorderColor: token.colorError,
            activeOutlineColor: token.colorErrorOutline,
            color: token.colorError
        })), genUnderlinedStatusStyle(token, {
            status: 'warning',
            borderColor: token.colorWarning,
            hoverBorderHover: token.colorWarningHover,
            activeBorderColor: token.colorWarning,
            activeOutlineColor: token.colorWarningOutline,
            color: token.colorWarning
        })), {
            [`&${token.componentCls}-disabled`]: {
                [`&:not(${token.componentCls}-customize-input) ${token.componentCls}-selector`]: {
                    color: token.colorTextDisabled
                }
            },
            [`&${token.componentCls}-multiple ${token.componentCls}-selection-item`]: {
                background: token.multipleItemBg,
                border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.lineWidth)} ${token.lineType} ${token.multipleItemBorderColor}`
            }
        })
    });
const genVariantsStyle = (token)=>({
        [token.componentCls]: Object.assign(Object.assign(Object.assign(Object.assign({}, genOutlinedStyle(token)), genFilledStyle(token)), genBorderlessStyle(token)), genUnderlinedStyle(token))
    });
const __TURBOPACK__default__export__ = genVariantsStyle;
}}),
"[project]/node_modules/antd/es/select/style/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$compact$2d$item$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/style/compact-item.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/util/genStyleUtils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs-utils/es/util/statistic.js [app-client] (ecmascript) <export merge as mergeToken>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$select$2f$style$2f$dropdown$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/select/style/dropdown.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$select$2f$style$2f$multiple$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/select/style/multiple.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$select$2f$style$2f$single$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/select/style/single.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$select$2f$style$2f$token$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/select/style/token.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$select$2f$style$2f$variants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/select/style/variants.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
// ============================= Selector =============================
const genSelectorStyle = (token)=>{
    const { componentCls } = token;
    return {
        position: 'relative',
        transition: `all ${token.motionDurationMid} ${token.motionEaseInOut}`,
        input: {
            cursor: 'pointer'
        },
        [`${componentCls}-show-search&`]: {
            cursor: 'text',
            input: {
                cursor: 'auto',
                color: 'inherit',
                height: '100%'
            }
        },
        [`${componentCls}-disabled&`]: {
            cursor: 'not-allowed',
            input: {
                cursor: 'not-allowed'
            }
        }
    };
};
// ============================== Styles ==============================
// /* Reset search input style */
const getSearchInputWithoutBorderStyle = (token)=>{
    const { componentCls } = token;
    return {
        [`${componentCls}-selection-search-input`]: {
            margin: 0,
            padding: 0,
            background: 'transparent',
            border: 'none',
            outline: 'none',
            appearance: 'none',
            fontFamily: 'inherit',
            '&::-webkit-search-cancel-button': {
                display: 'none',
                appearance: 'none'
            }
        }
    };
};
// =============================== Base ===============================
const genBaseStyle = (token)=>{
    const { antCls, componentCls, inputPaddingHorizontalBase, iconCls } = token;
    const hoverShowClearStyle = {
        [`${componentCls}-clear`]: {
            opacity: 1,
            background: token.colorBgBase,
            borderRadius: '50%'
        }
    };
    return {
        [componentCls]: Object.assign(Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resetComponent"])(token)), {
            position: 'relative',
            display: 'inline-flex',
            cursor: 'pointer',
            [`&:not(${componentCls}-customize-input) ${componentCls}-selector`]: Object.assign(Object.assign({}, genSelectorStyle(token)), getSearchInputWithoutBorderStyle(token)),
            // ======================== Selection ========================
            [`${componentCls}-selection-item`]: Object.assign(Object.assign({
                flex: 1,
                fontWeight: 'normal',
                position: 'relative',
                userSelect: 'none'
            }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["textEllipsis"]), {
                // https://github.com/ant-design/ant-design/issues/40421
                [`> ${antCls}-typography`]: {
                    display: 'inline'
                }
            }),
            // ======================= Placeholder =======================
            [`${componentCls}-selection-placeholder`]: Object.assign(Object.assign({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["textEllipsis"]), {
                flex: 1,
                color: token.colorTextPlaceholder,
                pointerEvents: 'none'
            }),
            // ========================== Arrow ==========================
            [`${componentCls}-arrow`]: Object.assign(Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resetIcon"])()), {
                position: 'absolute',
                top: '50%',
                insetInlineStart: 'auto',
                insetInlineEnd: inputPaddingHorizontalBase,
                height: token.fontSizeIcon,
                marginTop: token.calc(token.fontSizeIcon).mul(-1).div(2).equal(),
                color: token.colorTextQuaternary,
                fontSize: token.fontSizeIcon,
                lineHeight: 1,
                textAlign: 'center',
                pointerEvents: 'none',
                display: 'flex',
                alignItems: 'center',
                transition: `opacity ${token.motionDurationSlow} ease`,
                [iconCls]: {
                    verticalAlign: 'top',
                    transition: `transform ${token.motionDurationSlow}`,
                    '> svg': {
                        verticalAlign: 'top'
                    },
                    [`&:not(${componentCls}-suffix)`]: {
                        pointerEvents: 'auto'
                    }
                },
                [`${componentCls}-disabled &`]: {
                    cursor: 'not-allowed'
                },
                '> *:not(:last-child)': {
                    marginInlineEnd: 8 // FIXME: magic
                }
            }),
            // ========================== Wrap ===========================
            [`${componentCls}-selection-wrap`]: {
                display: 'flex',
                width: '100%',
                position: 'relative',
                minWidth: 0,
                // https://github.com/ant-design/ant-design/issues/51669
                '&:after': {
                    content: '"\\a0"',
                    width: 0,
                    overflow: 'hidden'
                }
            },
            // ========================= Prefix ==========================
            [`${componentCls}-prefix`]: {
                flex: 'none',
                marginInlineEnd: token.selectAffixPadding
            },
            // ========================== Clear ==========================
            [`${componentCls}-clear`]: {
                position: 'absolute',
                top: '50%',
                insetInlineStart: 'auto',
                insetInlineEnd: inputPaddingHorizontalBase,
                zIndex: 1,
                display: 'inline-block',
                width: token.fontSizeIcon,
                height: token.fontSizeIcon,
                marginTop: token.calc(token.fontSizeIcon).mul(-1).div(2).equal(),
                color: token.colorTextQuaternary,
                fontSize: token.fontSizeIcon,
                fontStyle: 'normal',
                lineHeight: 1,
                textAlign: 'center',
                textTransform: 'none',
                cursor: 'pointer',
                opacity: 0,
                transition: `color ${token.motionDurationMid} ease, opacity ${token.motionDurationSlow} ease`,
                textRendering: 'auto',
                '&:before': {
                    display: 'block'
                },
                '&:hover': {
                    color: token.colorIcon
                }
            },
            '@media(hover:none)': hoverShowClearStyle,
            '&:hover': hoverShowClearStyle
        }),
        // ========================= Feedback ==========================
        [`${componentCls}-status`]: {
            '&-error, &-warning, &-success, &-validating': {
                [`&${componentCls}-has-feedback`]: {
                    [`${componentCls}-clear`]: {
                        insetInlineEnd: token.calc(inputPaddingHorizontalBase).add(token.fontSize).add(token.paddingXS).equal()
                    }
                }
            }
        }
    };
};
// ============================== Styles ==============================
const genSelectStyle = (token)=>{
    const { componentCls } = token;
    return [
        {
            [componentCls]: {
                // ==================== In Form ====================
                [`&${componentCls}-in-form-item`]: {
                    width: '100%'
                }
            }
        },
        // =====================================================
        // ==                       LTR                       ==
        // =====================================================
        // Base
        genBaseStyle(token),
        // Single
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$select$2f$style$2f$single$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(token),
        // Multiple
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$select$2f$style$2f$multiple$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(token),
        // Dropdown
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$select$2f$style$2f$dropdown$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(token),
        // =====================================================
        // ==                       RTL                       ==
        // =====================================================
        {
            [`${componentCls}-rtl`]: {
                direction: 'rtl'
            }
        },
        // =====================================================
        // ==             Space Compact                       ==
        // =====================================================
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$compact$2d$item$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genCompactItemStyle"])(token, {
            borderElCls: `${componentCls}-selector`,
            focusElCls: `${componentCls}-focused`
        })
    ];
};
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genStyleHooks"])('Select', (token, _ref)=>{
    let { rootPrefixCls } = _ref;
    const selectToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__["mergeToken"])(token, {
        rootPrefixCls,
        inputPaddingHorizontalBase: token.calc(token.paddingSM).sub(1).equal(),
        multipleSelectItemHeight: token.multipleItemHeight,
        selectHeight: token.controlHeight
    });
    return [
        genSelectStyle(selectToken),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$select$2f$style$2f$variants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(selectToken)
    ];
}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$select$2f$style$2f$token$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["prepareComponentToken"], {
    unitless: {
        optionLineHeight: true,
        optionSelectedFontWeight: true
    }
});
}}),
"[project]/node_modules/antd/es/select/useIcons.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useIcons)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CheckOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/CheckOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CloseCircleFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/CloseCircleFilled.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CloseOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/CloseOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$DownOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/DownOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$LoadingOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/LoadingOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$SearchOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/SearchOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/warning.js [app-client] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
function useIcons(_ref) {
    let { suffixIcon, clearIcon, menuItemSelectedIcon, removeIcon, loading, multiple, hasFeedback, prefixCls, showSuffixIcon, feedbackIcon, showArrow, componentName } = _ref;
    if ("TURBOPACK compile-time truthy", 1) {
        const warning = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["devUseWarning"])(componentName);
        warning.deprecated(!clearIcon, 'clearIcon', 'allowClear={{ clearIcon: React.ReactNode }}');
    }
    // Clear Icon
    const mergedClearIcon = clearIcon !== null && clearIcon !== void 0 ? clearIcon : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CloseCircleFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null);
    // Validation Feedback Icon
    const getSuffixIconNode = (arrowIcon)=>{
        if (suffixIcon === null && !hasFeedback && !showArrow) {
            return null;
        }
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, showSuffixIcon !== false && arrowIcon, hasFeedback && feedbackIcon);
    };
    // Arrow item icon
    let mergedSuffixIcon = null;
    if (suffixIcon !== undefined) {
        mergedSuffixIcon = getSuffixIconNode(suffixIcon);
    } else if (loading) {
        mergedSuffixIcon = getSuffixIconNode(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$LoadingOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            spin: true
        }));
    } else {
        const iconCls = `${prefixCls}-suffix`;
        mergedSuffixIcon = (_ref2)=>{
            let { open, showSearch } = _ref2;
            if (open && showSearch) {
                return getSuffixIconNode(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$SearchOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    className: iconCls
                }));
            }
            return getSuffixIconNode(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$DownOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                className: iconCls
            }));
        };
    }
    // Checked item icon
    let mergedItemIcon = null;
    if (menuItemSelectedIcon !== undefined) {
        mergedItemIcon = menuItemSelectedIcon;
    } else if (multiple) {
        mergedItemIcon = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CheckOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null);
    } else {
        mergedItemIcon = null;
    }
    let mergedRemoveIcon = null;
    if (removeIcon !== undefined) {
        mergedRemoveIcon = removeIcon;
    } else {
        mergedRemoveIcon = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CloseOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null);
    }
    return {
        clearIcon: mergedClearIcon,
        suffixIcon: mergedSuffixIcon,
        itemIcon: mergedItemIcon,
        removeIcon: mergedRemoveIcon
    };
}
}}),
"[project]/node_modules/antd/es/select/useShowArrow.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Since Select, TreeSelect, Cascader is same Select like component.
 * We just use same hook to handle this logic.
 *
 * If `suffixIcon` is not equal to `null`, always show it.
 */ __turbopack_context__.s({
    "default": (()=>useShowArrow)
});
function useShowArrow(suffixIcon, showArrow) {
    return showArrow !== undefined ? showArrow : suffixIcon !== null;
}
}}),
"[project]/node_modules/antd/es/select/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
// TODO: 4.0 - codemod should help to change `filterOption` to support node props.
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$select$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-select/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$select$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-select/es/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$select$2f$es$2f$OptGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__OptGroup$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-select/es/OptGroup.js [app-client] (ecmascript) <export default as OptGroup>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$select$2f$es$2f$Option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Option$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-select/es/Option.js [app-client] (ecmascript) <export default as Option>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$omit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/omit.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$hooks$2f$useZIndex$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/hooks/useZIndex.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$motion$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/motion.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$PurePanel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/PurePanel.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$statusUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/statusUtils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$defaultRenderEmpty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/defaultRenderEmpty.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$DisabledContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/DisabledContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useCSSVarCls$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/hooks/useCSSVarCls.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useSize$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/hooks/useSize.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useVariants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/hooks/useVariants.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$space$2f$Compact$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/space/Compact.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$useToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useToken$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/useToken.js [app-client] (ecmascript) <export default as useToken>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$select$2f$mergedBuiltinPlacements$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/select/mergedBuiltinPlacements.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$select$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/select/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$select$2f$useIcons$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/select/useIcons.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$select$2f$useShowArrow$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/select/useShowArrow.js [app-client] (ecmascript)");
"use client";
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const SECRET_COMBOBOX_MODE_DO_NOT_USE = 'SECRET_COMBOBOX_MODE_DO_NOT_USE';
const InternalSelect = (props, ref)=>{
    var _a;
    const { prefixCls: customizePrefixCls, bordered, className, rootClassName, getPopupContainer, popupClassName, dropdownClassName, listHeight = 256, placement, listItemHeight: customListItemHeight, size: customizeSize, disabled: customDisabled, notFoundContent, status: customStatus, builtinPlacements, dropdownMatchSelectWidth, popupMatchSelectWidth, direction: propDirection, style, allowClear, variant: customizeVariant, dropdownStyle, transitionName, tagRender, maxCount, prefix } = props, rest = __rest(props, [
        "prefixCls",
        "bordered",
        "className",
        "rootClassName",
        "getPopupContainer",
        "popupClassName",
        "dropdownClassName",
        "listHeight",
        "placement",
        "listItemHeight",
        "size",
        "disabled",
        "notFoundContent",
        "status",
        "builtinPlacements",
        "dropdownMatchSelectWidth",
        "popupMatchSelectWidth",
        "direction",
        "style",
        "allowClear",
        "variant",
        "dropdownStyle",
        "transitionName",
        "tagRender",
        "maxCount",
        "prefix"
    ]);
    const { getPopupContainer: getContextPopupContainer, getPrefixCls, renderEmpty, direction: contextDirection, virtual, popupMatchSelectWidth: contextPopupMatchSelectWidth, popupOverflow } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfigContext"]);
    const contextSelect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useComponentConfig"])('select');
    const [, token] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$useToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useToken$3e$__["useToken"])();
    const listItemHeight = customListItemHeight !== null && customListItemHeight !== void 0 ? customListItemHeight : token === null || token === void 0 ? void 0 : token.controlHeight;
    const prefixCls = getPrefixCls('select', customizePrefixCls);
    const rootPrefixCls = getPrefixCls();
    const direction = propDirection !== null && propDirection !== void 0 ? propDirection : contextDirection;
    const { compactSize, compactItemClassnames } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$space$2f$Compact$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCompactItemContext"])(prefixCls, direction);
    const [variant, enableVariantCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useVariants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])('select', customizeVariant, bordered);
    const rootCls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useCSSVarCls$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls);
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$select$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, rootCls);
    const mode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "InternalSelect.useMemo[mode]": ()=>{
            const { mode: m } = props;
            if (m === 'combobox') {
                return undefined;
            }
            if (m === SECRET_COMBOBOX_MODE_DO_NOT_USE) {
                return 'combobox';
            }
            return m;
        }
    }["InternalSelect.useMemo[mode]"], [
        props.mode
    ]);
    const isMultiple = mode === 'multiple' || mode === 'tags';
    const showSuffixIcon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$select$2f$useShowArrow$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props.suffixIcon, props.showArrow);
    const mergedPopupMatchSelectWidth = (_a = popupMatchSelectWidth !== null && popupMatchSelectWidth !== void 0 ? popupMatchSelectWidth : dropdownMatchSelectWidth) !== null && _a !== void 0 ? _a : contextPopupMatchSelectWidth;
    // ===================== Form Status =====================
    const { status: contextStatus, hasFeedback, isFormItemInput, feedbackIcon } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormItemInputContext"]);
    const mergedStatus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$statusUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getMergedStatus"])(contextStatus, customStatus);
    // ===================== Empty =====================
    let mergedNotFound;
    if (notFoundContent !== undefined) {
        mergedNotFound = notFoundContent;
    } else if (mode === 'combobox') {
        mergedNotFound = null;
    } else {
        mergedNotFound = (renderEmpty === null || renderEmpty === void 0 ? void 0 : renderEmpty('Select')) || /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$defaultRenderEmpty$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            componentName: "Select"
        });
    }
    // ===================== Icons =====================
    const { suffixIcon, itemIcon, removeIcon, clearIcon } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$select$2f$useIcons$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(Object.assign(Object.assign({}, rest), {
        multiple: isMultiple,
        hasFeedback,
        feedbackIcon,
        showSuffixIcon,
        prefixCls,
        componentName: 'Select'
    }));
    const mergedAllowClear = allowClear === true ? {
        clearIcon
    } : allowClear;
    const selectProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$omit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(rest, [
        'suffixIcon',
        'itemIcon'
    ]);
    const mergedPopupClassName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(popupClassName || dropdownClassName, {
        [`${prefixCls}-dropdown-${direction}`]: direction === 'rtl'
    }, rootClassName, cssVarCls, rootCls, hashId);
    const mergedSize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useSize$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        "InternalSelect.useSize[mergedSize]": (ctx)=>{
            var _a;
            return (_a = customizeSize !== null && customizeSize !== void 0 ? customizeSize : compactSize) !== null && _a !== void 0 ? _a : ctx;
        }
    }["InternalSelect.useSize[mergedSize]"]);
    // ===================== Disabled =====================
    const disabled = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$DisabledContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
    const mergedDisabled = customDisabled !== null && customDisabled !== void 0 ? customDisabled : disabled;
    const mergedClassName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        [`${prefixCls}-lg`]: mergedSize === 'large',
        [`${prefixCls}-sm`]: mergedSize === 'small',
        [`${prefixCls}-rtl`]: direction === 'rtl',
        [`${prefixCls}-${variant}`]: enableVariantCls,
        [`${prefixCls}-in-form-item`]: isFormItemInput
    }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$statusUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getStatusClassNames"])(prefixCls, mergedStatus, hasFeedback), compactItemClassnames, contextSelect.className, className, rootClassName, cssVarCls, rootCls, hashId);
    // ===================== Placement =====================
    const memoPlacement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "InternalSelect.useMemo[memoPlacement]": ()=>{
            if (placement !== undefined) {
                return placement;
            }
            return direction === 'rtl' ? 'bottomRight' : 'bottomLeft';
        }
    }["InternalSelect.useMemo[memoPlacement]"], [
        placement,
        direction
    ]);
    // ====================== Warning ======================
    if ("TURBOPACK compile-time truthy", 1) {
        const warning = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["devUseWarning"])('Select');
        warning.deprecated(!dropdownClassName, 'dropdownClassName', 'popupClassName');
        warning.deprecated(dropdownMatchSelectWidth === undefined, 'dropdownMatchSelectWidth', 'popupMatchSelectWidth');
        ("TURBOPACK compile-time truthy", 1) ? warning(!('showArrow' in props), 'deprecated', '`showArrow` is deprecated which will be removed in next major version. It will be a default behavior, you can hide it by setting `suffixIcon` to null.') : ("TURBOPACK unreachable", undefined);
        warning.deprecated(!('bordered' in props), 'bordered', 'variant');
        ("TURBOPACK compile-time truthy", 1) ? warning(!(typeof maxCount !== 'undefined' && !isMultiple), 'usage', '`maxCount` only works with mode `multiple` or `tags`') : ("TURBOPACK unreachable", undefined);
    }
    // ====================== zIndex =========================
    const [zIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$hooks$2f$useZIndex$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useZIndex"])('SelectLike', dropdownStyle === null || dropdownStyle === void 0 ? void 0 : dropdownStyle.zIndex);
    // ====================== Render =======================
    return wrapCSSVar(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$select$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"], Object.assign({
        ref: ref,
        virtual: virtual,
        showSearch: contextSelect.showSearch
    }, selectProps, {
        style: Object.assign(Object.assign({}, contextSelect.style), style),
        dropdownMatchSelectWidth: mergedPopupMatchSelectWidth,
        transitionName: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$motion$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTransitionName"])(rootPrefixCls, 'slide-up', transitionName),
        builtinPlacements: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$select$2f$mergedBuiltinPlacements$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(builtinPlacements, popupOverflow),
        listHeight: listHeight,
        listItemHeight: listItemHeight,
        mode: mode,
        prefixCls: prefixCls,
        placement: memoPlacement,
        direction: direction,
        prefix: prefix,
        suffixIcon: suffixIcon,
        menuItemSelectedIcon: itemIcon,
        removeIcon: removeIcon,
        allowClear: mergedAllowClear,
        notFoundContent: mergedNotFound,
        className: mergedClassName,
        getPopupContainer: getPopupContainer || getContextPopupContainer,
        dropdownClassName: mergedPopupClassName,
        disabled: mergedDisabled,
        dropdownStyle: Object.assign(Object.assign({}, dropdownStyle), {
            zIndex
        }),
        maxCount: isMultiple ? maxCount : undefined,
        tagRender: isMultiple ? tagRender : undefined
    })));
};
if ("TURBOPACK compile-time truthy", 1) {
    InternalSelect.displayName = 'Select';
}
const Select = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(InternalSelect);
// We don't care debug panel
/* istanbul ignore next */ const PurePanel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$PurePanel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(Select, 'dropdownAlign');
Select.SECRET_COMBOBOX_MODE_DO_NOT_USE = SECRET_COMBOBOX_MODE_DO_NOT_USE;
Select.Option = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$select$2f$es$2f$Option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Option$3e$__["Option"];
Select.OptGroup = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$select$2f$es$2f$OptGroup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__OptGroup$3e$__["OptGroup"];
Select._InternalPanelDoNotUseOrYouWillBeFired = PurePanel;
if ("TURBOPACK compile-time truthy", 1) {
    Select.displayName = 'Select';
}
const __TURBOPACK__default__export__ = Select;
}}),
"[project]/node_modules/antd/es/pagination/style/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "prepareComponentToken": (()=>prepareComponentToken),
    "prepareToken": (()=>prepareToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/util/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$input$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/input/style/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$input$2f$style$2f$token$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/input/style/token.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$input$2f$style$2f$variants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/input/style/variants.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/util/genStyleUtils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs-utils/es/util/statistic.js [app-client] (ecmascript) <export merge as mergeToken>");
;
;
;
;
;
const genPaginationDisabledStyle = (token)=>{
    const { componentCls } = token;
    return {
        [`${componentCls}-disabled`]: {
            '&, &:hover': {
                cursor: 'not-allowed',
                [`${componentCls}-item-link`]: {
                    color: token.colorTextDisabled,
                    cursor: 'not-allowed'
                }
            },
            '&:focus-visible': {
                cursor: 'not-allowed',
                [`${componentCls}-item-link`]: {
                    color: token.colorTextDisabled,
                    cursor: 'not-allowed'
                }
            }
        },
        [`&${componentCls}-disabled`]: {
            cursor: 'not-allowed',
            [`${componentCls}-item`]: {
                cursor: 'not-allowed',
                backgroundColor: 'transparent',
                '&:hover, &:active': {
                    backgroundColor: 'transparent'
                },
                a: {
                    color: token.colorTextDisabled,
                    backgroundColor: 'transparent',
                    border: 'none',
                    cursor: 'not-allowed'
                },
                '&-active': {
                    borderColor: token.colorBorder,
                    backgroundColor: token.itemActiveBgDisabled,
                    '&:hover, &:active': {
                        backgroundColor: token.itemActiveBgDisabled
                    },
                    a: {
                        color: token.itemActiveColorDisabled
                    }
                }
            },
            [`${componentCls}-item-link`]: {
                color: token.colorTextDisabled,
                cursor: 'not-allowed',
                '&:hover, &:active': {
                    backgroundColor: 'transparent'
                },
                [`${componentCls}-simple&`]: {
                    backgroundColor: 'transparent',
                    '&:hover, &:active': {
                        backgroundColor: 'transparent'
                    }
                }
            },
            [`${componentCls}-simple-pager`]: {
                color: token.colorTextDisabled
            },
            [`${componentCls}-jump-prev, ${componentCls}-jump-next`]: {
                [`${componentCls}-item-link-icon`]: {
                    opacity: 0
                },
                [`${componentCls}-item-ellipsis`]: {
                    opacity: 1
                }
            }
        },
        [`&${componentCls}-simple`]: {
            [`${componentCls}-prev, ${componentCls}-next`]: {
                [`&${componentCls}-disabled ${componentCls}-item-link`]: {
                    '&:hover, &:active': {
                        backgroundColor: 'transparent'
                    }
                }
            }
        }
    };
};
const genPaginationMiniStyle = (token)=>{
    const { componentCls } = token;
    return {
        [`&${componentCls}-mini ${componentCls}-total-text, &${componentCls}-mini ${componentCls}-simple-pager`]: {
            height: token.itemSizeSM,
            lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.itemSizeSM)
        },
        [`&${componentCls}-mini ${componentCls}-item`]: {
            minWidth: token.itemSizeSM,
            height: token.itemSizeSM,
            margin: 0,
            lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.calc(token.itemSizeSM).sub(2).equal())
        },
        [`&${componentCls}-mini ${componentCls}-prev, &${componentCls}-mini ${componentCls}-next`]: {
            minWidth: token.itemSizeSM,
            height: token.itemSizeSM,
            margin: 0,
            lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.itemSizeSM)
        },
        [`&${componentCls}-mini:not(${componentCls}-disabled)`]: {
            [`${componentCls}-prev, ${componentCls}-next`]: {
                [`&:hover ${componentCls}-item-link`]: {
                    backgroundColor: token.colorBgTextHover
                },
                [`&:active ${componentCls}-item-link`]: {
                    backgroundColor: token.colorBgTextActive
                },
                [`&${componentCls}-disabled:hover ${componentCls}-item-link`]: {
                    backgroundColor: 'transparent'
                }
            }
        },
        [`
    &${componentCls}-mini ${componentCls}-prev ${componentCls}-item-link,
    &${componentCls}-mini ${componentCls}-next ${componentCls}-item-link
    `]: {
            backgroundColor: 'transparent',
            borderColor: 'transparent',
            '&::after': {
                height: token.itemSizeSM,
                lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.itemSizeSM)
            }
        },
        [`&${componentCls}-mini ${componentCls}-jump-prev, &${componentCls}-mini ${componentCls}-jump-next`]: {
            height: token.itemSizeSM,
            marginInlineEnd: 0,
            lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.itemSizeSM)
        },
        [`&${componentCls}-mini ${componentCls}-options`]: {
            marginInlineStart: token.paginationMiniOptionsMarginInlineStart,
            '&-size-changer': {
                top: token.miniOptionsSizeChangerTop
            },
            '&-quick-jumper': {
                height: token.itemSizeSM,
                lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.itemSizeSM),
                input: Object.assign(Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$input$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["genInputSmallStyle"])(token)), {
                    width: token.paginationMiniQuickJumperInputWidth,
                    height: token.controlHeightSM
                })
            }
        }
    };
};
const genPaginationSimpleStyle = (token)=>{
    const { componentCls } = token;
    return {
        [`
    &${componentCls}-simple ${componentCls}-prev,
    &${componentCls}-simple ${componentCls}-next
    `]: {
            height: token.itemSizeSM,
            lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.itemSizeSM),
            verticalAlign: 'top',
            [`${componentCls}-item-link`]: {
                height: token.itemSizeSM,
                backgroundColor: 'transparent',
                border: 0,
                '&:hover': {
                    backgroundColor: token.colorBgTextHover
                },
                '&:active': {
                    backgroundColor: token.colorBgTextActive
                },
                '&::after': {
                    height: token.itemSizeSM,
                    lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.itemSizeSM)
                }
            }
        },
        [`&${componentCls}-simple ${componentCls}-simple-pager`]: {
            display: 'inline-block',
            height: token.itemSizeSM,
            marginInlineEnd: token.marginXS,
            input: {
                boxSizing: 'border-box',
                height: '100%',
                padding: `0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.paginationItemPaddingInline)}`,
                textAlign: 'center',
                backgroundColor: token.itemInputBg,
                border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.lineWidth)} ${token.lineType} ${token.colorBorder}`,
                borderRadius: token.borderRadius,
                outline: 'none',
                transition: `border-color ${token.motionDurationMid}`,
                color: 'inherit',
                '&:hover': {
                    borderColor: token.colorPrimary
                },
                '&:focus': {
                    borderColor: token.colorPrimaryHover,
                    boxShadow: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.inputOutlineOffset)} 0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.controlOutlineWidth)} ${token.controlOutline}`
                },
                '&[disabled]': {
                    color: token.colorTextDisabled,
                    backgroundColor: token.colorBgContainerDisabled,
                    borderColor: token.colorBorder,
                    cursor: 'not-allowed'
                }
            }
        }
    };
};
const genPaginationJumpStyle = (token)=>{
    const { componentCls } = token;
    return {
        [`${componentCls}-jump-prev, ${componentCls}-jump-next`]: {
            outline: 0,
            [`${componentCls}-item-container`]: {
                position: 'relative',
                [`${componentCls}-item-link-icon`]: {
                    color: token.colorPrimary,
                    fontSize: token.fontSizeSM,
                    opacity: 0,
                    transition: `all ${token.motionDurationMid}`,
                    '&-svg': {
                        top: 0,
                        insetInlineEnd: 0,
                        bottom: 0,
                        insetInlineStart: 0,
                        margin: 'auto'
                    }
                },
                [`${componentCls}-item-ellipsis`]: {
                    position: 'absolute',
                    top: 0,
                    insetInlineEnd: 0,
                    bottom: 0,
                    insetInlineStart: 0,
                    display: 'block',
                    margin: 'auto',
                    color: token.colorTextDisabled,
                    letterSpacing: token.paginationEllipsisLetterSpacing,
                    textAlign: 'center',
                    textIndent: token.paginationEllipsisTextIndent,
                    opacity: 1,
                    transition: `all ${token.motionDurationMid}`
                }
            },
            '&:hover': {
                [`${componentCls}-item-link-icon`]: {
                    opacity: 1
                },
                [`${componentCls}-item-ellipsis`]: {
                    opacity: 0
                }
            }
        },
        [`
    ${componentCls}-prev,
    ${componentCls}-jump-prev,
    ${componentCls}-jump-next
    `]: {
            marginInlineEnd: token.marginXS
        },
        [`
    ${componentCls}-prev,
    ${componentCls}-next,
    ${componentCls}-jump-prev,
    ${componentCls}-jump-next
    `]: {
            display: 'inline-block',
            minWidth: token.itemSize,
            height: token.itemSize,
            color: token.colorText,
            fontFamily: token.fontFamily,
            lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.itemSize),
            textAlign: 'center',
            verticalAlign: 'middle',
            listStyle: 'none',
            borderRadius: token.borderRadius,
            cursor: 'pointer',
            transition: `all ${token.motionDurationMid}`
        },
        [`${componentCls}-prev, ${componentCls}-next`]: {
            outline: 0,
            button: {
                color: token.colorText,
                cursor: 'pointer',
                userSelect: 'none'
            },
            [`${componentCls}-item-link`]: {
                display: 'block',
                width: '100%',
                height: '100%',
                padding: 0,
                fontSize: token.fontSizeSM,
                textAlign: 'center',
                backgroundColor: 'transparent',
                border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.lineWidth)} ${token.lineType} transparent`,
                borderRadius: token.borderRadius,
                outline: 'none',
                transition: `all ${token.motionDurationMid}`
            },
            [`&:hover ${componentCls}-item-link`]: {
                backgroundColor: token.colorBgTextHover
            },
            [`&:active ${componentCls}-item-link`]: {
                backgroundColor: token.colorBgTextActive
            },
            [`&${componentCls}-disabled:hover`]: {
                [`${componentCls}-item-link`]: {
                    backgroundColor: 'transparent'
                }
            }
        },
        [`${componentCls}-slash`]: {
            marginInlineEnd: token.paginationSlashMarginInlineEnd,
            marginInlineStart: token.paginationSlashMarginInlineStart
        },
        [`${componentCls}-options`]: {
            display: 'inline-block',
            marginInlineStart: token.margin,
            verticalAlign: 'middle',
            '&-size-changer': {
                display: 'inline-block',
                width: 'auto'
            },
            '&-quick-jumper': {
                display: 'inline-block',
                height: token.controlHeight,
                marginInlineStart: token.marginXS,
                lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.controlHeight),
                verticalAlign: 'top',
                input: Object.assign(Object.assign(Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$input$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["genBasicInputStyle"])(token)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$input$2f$style$2f$variants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genBaseOutlinedStyle"])(token, {
                    borderColor: token.colorBorder,
                    hoverBorderColor: token.colorPrimaryHover,
                    activeBorderColor: token.colorPrimary,
                    activeShadow: token.activeShadow
                })), {
                    '&[disabled]': Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$input$2f$style$2f$variants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genDisabledStyle"])(token)),
                    width: token.calc(token.controlHeightLG).mul(1.25).equal(),
                    height: token.controlHeight,
                    boxSizing: 'border-box',
                    margin: 0,
                    marginInlineStart: token.marginXS,
                    marginInlineEnd: token.marginXS
                })
            }
        }
    };
};
const genPaginationItemStyle = (token)=>{
    const { componentCls } = token;
    return {
        [`${componentCls}-item`]: {
            display: 'inline-block',
            minWidth: token.itemSize,
            height: token.itemSize,
            marginInlineEnd: token.marginXS,
            fontFamily: token.fontFamily,
            lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.calc(token.itemSize).sub(2).equal()),
            textAlign: 'center',
            verticalAlign: 'middle',
            listStyle: 'none',
            backgroundColor: token.itemBg,
            border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.lineWidth)} ${token.lineType} transparent`,
            borderRadius: token.borderRadius,
            outline: 0,
            cursor: 'pointer',
            userSelect: 'none',
            a: {
                display: 'block',
                padding: `0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.paginationItemPaddingInline)}`,
                color: token.colorText,
                '&:hover': {
                    textDecoration: 'none'
                }
            },
            [`&:not(${componentCls}-item-active)`]: {
                '&:hover': {
                    transition: `all ${token.motionDurationMid}`,
                    backgroundColor: token.colorBgTextHover
                },
                '&:active': {
                    backgroundColor: token.colorBgTextActive
                }
            },
            '&-active': {
                fontWeight: token.fontWeightStrong,
                backgroundColor: token.itemActiveBg,
                borderColor: token.colorPrimary,
                a: {
                    color: token.colorPrimary
                },
                '&:hover': {
                    borderColor: token.colorPrimaryHover
                },
                '&:hover a': {
                    color: token.colorPrimaryHover
                }
            }
        }
    };
};
const genPaginationStyle = (token)=>{
    const { componentCls } = token;
    return {
        [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resetComponent"])(token)), {
            display: 'flex',
            '&-start': {
                justifyContent: 'start'
            },
            '&-center': {
                justifyContent: 'center'
            },
            '&-end': {
                justifyContent: 'end'
            },
            'ul, ol': {
                margin: 0,
                padding: 0,
                listStyle: 'none'
            },
            '&::after': {
                display: 'block',
                clear: 'both',
                height: 0,
                overflow: 'hidden',
                visibility: 'hidden',
                content: '""'
            },
            [`${componentCls}-total-text`]: {
                display: 'inline-block',
                height: token.itemSize,
                marginInlineEnd: token.marginXS,
                lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.calc(token.itemSize).sub(2).equal()),
                verticalAlign: 'middle'
            }
        }), genPaginationItemStyle(token)), genPaginationJumpStyle(token)), genPaginationSimpleStyle(token)), genPaginationMiniStyle(token)), genPaginationDisabledStyle(token)), {
            // media query style
            [`@media only screen and (max-width: ${token.screenLG}px)`]: {
                [`${componentCls}-item`]: {
                    '&-after-jump-prev, &-before-jump-next': {
                        display: 'none'
                    }
                }
            },
            [`@media only screen and (max-width: ${token.screenSM}px)`]: {
                [`${componentCls}-options`]: {
                    display: 'none'
                }
            }
        }),
        // rtl style
        [`&${token.componentCls}-rtl`]: {
            direction: 'rtl'
        }
    };
};
const genPaginationFocusStyle = (token)=>{
    const { componentCls } = token;
    return {
        [`${componentCls}:not(${componentCls}-disabled)`]: {
            [`${componentCls}-item`]: Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genFocusStyle"])(token)),
            [`${componentCls}-jump-prev, ${componentCls}-jump-next`]: {
                '&:focus-visible': Object.assign({
                    [`${componentCls}-item-link-icon`]: {
                        opacity: 1
                    },
                    [`${componentCls}-item-ellipsis`]: {
                        opacity: 0
                    }
                }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genFocusOutline"])(token))
            },
            [`${componentCls}-prev, ${componentCls}-next`]: {
                [`&:focus-visible ${componentCls}-item-link`]: Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genFocusOutline"])(token))
            }
        }
    };
};
const prepareComponentToken = (token)=>Object.assign({
        itemBg: token.colorBgContainer,
        itemSize: token.controlHeight,
        itemSizeSM: token.controlHeightSM,
        itemActiveBg: token.colorBgContainer,
        itemLinkBg: token.colorBgContainer,
        itemActiveColorDisabled: token.colorTextDisabled,
        itemActiveBgDisabled: token.controlItemBgActiveDisabled,
        itemInputBg: token.colorBgContainer,
        miniOptionsSizeChangerTop: 0
    }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$input$2f$style$2f$token$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initComponentToken"])(token));
const prepareToken = (token)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__["mergeToken"])(token, {
        inputOutlineOffset: 0,
        paginationMiniOptionsMarginInlineStart: token.calc(token.marginXXS).div(2).equal(),
        paginationMiniQuickJumperInputWidth: token.calc(token.controlHeightLG).mul(1.1).equal(),
        paginationItemPaddingInline: token.calc(token.marginXXS).mul(1.5).equal(),
        paginationEllipsisLetterSpacing: token.calc(token.marginXXS).div(2).equal(),
        paginationSlashMarginInlineStart: token.marginSM,
        paginationSlashMarginInlineEnd: token.marginSM,
        paginationEllipsisTextIndent: '0.13em' // magic for ui experience
    }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$input$2f$style$2f$token$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initInputToken"])(token));
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genStyleHooks"])('Pagination', (token)=>{
    const paginationToken = prepareToken(token);
    return [
        genPaginationStyle(paginationToken),
        genPaginationFocusStyle(paginationToken)
    ];
}, prepareComponentToken);
}}),
"[project]/node_modules/antd/es/pagination/style/bordered.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/util/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$pagination$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/pagination/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/util/genStyleUtils.js [app-client] (ecmascript)");
;
;
;
const genBorderedStyle = (token)=>{
    const { componentCls } = token;
    return {
        [`${componentCls}${componentCls}-bordered${componentCls}-disabled:not(${componentCls}-mini)`]: {
            '&, &:hover': {
                [`${componentCls}-item-link`]: {
                    borderColor: token.colorBorder
                }
            },
            '&:focus-visible': {
                [`${componentCls}-item-link`]: {
                    borderColor: token.colorBorder
                }
            },
            [`${componentCls}-item, ${componentCls}-item-link`]: {
                backgroundColor: token.colorBgContainerDisabled,
                borderColor: token.colorBorder,
                [`&:hover:not(${componentCls}-item-active)`]: {
                    backgroundColor: token.colorBgContainerDisabled,
                    borderColor: token.colorBorder,
                    a: {
                        color: token.colorTextDisabled
                    }
                },
                [`&${componentCls}-item-active`]: {
                    backgroundColor: token.itemActiveBgDisabled
                }
            },
            [`${componentCls}-prev, ${componentCls}-next`]: {
                '&:hover button': {
                    backgroundColor: token.colorBgContainerDisabled,
                    borderColor: token.colorBorder,
                    color: token.colorTextDisabled
                },
                [`${componentCls}-item-link`]: {
                    backgroundColor: token.colorBgContainerDisabled,
                    borderColor: token.colorBorder
                }
            }
        },
        [`${componentCls}${componentCls}-bordered:not(${componentCls}-mini)`]: {
            [`${componentCls}-prev, ${componentCls}-next`]: {
                '&:hover button': {
                    borderColor: token.colorPrimaryHover,
                    backgroundColor: token.itemBg
                },
                [`${componentCls}-item-link`]: {
                    backgroundColor: token.itemLinkBg,
                    borderColor: token.colorBorder
                },
                [`&:hover ${componentCls}-item-link`]: {
                    borderColor: token.colorPrimary,
                    backgroundColor: token.itemBg,
                    color: token.colorPrimary
                },
                [`&${componentCls}-disabled`]: {
                    [`${componentCls}-item-link`]: {
                        borderColor: token.colorBorder,
                        color: token.colorTextDisabled
                    }
                }
            },
            [`${componentCls}-item`]: {
                backgroundColor: token.itemBg,
                border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.lineWidth)} ${token.lineType} ${token.colorBorder}`,
                [`&:hover:not(${componentCls}-item-active)`]: {
                    borderColor: token.colorPrimary,
                    backgroundColor: token.itemBg,
                    a: {
                        color: token.colorPrimary
                    }
                },
                '&-active': {
                    borderColor: token.colorPrimary
                }
            }
        }
    };
};
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genSubStyleComponent"])([
    'Pagination',
    'bordered'
], (token)=>{
    const paginationToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$pagination$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["prepareToken"])(token);
    return [
        genBorderedStyle(paginationToken)
    ];
}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$pagination$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["prepareComponentToken"]);
}}),
"[project]/node_modules/antd/es/pagination/useShowSizeChanger.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useShowSizeChanger)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
function useShowSizeChanger(showSizeChanger) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useShowSizeChanger.useMemo": ()=>{
            if (typeof showSizeChanger === 'boolean') {
                return [
                    showSizeChanger,
                    {}
                ];
            }
            if (showSizeChanger && typeof showSizeChanger === 'object') {
                return [
                    true,
                    showSizeChanger
                ];
            }
            return [
                undefined,
                undefined
            ];
        }
    }["useShowSizeChanger.useMemo"], [
        showSizeChanger
    ]);
}
}}),
"[project]/node_modules/antd/es/pagination/Pagination.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$DoubleLeftOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/DoubleLeftOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$DoubleRightOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/DoubleRightOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$LeftOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/LeftOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$RightOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/RightOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$pagination$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-pagination/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$pagination$2f$es$2f$Pagination$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-pagination/es/Pagination.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$pagination$2f$es$2f$locale$2f$en_US$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-pagination/es/locale/en_US.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useSize$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/hooks/useSize.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$grid$2f$hooks$2f$useBreakpoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/grid/hooks/useBreakpoint.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$locale$2f$useLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useLocale$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/locale/useLocale.js [app-client] (ecmascript) <export default as useLocale>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$select$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/select/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$useToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useToken$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/useToken.js [app-client] (ecmascript) <export default as useToken>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$pagination$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/pagination/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$pagination$2f$style$2f$bordered$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/pagination/style/bordered.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$pagination$2f$useShowSizeChanger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/pagination/useShowSizeChanger.js [app-client] (ecmascript)");
"use client";
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const Pagination = (props)=>{
    const { align, prefixCls: customizePrefixCls, selectPrefixCls: customizeSelectPrefixCls, className, rootClassName, style, size: customizeSize, locale: customLocale, responsive, showSizeChanger, selectComponentClass, pageSizeOptions } = props, restProps = __rest(props, [
        "align",
        "prefixCls",
        "selectPrefixCls",
        "className",
        "rootClassName",
        "style",
        "size",
        "locale",
        "responsive",
        "showSizeChanger",
        "selectComponentClass",
        "pageSizeOptions"
    ]);
    const { xs } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$grid$2f$hooks$2f$useBreakpoint$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(responsive);
    const [, token] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$useToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useToken$3e$__["useToken"])();
    const { getPrefixCls, direction, showSizeChanger: contextShowSizeChangerConfig, className: contextClassName, style: contextStyle } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useComponentConfig"])('pagination');
    const prefixCls = getPrefixCls('pagination', customizePrefixCls);
    // Style
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$pagination$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls);
    // ============================== Size ==============================
    const mergedSize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useSize$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(customizeSize);
    const isSmall = mergedSize === 'small' || !!(xs && !mergedSize && responsive);
    // ============================= Locale =============================
    const [contextLocale] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$locale$2f$useLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useLocale$3e$__["useLocale"])('Pagination', __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$pagination$2f$es$2f$locale$2f$en_US$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
    const locale = Object.assign(Object.assign({}, contextLocale), customLocale);
    // ========================== Size Changer ==========================
    // Merge the props showSizeChanger
    const [propShowSizeChanger, propSizeChangerSelectProps] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$pagination$2f$useShowSizeChanger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(showSizeChanger);
    const [contextShowSizeChanger, contextSizeChangerSelectProps] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$pagination$2f$useShowSizeChanger$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(contextShowSizeChangerConfig);
    const mergedShowSizeChanger = propShowSizeChanger !== null && propShowSizeChanger !== void 0 ? propShowSizeChanger : contextShowSizeChanger;
    const mergedShowSizeChangerSelectProps = propSizeChangerSelectProps !== null && propSizeChangerSelectProps !== void 0 ? propSizeChangerSelectProps : contextSizeChangerSelectProps;
    const SizeChanger = selectComponentClass || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$select$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
    // Generate options
    const mergedPageSizeOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Pagination.useMemo[mergedPageSizeOptions]": ()=>{
            return pageSizeOptions ? pageSizeOptions.map({
                "Pagination.useMemo[mergedPageSizeOptions]": (option)=>Number(option)
            }["Pagination.useMemo[mergedPageSizeOptions]"]) : undefined;
        }
    }["Pagination.useMemo[mergedPageSizeOptions]"], [
        pageSizeOptions
    ]);
    // Render size changer
    const sizeChangerRender = (info)=>{
        var _a;
        const { disabled, size: pageSize, onSizeChange, 'aria-label': ariaLabel, className: sizeChangerClassName, options } = info;
        const { className: propSizeChangerClassName, onChange: propSizeChangerOnChange } = mergedShowSizeChangerSelectProps || {};
        // Origin Select is using Select.Option,
        // So it make the option value must be string
        // Just for compatible
        const selectedValue = (_a = options.find((option)=>String(option.value) === String(pageSize))) === null || _a === void 0 ? void 0 : _a.value;
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(SizeChanger, Object.assign({
            disabled: disabled,
            showSearch: true,
            popupMatchSelectWidth: false,
            getPopupContainer: (triggerNode)=>triggerNode.parentNode,
            "aria-label": ariaLabel,
            options: options
        }, mergedShowSizeChangerSelectProps, {
            value: selectedValue,
            onChange: (nextSize, option)=>{
                onSizeChange === null || onSizeChange === void 0 ? void 0 : onSizeChange(nextSize);
                propSizeChangerOnChange === null || propSizeChangerOnChange === void 0 ? void 0 : propSizeChangerOnChange(nextSize, option);
            },
            size: isSmall ? 'small' : 'middle',
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(sizeChangerClassName, propSizeChangerClassName)
        }));
    };
    if ("TURBOPACK compile-time truthy", 1) {
        const warning = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["devUseWarning"])('Pagination');
        ("TURBOPACK compile-time truthy", 1) ? warning(!selectComponentClass, 'usage', '`selectComponentClass` is not official api which will be removed.') : ("TURBOPACK unreachable", undefined);
    }
    // ============================= Render =============================
    const iconsProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Pagination.useMemo[iconsProps]": ()=>{
            const ellipsis = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("span", {
                className: `${prefixCls}-item-ellipsis`
            }, "\u2022\u2022\u2022");
            const prevIcon = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("button", {
                className: `${prefixCls}-item-link`,
                type: "button",
                tabIndex: -1
            }, direction === 'rtl' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$RightOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$LeftOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null));
            const nextIcon = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("button", {
                className: `${prefixCls}-item-link`,
                type: "button",
                tabIndex: -1
            }, direction === 'rtl' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$LeftOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$RightOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null));
            const jumpPrevIcon = /*#__PURE__*/ // biome-ignore lint/a11y/useValidAnchor: it is hard to refactor
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("a", {
                className: `${prefixCls}-item-link`
            }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
                className: `${prefixCls}-item-container`
            }, direction === 'rtl' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$DoubleRightOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                className: `${prefixCls}-item-link-icon`
            }) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$DoubleLeftOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                className: `${prefixCls}-item-link-icon`
            }), ellipsis));
            const jumpNextIcon = /*#__PURE__*/ // biome-ignore lint/a11y/useValidAnchor: it is hard to refactor
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("a", {
                className: `${prefixCls}-item-link`
            }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
                className: `${prefixCls}-item-container`
            }, direction === 'rtl' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$DoubleLeftOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                className: `${prefixCls}-item-link-icon`
            }) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$DoubleRightOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                className: `${prefixCls}-item-link-icon`
            }), ellipsis));
            return {
                prevIcon,
                nextIcon,
                jumpPrevIcon,
                jumpNextIcon
            };
        }
    }["Pagination.useMemo[iconsProps]"], [
        direction,
        prefixCls
    ]);
    const selectPrefixCls = getPrefixCls('select', customizeSelectPrefixCls);
    const extendedClassName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        [`${prefixCls}-${align}`]: !!align,
        [`${prefixCls}-mini`]: isSmall,
        [`${prefixCls}-rtl`]: direction === 'rtl',
        [`${prefixCls}-bordered`]: token.wireframe
    }, contextClassName, className, rootClassName, hashId, cssVarCls);
    const mergedStyle = Object.assign(Object.assign({}, contextStyle), style);
    return wrapCSSVar(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, token.wireframe && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$pagination$2f$style$2f$bordered$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        prefixCls: prefixCls
    }), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$pagination$2f$es$2f$Pagination$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], Object.assign({}, iconsProps, restProps, {
        style: mergedStyle,
        prefixCls: prefixCls,
        selectPrefixCls: selectPrefixCls,
        className: extendedClassName,
        locale: locale,
        pageSizeOptions: mergedPageSizeOptions,
        showSizeChanger: mergedShowSizeChanger,
        sizeChangerRender: sizeChangerRender
    }))));
};
if ("TURBOPACK compile-time truthy", 1) {
    Pagination.displayName = 'Pagination';
}
const __TURBOPACK__default__export__ = Pagination;
}}),
"[project]/node_modules/antd/es/pagination/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$pagination$2f$Pagination$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/pagination/Pagination.js [app-client] (ecmascript)");
"use client";
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$pagination$2f$Pagination$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
}}),
"[project]/node_modules/antd/es/tree/style/directory.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// ============================ Directory =============================
__turbopack_context__.s({
    "genDirectoryStyle": (()=>genDirectoryStyle)
});
const genDirectoryStyle = (_ref)=>{
    let { treeCls, treeNodeCls, directoryNodeSelectedBg, directoryNodeSelectedColor, motionDurationMid, borderRadius, controlItemBgHover } = _ref;
    return {
        [`${treeCls}${treeCls}-directory ${treeNodeCls}`]: {
            // >>> Title
            [`${treeCls}-node-content-wrapper`]: {
                position: 'static',
                [`> *:not(${treeCls}-drop-indicator)`]: {
                    position: 'relative'
                },
                '&:hover': {
                    background: 'transparent'
                },
                // Expand interactive area to whole line
                '&:before': {
                    position: 'absolute',
                    inset: 0,
                    transition: `background-color ${motionDurationMid}`,
                    content: '""',
                    borderRadius
                },
                '&:hover:before': {
                    background: controlItemBgHover
                }
            },
            [`${treeCls}-switcher, ${treeCls}-checkbox, ${treeCls}-draggable-icon`]: {
                zIndex: 1
            },
            // ============= Selected =============
            '&-selected': {
                [`${treeCls}-switcher, ${treeCls}-draggable-icon`]: {
                    color: directoryNodeSelectedColor
                },
                // >>> Title
                [`${treeCls}-node-content-wrapper`]: {
                    color: directoryNodeSelectedColor,
                    background: 'transparent',
                    '&:before, &:hover:before': {
                        background: directoryNodeSelectedBg
                    }
                }
            }
        }
    };
};
}}),
"[project]/node_modules/antd/es/tree/style/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "genBaseStyle": (()=>genBaseStyle),
    "genTreeStyle": (()=>genTreeStyle),
    "initComponentToken": (()=>initComponentToken),
    "prepareComponentToken": (()=>prepareComponentToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$Keyframes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Keyframes$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/Keyframes.js [app-client] (ecmascript) <export default as Keyframes>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/util/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$checkbox$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/checkbox/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$motion$2f$collapse$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__genCollapseMotion$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/style/motion/collapse.js [app-client] (ecmascript) <export default as genCollapseMotion>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/util/genStyleUtils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs-utils/es/util/statistic.js [app-client] (ecmascript) <export merge as mergeToken>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tree$2f$style$2f$directory$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/tree/style/directory.js [app-client] (ecmascript)");
;
;
;
;
;
;
// ============================ Keyframes =============================
const treeNodeFX = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$Keyframes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Keyframes$3e$__["Keyframes"]('ant-tree-node-fx-do-not-use', {
    '0%': {
        opacity: 0
    },
    '100%': {
        opacity: 1
    }
});
// ============================== Switch ==============================
const getSwitchStyle = (prefixCls, token)=>({
        [`.${prefixCls}-switcher-icon`]: {
            display: 'inline-block',
            fontSize: 10,
            verticalAlign: 'baseline',
            svg: {
                transition: `transform ${token.motionDurationSlow}`
            }
        }
    });
// =============================== Drop ===============================
const getDropIndicatorStyle = (prefixCls, token)=>({
        [`.${prefixCls}-drop-indicator`]: {
            position: 'absolute',
            // it should displayed over the following node
            zIndex: 1,
            height: 2,
            backgroundColor: token.colorPrimary,
            borderRadius: 1,
            pointerEvents: 'none',
            '&:after': {
                position: 'absolute',
                top: -3,
                insetInlineStart: -6,
                width: 8,
                height: 8,
                backgroundColor: 'transparent',
                border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.lineWidthBold)} solid ${token.colorPrimary}`,
                borderRadius: '50%',
                content: '""'
            }
        }
    });
const genBaseStyle = (prefixCls, token)=>{
    const { treeCls, treeNodeCls, treeNodePadding, titleHeight, indentSize, nodeSelectedBg, nodeHoverBg, colorTextQuaternary, controlItemBgActiveDisabled } = token;
    return {
        [treeCls]: Object.assign(Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resetComponent"])(token)), {
            background: token.colorBgContainer,
            borderRadius: token.borderRadius,
            transition: `background-color ${token.motionDurationSlow}`,
            '&-rtl': {
                direction: 'rtl'
            },
            [`&${treeCls}-rtl ${treeCls}-switcher_close ${treeCls}-switcher-icon svg`]: {
                transform: 'rotate(90deg)'
            },
            [`&-focused:not(:hover):not(${treeCls}-active-focused)`]: Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genFocusOutline"])(token)),
            // =================== Virtual List ===================
            [`${treeCls}-list-holder-inner`]: {
                alignItems: 'flex-start'
            },
            [`&${treeCls}-block-node`]: {
                [`${treeCls}-list-holder-inner`]: {
                    alignItems: 'stretch',
                    // >>> Title
                    [`${treeCls}-node-content-wrapper`]: {
                        flex: 'auto'
                    },
                    // >>> Drag
                    [`${treeNodeCls}.dragging:after`]: {
                        position: 'absolute',
                        inset: 0,
                        border: `1px solid ${token.colorPrimary}`,
                        opacity: 0,
                        animationName: treeNodeFX,
                        animationDuration: token.motionDurationSlow,
                        animationPlayState: 'running',
                        animationFillMode: 'forwards',
                        content: '""',
                        pointerEvents: 'none',
                        borderRadius: token.borderRadius
                    }
                }
            },
            // ===================== TreeNode =====================
            [treeNodeCls]: {
                display: 'flex',
                alignItems: 'flex-start',
                marginBottom: treeNodePadding,
                lineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(titleHeight),
                position: 'relative',
                // 非常重要，避免 drop-indicator 在拖拽过程中闪烁
                '&:before': {
                    content: '""',
                    position: 'absolute',
                    zIndex: 1,
                    insetInlineStart: 0,
                    width: '100%',
                    top: '100%',
                    height: treeNodePadding
                },
                // Disabled
                [`&-disabled ${treeCls}-node-content-wrapper`]: {
                    color: token.colorTextDisabled,
                    cursor: 'not-allowed',
                    '&:hover': {
                        background: 'transparent'
                    }
                },
                [`${treeCls}-checkbox-disabled + ${treeCls}-node-selected,&${treeNodeCls}-disabled${treeNodeCls}-selected ${treeCls}-node-content-wrapper`]: {
                    backgroundColor: controlItemBgActiveDisabled
                },
                // we can not set pointer-events to none for checkbox in tree
                // ref: https://github.com/ant-design/ant-design/issues/39822#issuecomment-2605234058
                [`${treeCls}-checkbox-disabled`]: {
                    pointerEvents: 'unset'
                },
                // not disable
                [`&:not(${treeNodeCls}-disabled)`]: {
                    // >>> Title
                    [`${treeCls}-node-content-wrapper`]: {
                        '&:hover': {
                            color: token.nodeHoverColor
                        }
                    }
                },
                [`&-active ${treeCls}-node-content-wrapper`]: {
                    background: token.controlItemBgHover
                },
                [`&:not(${treeNodeCls}-disabled).filter-node ${treeCls}-title`]: {
                    color: token.colorPrimary,
                    fontWeight: 500
                },
                '&-draggable': {
                    cursor: 'grab',
                    [`${treeCls}-draggable-icon`]: {
                        // https://github.com/ant-design/ant-design/issues/41915
                        flexShrink: 0,
                        width: titleHeight,
                        textAlign: 'center',
                        visibility: 'visible',
                        color: colorTextQuaternary
                    },
                    [`&${treeNodeCls}-disabled ${treeCls}-draggable-icon`]: {
                        visibility: 'hidden'
                    }
                }
            },
            // >>> Indent
            [`${treeCls}-indent`]: {
                alignSelf: 'stretch',
                whiteSpace: 'nowrap',
                userSelect: 'none',
                '&-unit': {
                    display: 'inline-block',
                    width: indentSize
                }
            },
            // >>> Drag Handler
            [`${treeCls}-draggable-icon`]: {
                visibility: 'hidden'
            },
            // Switcher / Checkbox
            [`${treeCls}-switcher, ${treeCls}-checkbox`]: {
                marginInlineEnd: token.calc(token.calc(titleHeight).sub(token.controlInteractiveSize)).div(2).equal()
            },
            // >>> Switcher
            [`${treeCls}-switcher`]: Object.assign(Object.assign({}, getSwitchStyle(prefixCls, token)), {
                position: 'relative',
                flex: 'none',
                alignSelf: 'stretch',
                width: titleHeight,
                textAlign: 'center',
                cursor: 'pointer',
                userSelect: 'none',
                transition: `all ${token.motionDurationSlow}`,
                '&-noop': {
                    cursor: 'unset'
                },
                '&:before': {
                    pointerEvents: 'none',
                    content: '""',
                    width: titleHeight,
                    height: titleHeight,
                    position: 'absolute',
                    left: {
                        _skip_check_: true,
                        value: 0
                    },
                    top: 0,
                    borderRadius: token.borderRadius,
                    transition: `all ${token.motionDurationSlow}`
                },
                [`&:not(${treeCls}-switcher-noop):hover:before`]: {
                    backgroundColor: token.colorBgTextHover
                },
                [`&_close ${treeCls}-switcher-icon svg`]: {
                    transform: 'rotate(-90deg)'
                },
                '&-loading-icon': {
                    color: token.colorPrimary
                },
                '&-leaf-line': {
                    position: 'relative',
                    zIndex: 1,
                    display: 'inline-block',
                    width: '100%',
                    height: '100%',
                    // https://github.com/ant-design/ant-design/issues/31884
                    '&:before': {
                        position: 'absolute',
                        top: 0,
                        insetInlineEnd: token.calc(titleHeight).div(2).equal(),
                        bottom: token.calc(treeNodePadding).mul(-1).equal(),
                        marginInlineStart: -1,
                        borderInlineEnd: `1px solid ${token.colorBorder}`,
                        content: '""'
                    },
                    '&:after': {
                        position: 'absolute',
                        width: token.calc(token.calc(titleHeight).div(2).equal()).mul(0.8).equal(),
                        height: token.calc(titleHeight).div(2).equal(),
                        borderBottom: `1px solid ${token.colorBorder}`,
                        content: '""'
                    }
                }
            }),
            // >>> Title
            // add `${treeCls}-checkbox + span` to cover checkbox `${checkboxCls} + span`
            [`${treeCls}-node-content-wrapper`]: Object.assign(Object.assign({
                position: 'relative',
                minHeight: titleHeight,
                paddingBlock: 0,
                paddingInline: token.paddingXS,
                background: 'transparent',
                borderRadius: token.borderRadius,
                cursor: 'pointer',
                transition: `all ${token.motionDurationMid}, border 0s, line-height 0s, box-shadow 0s`
            }, getDropIndicatorStyle(prefixCls, token)), {
                '&:hover': {
                    backgroundColor: nodeHoverBg
                },
                [`&${treeCls}-node-selected`]: {
                    color: token.nodeSelectedColor,
                    backgroundColor: nodeSelectedBg
                },
                // Icon
                [`${treeCls}-iconEle`]: {
                    display: 'inline-block',
                    width: titleHeight,
                    height: titleHeight,
                    textAlign: 'center',
                    verticalAlign: 'top',
                    '&:empty': {
                        display: 'none'
                    }
                }
            }),
            // https://github.com/ant-design/ant-design/issues/28217
            [`${treeCls}-unselectable ${treeCls}-node-content-wrapper:hover`]: {
                backgroundColor: 'transparent'
            },
            [`${treeNodeCls}.drop-container > [draggable]`]: {
                boxShadow: `0 0 0 2px ${token.colorPrimary}`
            },
            // ==================== Show Line =====================
            '&-show-line': {
                // ================ Indent lines ================
                [`${treeCls}-indent-unit`]: {
                    position: 'relative',
                    height: '100%',
                    '&:before': {
                        position: 'absolute',
                        top: 0,
                        insetInlineEnd: token.calc(titleHeight).div(2).equal(),
                        bottom: token.calc(treeNodePadding).mul(-1).equal(),
                        borderInlineEnd: `1px solid ${token.colorBorder}`,
                        content: '""'
                    },
                    '&-end:before': {
                        display: 'none'
                    }
                },
                // ============== Cover Background ==============
                [`${treeCls}-switcher`]: {
                    background: 'transparent',
                    '&-line-icon': {
                        // https://github.com/ant-design/ant-design/issues/32813
                        verticalAlign: '-0.15em'
                    }
                }
            },
            [`${treeNodeCls}-leaf-last ${treeCls}-switcher-leaf-line:before`]: {
                top: 'auto !important',
                bottom: 'auto !important',
                height: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.calc(titleHeight).div(2).equal())} !important`
            }
        })
    };
};
const genTreeStyle = function(prefixCls, token) {
    let enableDirectory = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;
    const treeCls = `.${prefixCls}`;
    const treeNodeCls = `${treeCls}-treenode`;
    const treeNodePadding = token.calc(token.paddingXS).div(2).equal();
    const treeToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__["mergeToken"])(token, {
        treeCls,
        treeNodeCls,
        treeNodePadding
    });
    return [
        // Basic
        genBaseStyle(prefixCls, treeToken),
        // Directory
        enableDirectory && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tree$2f$style$2f$directory$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genDirectoryStyle"])(treeToken)
    ].filter(Boolean);
};
const initComponentToken = (token)=>{
    const { controlHeightSM, controlItemBgHover, controlItemBgActive } = token;
    const titleHeight = controlHeightSM;
    return {
        titleHeight,
        indentSize: titleHeight,
        nodeHoverBg: controlItemBgHover,
        nodeHoverColor: token.colorText,
        nodeSelectedBg: controlItemBgActive,
        nodeSelectedColor: token.colorText
    };
};
const prepareComponentToken = (token)=>{
    const { colorTextLightSolid, colorPrimary } = token;
    return Object.assign(Object.assign({}, initComponentToken(token)), {
        directoryNodeSelectedColor: colorTextLightSolid,
        directoryNodeSelectedBg: colorPrimary
    });
};
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genStyleHooks"])('Tree', (token, _ref)=>{
    let { prefixCls } = _ref;
    return [
        {
            [token.componentCls]: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$checkbox$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getStyle"])(`${prefixCls}-checkbox`, token)
        },
        genTreeStyle(prefixCls, token),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$motion$2f$collapse$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__genCollapseMotion$3e$__["genCollapseMotion"])(token)
    ];
}, prepareComponentToken);
}}),
"[project]/node_modules/antd/es/tree/utils/dropIndicator.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "offset": (()=>offset)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
"use client";
;
const offset = 4;
function dropIndicatorRender(props) {
    const { dropPosition, dropLevelOffset, prefixCls, indent, direction = 'ltr' } = props;
    const startPosition = direction === 'ltr' ? 'left' : 'right';
    const endPosition = direction === 'ltr' ? 'right' : 'left';
    const style = {
        [startPosition]: -dropLevelOffset * indent + offset,
        [endPosition]: 0
    };
    switch(dropPosition){
        case -1:
            style.top = -3;
            break;
        case 1:
            style.bottom = -3;
            break;
        default:
            // dropPosition === 0
            style.bottom = -3;
            style[startPosition] = indent + offset;
            break;
    }
    return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        style: style,
        className: `${prefixCls}-drop-indicator`
    });
}
const __TURBOPACK__default__export__ = dropIndicatorRender;
}}),
"[project]/node_modules/antd/es/tree/utils/iconUtil.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CaretDownFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/CaretDownFilled.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$FileOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/FileOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$LoadingOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/LoadingOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$MinusSquareOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/MinusSquareOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$PlusSquareOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/PlusSquareOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$reactNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/reactNode.js [app-client] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
const SwitcherIconCom = (props)=>{
    const { prefixCls, switcherIcon, treeNodeProps, showLine, switcherLoadingIcon } = props;
    const { isLeaf, expanded, loading } = treeNodeProps;
    if (loading) {
        if (/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidElement"])(switcherLoadingIcon)) {
            return switcherLoadingIcon;
        }
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$LoadingOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            className: `${prefixCls}-switcher-loading-icon`
        });
    }
    let showLeafIcon;
    if (showLine && typeof showLine === 'object') {
        showLeafIcon = showLine.showLeafIcon;
    }
    if (isLeaf) {
        if (!showLine) {
            return null;
        }
        if (typeof showLeafIcon !== 'boolean' && !!showLeafIcon) {
            const leafIcon = typeof showLeafIcon === 'function' ? showLeafIcon(treeNodeProps) : showLeafIcon;
            const leafCls = `${prefixCls}-switcher-line-custom-icon`;
            if (/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidElement"])(leafIcon)) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$reactNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cloneElement"])(leafIcon, {
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(leafIcon.props.className || '', leafCls)
                });
            }
            return leafIcon;
        }
        return showLeafIcon ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$FileOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            className: `${prefixCls}-switcher-line-icon`
        }) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("span", {
            className: `${prefixCls}-switcher-leaf-line`
        });
    }
    const switcherCls = `${prefixCls}-switcher-icon`;
    const switcher = typeof switcherIcon === 'function' ? switcherIcon(treeNodeProps) : switcherIcon;
    if (/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidElement"])(switcher)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$reactNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cloneElement"])(switcher, {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(switcher.props.className || '', switcherCls)
        });
    }
    if (switcher !== undefined) {
        return switcher;
    }
    if (showLine) {
        return expanded ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$MinusSquareOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            className: `${prefixCls}-switcher-line-icon`
        }) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$PlusSquareOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            className: `${prefixCls}-switcher-line-icon`
        });
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CaretDownFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        className: switcherCls
    });
};
const __TURBOPACK__default__export__ = SwitcherIconCom;
}}),
"[project]/node_modules/antd/es/tree/Tree.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$HolderOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/HolderOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-tree/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-tree/es/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$motion$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/motion.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$useToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useToken$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/useToken.js [app-client] (ecmascript) <export default as useToken>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tree$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/tree/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tree$2f$utils$2f$dropIndicator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/tree/utils/dropIndicator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tree$2f$utils$2f$iconUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/tree/utils/iconUtil.js [app-client] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
const Tree = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].forwardRef((props, ref)=>{
    var _a;
    const { getPrefixCls, direction, virtual, tree } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useContext(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfigContext"]);
    const { prefixCls: customizePrefixCls, className, showIcon = false, showLine, switcherIcon, switcherLoadingIcon, blockNode = false, children, checkable = false, selectable = true, draggable, motion: customMotion, style } = props;
    const prefixCls = getPrefixCls('tree', customizePrefixCls);
    const rootPrefixCls = getPrefixCls();
    const motion = customMotion !== null && customMotion !== void 0 ? customMotion : Object.assign(Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$motion$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(rootPrefixCls)), {
        motionAppear: false
    });
    const newProps = Object.assign(Object.assign({}, props), {
        checkable,
        selectable,
        showIcon,
        motion,
        blockNode,
        showLine: Boolean(showLine),
        dropIndicatorRender: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tree$2f$utils$2f$dropIndicator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
    });
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tree$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls);
    const [, token] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$useToken$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useToken$3e$__["useToken"])();
    const itemHeight = token.paddingXS / 2 + (((_a = token.Tree) === null || _a === void 0 ? void 0 : _a.titleHeight) || token.controlHeightSM);
    const draggableConfig = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useMemo({
        "Tree.useMemo[draggableConfig]": ()=>{
            if (!draggable) {
                return false;
            }
            let mergedDraggable = {};
            switch(typeof draggable){
                case 'function':
                    mergedDraggable.nodeDraggable = draggable;
                    break;
                case 'object':
                    mergedDraggable = Object.assign({}, draggable);
                    break;
                default:
                    break;
            }
            if (mergedDraggable.icon !== false) {
                mergedDraggable.icon = mergedDraggable.icon || /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$HolderOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null);
            }
            return mergedDraggable;
        }
    }["Tree.useMemo[draggableConfig]"], [
        draggable
    ]);
    const renderSwitcherIcon = (nodeProps)=>/*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tree$2f$utils$2f$iconUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            prefixCls: prefixCls,
            switcherIcon: switcherIcon,
            switcherLoadingIcon: switcherLoadingIcon,
            treeNodeProps: nodeProps,
            showLine: showLine
        });
    return wrapCSSVar(/*#__PURE__*/ // @ts-ignore
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"], Object.assign({
        itemHeight: itemHeight,
        ref: ref,
        virtual: virtual
    }, newProps, {
        // newProps may contain style so declare style below it
        style: Object.assign(Object.assign({}, tree === null || tree === void 0 ? void 0 : tree.style), style),
        prefixCls: prefixCls,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
            [`${prefixCls}-icon-hide`]: !showIcon,
            [`${prefixCls}-block-node`]: blockNode,
            [`${prefixCls}-unselectable`]: !selectable,
            [`${prefixCls}-rtl`]: direction === 'rtl'
        }, tree === null || tree === void 0 ? void 0 : tree.className, className, hashId, cssVarCls),
        direction: direction,
        checkable: checkable ? /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("span", {
            className: `${prefixCls}-checkbox-inner`
        }) : checkable,
        selectable: selectable,
        switcherIcon: renderSwitcherIcon,
        draggable: draggableConfig
    }), children));
});
if ("TURBOPACK compile-time truthy", 1) {
    Tree.displayName = 'Tree';
}
const __TURBOPACK__default__export__ = Tree;
}}),
"[project]/node_modules/antd/es/tree/utils/dictUtil.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "calcRangeKeys": (()=>calcRangeKeys),
    "convertDirectoryKeysToNodes": (()=>convertDirectoryKeysToNodes)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$utils$2f$treeUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree/es/utils/treeUtil.js [app-client] (ecmascript)");
;
;
const RECORD_NONE = 0;
const RECORD_START = 1;
const RECORD_END = 2;
function traverseNodesKey(treeData, callback, fieldNames) {
    const { key: fieldKey, children: fieldChildren } = fieldNames;
    function processNode(dataNode) {
        const key = dataNode[fieldKey];
        const children = dataNode[fieldChildren];
        if (callback(key, dataNode) !== false) {
            traverseNodesKey(children || [], callback, fieldNames);
        }
    }
    treeData.forEach(processNode);
}
function calcRangeKeys(_ref) {
    let { treeData, expandedKeys, startKey, endKey, fieldNames } = _ref;
    const keys = [];
    let record = RECORD_NONE;
    if (startKey && startKey === endKey) {
        return [
            startKey
        ];
    }
    if (!startKey || !endKey) {
        return [];
    }
    function matchKey(key) {
        return key === startKey || key === endKey;
    }
    traverseNodesKey(treeData, (key)=>{
        if (record === RECORD_END) {
            return false;
        }
        if (matchKey(key)) {
            // Match test
            keys.push(key);
            if (record === RECORD_NONE) {
                record = RECORD_START;
            } else if (record === RECORD_START) {
                record = RECORD_END;
                return false;
            }
        } else if (record === RECORD_START) {
            // Append selection
            keys.push(key);
        }
        return expandedKeys.includes(key);
    }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$utils$2f$treeUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fillFieldNames"])(fieldNames));
    return keys;
}
function convertDirectoryKeysToNodes(treeData, keys, fieldNames) {
    const restKeys = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(keys);
    const nodes = [];
    traverseNodesKey(treeData, (key, node)=>{
        const index = restKeys.indexOf(key);
        if (index !== -1) {
            nodes.push(node);
            restKeys.splice(index, 1);
        }
        return !!restKeys.length;
    }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$utils$2f$treeUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fillFieldNames"])(fieldNames));
    return nodes;
}
}}),
"[project]/node_modules/antd/es/tree/DirectoryTree.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$FileOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/FileOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$FolderOpenOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/FolderOpenOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$FolderOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/FolderOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$util$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-tree/es/util.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$util$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-tree/es/util.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$utils$2f$treeUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-tree/es/utils/treeUtil.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tree$2f$Tree$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/tree/Tree.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tree$2f$utils$2f$dictUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/tree/utils/dictUtil.js [app-client] (ecmascript)");
"use client";
;
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
;
;
;
;
;
;
;
;
;
;
function getIcon(props) {
    const { isLeaf, expanded } = props;
    if (isLeaf) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$FileOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null);
    }
    return expanded ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$FolderOpenOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$FolderOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null);
}
function getTreeData(_ref) {
    let { treeData, children } = _ref;
    return treeData || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$utils$2f$treeUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertTreeToData"])(children);
}
const DirectoryTree = (_a, ref)=>{
    var { defaultExpandAll, defaultExpandParent, defaultExpandedKeys } = _a, props = __rest(_a, [
        "defaultExpandAll",
        "defaultExpandParent",
        "defaultExpandedKeys"
    ]);
    // Shift click usage
    const lastSelectedKey = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const cachedSelectedKeys = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const getInitExpandedKeys = ()=>{
        const { keyEntities } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$utils$2f$treeUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertDataToEntities"])(getTreeData(props));
        let initExpandedKeys;
        // Expanded keys
        if (defaultExpandAll) {
            initExpandedKeys = Object.keys(keyEntities);
        } else if (defaultExpandParent) {
            initExpandedKeys = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$util$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["conductExpandParent"])(props.expandedKeys || defaultExpandedKeys || [], keyEntities);
        } else {
            initExpandedKeys = props.expandedKeys || defaultExpandedKeys || [];
        }
        return initExpandedKeys;
    };
    const [selectedKeys, setSelectedKeys] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(props.selectedKeys || props.defaultSelectedKeys || []);
    const [expandedKeys, setExpandedKeys] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "DirectoryTree.useState": ()=>getInitExpandedKeys()
    }["DirectoryTree.useState"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DirectoryTree.useEffect": ()=>{
            if ('selectedKeys' in props) {
                setSelectedKeys(props.selectedKeys);
            }
        }
    }["DirectoryTree.useEffect"], [
        props.selectedKeys
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DirectoryTree.useEffect": ()=>{
            if ('expandedKeys' in props) {
                setExpandedKeys(props.expandedKeys);
            }
        }
    }["DirectoryTree.useEffect"], [
        props.expandedKeys
    ]);
    const onExpand = (keys, info)=>{
        var _a;
        if (!('expandedKeys' in props)) {
            setExpandedKeys(keys);
        }
        // Call origin function
        return (_a = props.onExpand) === null || _a === void 0 ? void 0 : _a.call(props, keys, info);
    };
    const onSelect = (keys, event)=>{
        var _a;
        const { multiple, fieldNames } = props;
        const { node, nativeEvent } = event;
        const { key = '' } = node;
        const treeData = getTreeData(props);
        // const newState: DirectoryTreeState = {};
        // We need wrap this event since some value is not same
        const newEvent = Object.assign(Object.assign({}, event), {
            selected: true
        });
        // Windows / Mac single pick
        const ctrlPick = (nativeEvent === null || nativeEvent === void 0 ? void 0 : nativeEvent.ctrlKey) || (nativeEvent === null || nativeEvent === void 0 ? void 0 : nativeEvent.metaKey);
        const shiftPick = nativeEvent === null || nativeEvent === void 0 ? void 0 : nativeEvent.shiftKey;
        // Generate new selected keys
        let newSelectedKeys;
        if (multiple && ctrlPick) {
            // Control click
            newSelectedKeys = keys;
            lastSelectedKey.current = key;
            cachedSelectedKeys.current = newSelectedKeys;
            newEvent.selectedNodes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tree$2f$utils$2f$dictUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertDirectoryKeysToNodes"])(treeData, newSelectedKeys, fieldNames);
        } else if (multiple && shiftPick) {
            // Shift click
            newSelectedKeys = Array.from(new Set([].concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(cachedSelectedKeys.current || []), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tree$2f$utils$2f$dictUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calcRangeKeys"])({
                treeData,
                expandedKeys,
                startKey: key,
                endKey: lastSelectedKey.current,
                fieldNames
            })))));
            newEvent.selectedNodes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tree$2f$utils$2f$dictUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertDirectoryKeysToNodes"])(treeData, newSelectedKeys, fieldNames);
        } else {
            // Single click
            newSelectedKeys = [
                key
            ];
            lastSelectedKey.current = key;
            cachedSelectedKeys.current = newSelectedKeys;
            newEvent.selectedNodes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tree$2f$utils$2f$dictUtil$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertDirectoryKeysToNodes"])(treeData, newSelectedKeys, fieldNames);
        }
        (_a = props.onSelect) === null || _a === void 0 ? void 0 : _a.call(props, newSelectedKeys, newEvent);
        if (!('selectedKeys' in props)) {
            setSelectedKeys(newSelectedKeys);
        }
    };
    const { getPrefixCls, direction } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfigContext"]);
    const { prefixCls: customizePrefixCls, className, showIcon = true, expandAction = 'click' } = props, otherProps = __rest(props, [
        "prefixCls",
        "className",
        "showIcon",
        "expandAction"
    ]);
    const prefixCls = getPrefixCls('tree', customizePrefixCls);
    const connectClassName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(`${prefixCls}-directory`, {
        [`${prefixCls}-directory-rtl`]: direction === 'rtl'
    }, className);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tree$2f$Tree$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], Object.assign({
        icon: getIcon,
        ref: ref,
        blockNode: true
    }, otherProps, {
        showIcon: showIcon,
        expandAction: expandAction,
        prefixCls: prefixCls,
        className: connectClassName,
        expandedKeys: expandedKeys,
        selectedKeys: selectedKeys,
        onSelect: onSelect,
        onExpand: onExpand
    }));
};
const ForwardDirectoryTree = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(DirectoryTree);
if ("TURBOPACK compile-time truthy", 1) {
    ForwardDirectoryTree.displayName = 'DirectoryTree';
}
const __TURBOPACK__default__export__ = ForwardDirectoryTree;
}}),
"[project]/node_modules/antd/es/tree/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-tree/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$TreeNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TreeNode$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-tree/es/TreeNode.js [app-client] (ecmascript) <export default as TreeNode>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tree$2f$DirectoryTree$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/tree/DirectoryTree.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tree$2f$Tree$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/tree/Tree.js [app-client] (ecmascript)");
"use client";
;
;
;
const Tree = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tree$2f$Tree$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
Tree.DirectoryTree = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tree$2f$DirectoryTree$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
Tree.TreeNode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$tree$2f$es$2f$TreeNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__TreeNode$3e$__["TreeNode"];
const __TURBOPACK__default__export__ = Tree;
}}),
"[project]/node_modules/antd/es/tree/index.js [app-client] (ecmascript) <export default as Tree>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Tree": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tree$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tree$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/tree/index.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/antd/es/tag/style/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "prepareComponentToken": (()=>prepareComponentToken),
    "prepareToken": (()=>prepareToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/util/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$fast$2d$color$2f$es$2f$FastColor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/fast-color/es/FastColor.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/util/genStyleUtils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs-utils/es/util/statistic.js [app-client] (ecmascript) <export merge as mergeToken>");
;
;
;
;
// ============================== Styles ==============================
const genBaseStyle = (token)=>{
    const { paddingXXS, lineWidth, tagPaddingHorizontal, componentCls, calc } = token;
    const paddingInline = calc(tagPaddingHorizontal).sub(lineWidth).equal();
    const iconMarginInline = calc(paddingXXS).sub(lineWidth).equal();
    return {
        // Result
        [componentCls]: Object.assign(Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resetComponent"])(token)), {
            display: 'inline-block',
            height: 'auto',
            // https://github.com/ant-design/ant-design/pull/47504
            marginInlineEnd: token.marginXS,
            paddingInline,
            fontSize: token.tagFontSize,
            lineHeight: token.tagLineHeight,
            whiteSpace: 'nowrap',
            background: token.defaultBg,
            border: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.lineWidth)} ${token.lineType} ${token.colorBorder}`,
            borderRadius: token.borderRadiusSM,
            opacity: 1,
            transition: `all ${token.motionDurationMid}`,
            textAlign: 'start',
            position: 'relative',
            // RTL
            [`&${componentCls}-rtl`]: {
                direction: 'rtl'
            },
            '&, a, a:hover': {
                color: token.defaultColor
            },
            [`${componentCls}-close-icon`]: {
                marginInlineStart: iconMarginInline,
                fontSize: token.tagIconSize,
                color: token.colorIcon,
                cursor: 'pointer',
                transition: `all ${token.motionDurationMid}`,
                '&:hover': {
                    color: token.colorTextHeading
                }
            },
            [`&${componentCls}-has-color`]: {
                borderColor: 'transparent',
                [`&, a, a:hover, ${token.iconCls}-close, ${token.iconCls}-close:hover`]: {
                    color: token.colorTextLightSolid
                }
            },
            '&-checkable': {
                backgroundColor: 'transparent',
                borderColor: 'transparent',
                cursor: 'pointer',
                [`&:not(${componentCls}-checkable-checked):hover`]: {
                    color: token.colorPrimary,
                    backgroundColor: token.colorFillSecondary
                },
                '&:active, &-checked': {
                    color: token.colorTextLightSolid
                },
                '&-checked': {
                    backgroundColor: token.colorPrimary,
                    '&:hover': {
                        backgroundColor: token.colorPrimaryHover
                    }
                },
                '&:active': {
                    backgroundColor: token.colorPrimaryActive
                }
            },
            '&-hidden': {
                display: 'none'
            },
            // To ensure that a space will be placed between character and `Icon`.
            [`> ${token.iconCls} + span, > span + ${token.iconCls}`]: {
                marginInlineStart: paddingInline
            }
        }),
        [`${componentCls}-borderless`]: {
            borderColor: 'transparent',
            background: token.tagBorderlessBg
        }
    };
};
const prepareToken = (token)=>{
    const { lineWidth, fontSizeIcon, calc } = token;
    const tagFontSize = token.fontSizeSM;
    const tagToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__["mergeToken"])(token, {
        tagFontSize,
        tagLineHeight: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(calc(token.lineHeightSM).mul(tagFontSize).equal()),
        tagIconSize: calc(fontSizeIcon).sub(calc(lineWidth).mul(2)).equal(),
        // Tag icon is much smaller
        tagPaddingHorizontal: 8,
        // Fixed padding.
        tagBorderlessBg: token.defaultBg
    });
    return tagToken;
};
const prepareComponentToken = (token)=>({
        defaultBg: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$fast$2d$color$2f$es$2f$FastColor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FastColor"](token.colorFillQuaternary).onBackground(token.colorBgContainer).toHexString(),
        defaultColor: token.colorText
    });
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genStyleHooks"])('Tag', (token)=>{
    const tagToken = prepareToken(token);
    return genBaseStyle(tagToken);
}, prepareComponentToken);
}}),
"[project]/node_modules/antd/es/tag/CheckableTag.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/tag/style/index.js [app-client] (ecmascript)");
"use client";
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
;
;
;
;
const CheckableTag = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((props, ref)=>{
    const { prefixCls: customizePrefixCls, style, className, checked, onChange, onClick } = props, restProps = __rest(props, [
        "prefixCls",
        "style",
        "className",
        "checked",
        "onChange",
        "onClick"
    ]);
    const { getPrefixCls, tag } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfigContext"]);
    const handleClick = (e)=>{
        onChange === null || onChange === void 0 ? void 0 : onChange(!checked);
        onClick === null || onClick === void 0 ? void 0 : onClick(e);
    };
    const prefixCls = getPrefixCls('tag', customizePrefixCls);
    // Style
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls);
    const cls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, `${prefixCls}-checkable`, {
        [`${prefixCls}-checkable-checked`]: checked
    }, tag === null || tag === void 0 ? void 0 : tag.className, className, hashId, cssVarCls);
    return wrapCSSVar(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("span", Object.assign({}, restProps, {
        ref: ref,
        style: Object.assign(Object.assign({}, style), tag === null || tag === void 0 ? void 0 : tag.style),
        className: cls,
        onClick: handleClick
    })));
});
const __TURBOPACK__default__export__ = CheckableTag;
}}),
"[project]/node_modules/antd/es/tag/style/presetCmp.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Style as status component
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/tag/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genPresetColor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__genPresetColor$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/util/genPresetColor.js [app-client] (ecmascript) <export default as genPresetColor>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/util/genStyleUtils.js [app-client] (ecmascript)");
;
;
// ============================== Preset ==============================
const genPresetStyle = (token)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genPresetColor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__genPresetColor$3e$__["genPresetColor"])(token, (colorKey, _ref)=>{
        let { textColor, lightBorderColor, lightColor, darkColor } = _ref;
        return {
            [`${token.componentCls}${token.componentCls}-${colorKey}`]: {
                color: textColor,
                background: lightColor,
                borderColor: lightBorderColor,
                // Inverse color
                '&-inverse': {
                    color: token.colorTextLightSolid,
                    background: darkColor,
                    borderColor: darkColor
                },
                [`&${token.componentCls}-borderless`]: {
                    borderColor: 'transparent'
                }
            }
        };
    });
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genSubStyleComponent"])([
    'Tag',
    'preset'
], (token)=>{
    const tagToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["prepareToken"])(token);
    return genPresetStyle(tagToken);
}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["prepareComponentToken"]);
}}),
"[project]/node_modules/antd/es/tag/style/statusCmp.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/tag/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$capitalize$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/capitalize.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/util/genStyleUtils.js [app-client] (ecmascript)");
;
;
;
const genTagStatusStyle = (token, status, cssVariableType)=>{
    const capitalizedCssVariableType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$capitalize$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(cssVariableType);
    return {
        [`${token.componentCls}${token.componentCls}-${status}`]: {
            color: token[`color${cssVariableType}`],
            background: token[`color${capitalizedCssVariableType}Bg`],
            borderColor: token[`color${capitalizedCssVariableType}Border`],
            [`&${token.componentCls}-borderless`]: {
                borderColor: 'transparent'
            }
        }
    };
};
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genSubStyleComponent"])([
    'Tag',
    'status'
], (token)=>{
    const tagToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["prepareToken"])(token);
    return [
        genTagStatusStyle(tagToken, 'success', 'Success'),
        genTagStatusStyle(tagToken, 'processing', 'Info'),
        genTagStatusStyle(tagToken, 'error', 'Error'),
        genTagStatusStyle(tagToken, 'warning', 'Warning')
    ];
}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["prepareComponentToken"]);
}}),
"[project]/node_modules/antd/es/tag/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$omit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/omit.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$colors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/colors.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$hooks$2f$useClosable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/hooks/useClosable.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$reactNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/reactNode.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$wave$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/wave/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$CheckableTag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/tag/CheckableTag.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/tag/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$style$2f$presetCmp$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/tag/style/presetCmp.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$style$2f$statusCmp$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/tag/style/statusCmp.js [app-client] (ecmascript)");
"use client";
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
;
;
;
;
;
;
;
;
;
;
;
;
;
const InternalTag = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((tagProps, ref)=>{
    const { prefixCls: customizePrefixCls, className, rootClassName, style, children, icon, color, onClose, bordered = true, visible: deprecatedVisible } = tagProps, props = __rest(tagProps, [
        "prefixCls",
        "className",
        "rootClassName",
        "style",
        "children",
        "icon",
        "color",
        "onClose",
        "bordered",
        "visible"
    ]);
    const { getPrefixCls, direction, tag: tagContext } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfigContext"]);
    const [visible, setVisible] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const domProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$omit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props, [
        'closeIcon',
        'closable'
    ]);
    // Warning for deprecated usage
    if ("TURBOPACK compile-time truthy", 1) {
        const warning = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["devUseWarning"])('Tag');
        warning.deprecated(!('visible' in tagProps), 'visible', 'visible && <Tag />');
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InternalTag.useEffect": ()=>{
            if (deprecatedVisible !== undefined) {
                setVisible(deprecatedVisible);
            }
        }
    }["InternalTag.useEffect"], [
        deprecatedVisible
    ]);
    const isPreset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$colors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isPresetColor"])(color);
    const isStatus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$colors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isPresetStatusColor"])(color);
    const isInternalColor = isPreset || isStatus;
    const tagStyle = Object.assign(Object.assign({
        backgroundColor: color && !isInternalColor ? color : undefined
    }, tagContext === null || tagContext === void 0 ? void 0 : tagContext.style), style);
    const prefixCls = getPrefixCls('tag', customizePrefixCls);
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls);
    // Style
    const tagClassName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, tagContext === null || tagContext === void 0 ? void 0 : tagContext.className, {
        [`${prefixCls}-${color}`]: isInternalColor,
        [`${prefixCls}-has-color`]: color && !isInternalColor,
        [`${prefixCls}-hidden`]: !visible,
        [`${prefixCls}-rtl`]: direction === 'rtl',
        [`${prefixCls}-borderless`]: !bordered
    }, className, rootClassName, hashId, cssVarCls);
    const handleCloseClick = (e)=>{
        e.stopPropagation();
        onClose === null || onClose === void 0 ? void 0 : onClose(e);
        if (e.defaultPrevented) {
            return;
        }
        setVisible(false);
    };
    const [, mergedCloseIcon] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$hooks$2f$useClosable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$hooks$2f$useClosable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["pickClosable"])(tagProps), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$hooks$2f$useClosable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["pickClosable"])(tagContext), {
        closable: false,
        closeIconRender: {
            "InternalTag.useClosable": (iconNode)=>{
                const replacement = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("span", {
                    className: `${prefixCls}-close-icon`,
                    onClick: handleCloseClick
                }, iconNode);
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$reactNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["replaceElement"])(iconNode, replacement, {
                    "InternalTag.useClosable": (originProps)=>({
                            onClick: ({
                                "InternalTag.useClosable": (e)=>{
                                    var _a;
                                    (_a = originProps === null || originProps === void 0 ? void 0 : originProps.onClick) === null || _a === void 0 ? void 0 : _a.call(originProps, e);
                                    handleCloseClick(e);
                                }
                            })["InternalTag.useClosable"],
                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(originProps === null || originProps === void 0 ? void 0 : originProps.className, `${prefixCls}-close-icon`)
                        })
                }["InternalTag.useClosable"]);
            }
        }["InternalTag.useClosable"]
    });
    const isNeedWave = typeof props.onClick === 'function' || children && children.type === 'a';
    const iconNode = icon || null;
    const kids = iconNode ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, iconNode, children && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("span", null, children)) : children;
    const tagNode = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("span", Object.assign({}, domProps, {
        ref: ref,
        className: tagClassName,
        style: tagStyle
    }), kids, mergedCloseIcon, isPreset && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$style$2f$presetCmp$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        key: "preset",
        prefixCls: prefixCls
    }), isStatus && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$style$2f$statusCmp$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        key: "status",
        prefixCls: prefixCls
    }));
    return wrapCSSVar(isNeedWave ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$wave$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        component: "Tag"
    }, tagNode) : tagNode);
});
const Tag = InternalTag;
if ("TURBOPACK compile-time truthy", 1) {
    Tag.displayName = 'Tag';
}
Tag.CheckableTag = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$CheckableTag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
const __TURBOPACK__default__export__ = Tag;
}}),
"[project]/node_modules/antd/es/tag/index.js [app-client] (ecmascript) <export default as Tag>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Tag": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tag$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/tag/index.js [app-client] (ecmascript)");
}}),
}]);

//# sourceMappingURL=node_modules_antd_es_1b5e832d._.js.map