package com.bosi.sim.paas.admin.server.controller;

import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.exception.CommonResult;
import com.bosi.sim.paas.common.core.utils.PageUtil;
import com.bosi.sim.paas.common.optlog.annotation.OperateLog;
import com.bosi.sim.paas.common.security.annotation.RequiresPermissions;
import com.bosi.sim.paas.dao.model.tds.TdsTenant;
import com.bosi.sim.paas.dao.model.tds.TmsNotify;
import com.bosi.sim.paas.dao.model.tds.TdsNotifyRecord;
import com.bosi.sim.paas.admin.server.service.TmsNotifyService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 通知管理
 */
@RestController
@Api(tags = "TmsNotifyController", value = "通知管理")
@RequestMapping("/tms/notify")
public class TmsNotifyController {
    @Autowired
    private TmsNotifyService tmsNotifyService;

    @RequiresPermissions("tms:notify:page")
    @GetMapping("/page")
    public CommonResult page(
            @RequestParam(value = "pageNum") Integer pageNum,
            @RequestParam(value = "pageSize") Integer pageSize,
            TmsNotify notify) {
        CommonPage<TmsNotify> page = tmsNotifyService.page(PageUtil.buildPage(pageNum, pageSize), notify);
        return CommonResult.success(page);
    }

    @RequiresPermissions("tms:notify:query")
    @GetMapping({"/{id}"})
    public CommonResult get(@PathVariable(value = "id") String id) {
        return CommonResult.success(tmsNotifyService.getById(id));
    }

    @RequiresPermissions("tms:notify:add")
    @OperateLog("通知新增")
    @PostMapping
    public CommonResult add(@Validated @RequestBody TmsNotify notify) {
        return CommonResult.success(tmsNotifyService.insert(notify));
    }

    @RequiresPermissions("tms:notify:edit")
    @OperateLog("通知管理")
    @PutMapping
    public CommonResult edit(@Validated @RequestBody TmsNotify notify) {
        return CommonResult.success(tmsNotifyService.update(notify));
    }

    @RequiresPermissions("tms:notify:remove")
    @OperateLog("通知删除")
    @DeleteMapping("/delete/{ids}")
    public CommonResult remove(@PathVariable String[] ids) {
        return CommonResult.success(tmsNotifyService.deleteByIds(ids));
    }


    @RequiresPermissions("tms:notify:record:page")
    @GetMapping("/record/page")
    public CommonResult pageRecord(
            @RequestParam(value = "pageNum") Integer pageNum,
            @RequestParam(value = "pageSize") Integer pageSize,
            TdsNotifyRecord tmsNotifyRecord) {
        CommonPage<TdsNotifyRecord> page = tmsNotifyService.pageRecord(PageUtil.buildPage(pageNum, pageSize), tmsNotifyRecord);
        return CommonResult.success(page);
    }

    @RequiresPermissions("tms:notify:record:distributor:page")
    @GetMapping("/record/distributor/page")
    public CommonResult pageDistributorForNotify(
            @RequestParam(value = "pageNum") Integer pageNum,
            @RequestParam(value = "pageSize") Integer pageSize,
            TdsTenant tmsDistributor) {
        CommonPage<TdsTenant> page = tmsNotifyService.pageDistributorForNotify(PageUtil.buildPage(pageNum, pageSize), tmsDistributor);
        return CommonResult.success(page);
    }

    @RequiresPermissions("tms:notify:record:add")
    @OperateLog("通知记录新增")
    @PostMapping("/record")
    public CommonResult insertRecord(@Validated @RequestBody TdsNotifyRecord tmsNotifyRecord) {
        return CommonResult.success(tmsNotifyService.insertRecord(tmsNotifyRecord));
    }

    @RequiresPermissions("tms:notify:record:remove")
    @OperateLog("通知记录删除")
    @DeleteMapping("/record/delete/{ids}")
    public CommonResult removeRecord(@PathVariable String[] ids) {
        return CommonResult.success(tmsNotifyService.deleteRecordByIds(ids));
    }


}
