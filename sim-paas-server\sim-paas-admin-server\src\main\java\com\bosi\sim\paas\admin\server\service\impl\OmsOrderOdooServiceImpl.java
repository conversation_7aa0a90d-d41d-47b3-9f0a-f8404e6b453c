package com.bosi.sim.paas.admin.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bosi.sim.paas.common.core.utils.IdUtils;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.dao.enums.sds.SdsOrderDeliveryStatusEnum;
import com.bosi.sim.paas.dao.enums.sds.SdsOrderStatusEnum;
import com.bosi.sim.paas.dao.mapper.oms.OmsOrderDeliveryMapper;
import com.bosi.sim.paas.dao.mapper.oms.OmsOrderItemMapper;
import com.bosi.sim.paas.dao.mapper.oms.OmsOrderMapper;
import com.bosi.sim.paas.dao.model.sds.OmsOrder;
import com.bosi.sim.paas.dao.model.sds.OmsOrderDelivery;
import com.bosi.sim.paas.dao.model.sds.OmsOrderItem;
import com.bosi.sim.paas.admin.server.dao.OmsOrderDeliveryDao;
import com.bosi.sim.paas.admin.server.service.OmsOrderOdooService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
@Slf4j
public class OmsOrderOdooServiceImpl implements OmsOrderOdooService {

    @Autowired
    private OmsOrderMapper orderMapper;

    @Autowired
    private OmsOrderItemMapper omsOrderItemMapper;

    @Autowired
    private OmsOrderDeliveryMapper omsOrderDeliveryMapper;

    @Autowired
    private OmsOrderDeliveryDao omsOrderDeliveryDao;

    @Override
    public void deliverySuccess(List<JSONObject> data) {
        data.forEach(jsonObject -> {
            try {
                List<JSONObject> order_lines = jsonObject.getList("order_lines", JSONObject.class);
                String orderSn = jsonObject.getString("customer_order_ref");
                if (CollUtil.isEmpty(order_lines) || StringUtils.isEmpty(orderSn)) {
                    log.error("the customer_order_ref or order_lines is empty:{}", jsonObject.toJSONString());
                    return;
                }
                LambdaQueryWrapper<OmsOrder> orderQueryWrapper = new LambdaQueryWrapper<>();
                orderQueryWrapper.eq(OmsOrder::getOrderSn, orderSn);
                OmsOrder order = orderMapper.selectOne(orderQueryWrapper);
                if (order == null) {
                    log.error("can not find order by ordeSn:{}", orderSn);
                    return;
                }
                if (!order.getStatus().equals(SdsOrderStatusEnum.WAITDELIVERY.getOrderStatus()) ||
                        !order.getStatus().equals(SdsOrderStatusEnum.DELIVERYED.getOrderStatus())) {
                    log.error("the order status is error:{}", orderSn);
                    return;
                }
                Set<String> skuCodeSet = Sets.newHashSet();
                order_lines.forEach(orderLine -> skuCodeSet.add(orderLine.getString("product_code")));
                LambdaQueryWrapper<OmsOrderItem> orderItemQueryWrapper = new LambdaQueryWrapper<>();
                orderItemQueryWrapper.in(OmsOrderItem::getProductSkuCode, skuCodeSet);
                orderItemQueryWrapper.eq(OmsOrderItem::getOrderId, order.getId());
                List<OmsOrderItem> orderItemList = omsOrderItemMapper.selectList(orderItemQueryWrapper);
                if (CollUtil.isEmpty(orderItemList)) {
                    log.error("the order item is empty:{}", orderSn);
                    return;
                }

                Map<String, OmsOrderItem> omsOrderItemMap = Maps.uniqueIndex(orderItemList, OmsOrderItem::getProductSkuCode);
                List<OmsOrderDelivery> orderDeliveryList = Lists.newArrayList();
                for (JSONObject order_line : order_lines) {
                    OmsOrderItem orderItem = omsOrderItemMap.get(order_line.getString("product_code"));
                    if (orderItem == null) {
                        log.error("the order item is empty:{}", order_line.getString("product_code"));
                        continue;
                    }
                    if (!order_line.getString("status").equals("delivered")) {
                        log.error("the order_line status is {}", order_line.getString("status"));
                        continue;
                    }
                    OmsOrderDelivery omsOrderDelivery = new OmsOrderDelivery();
                    omsOrderDelivery.setId(IdUtils.fastSimpleUUID());
                    omsOrderDelivery.setOrderId(orderItem.getOrderId());
                    omsOrderDelivery.setProductSkuId(orderItem.getProductSkuId());
                    omsOrderDelivery.setProductId(orderItem.getProductId());
                    omsOrderDelivery.setOrderItemId(orderItem.getId());
                    omsOrderDelivery.setDeliveryNumber(order_line.getInteger("delivered_qty"));
                    omsOrderDelivery.setDeliverySn(order_line.getString("tracking_number"));
                    omsOrderDelivery.setDeliveryStatus(SdsOrderDeliveryStatusEnum.DELIVERED.getOrderDeliveryStatus());
                    orderDeliveryList.add(omsOrderDelivery);
                }
                omsOrderDeliveryDao.insertList(orderDeliveryList);
                handlerDeliveryInfo(order, orderItemList);
            } catch (Exception e) {
                log.error("odooDeliverySuccess error", e);
            }
        });
    }

    private void handlerDeliveryInfo(OmsOrder orderDetail, List<OmsOrderItem> orderItemList) {
        if (isAllDelivered(orderDetail, orderItemList)) {
            orderDetail.setStatus(SdsOrderStatusEnum.DELIVERYED.getOrderStatus());
            orderDetail.setDeliveryTime(new Date());
            orderMapper.updateById(orderDetail);
        }
    }

    private boolean isAllDelivered(OmsOrder orderDetail, List<OmsOrderItem> orderItemList) {
        LambdaQueryWrapper<OmsOrderDelivery> deliveryLambdaQueryWrapper = new LambdaQueryWrapper<>();
        deliveryLambdaQueryWrapper.eq(OmsOrderDelivery::getOrderId, orderDetail.getId());
        List<OmsOrderDelivery> orderDeliveryList = omsOrderDeliveryMapper.selectList(deliveryLambdaQueryWrapper);
        Map<String, Integer> deliveryNumberMap = Maps.newHashMap();
        orderDeliveryList.forEach(omsOrderDelivery -> {
            Integer deliveryNumber = deliveryNumberMap.get(omsOrderDelivery.getOrderItemId());
            if (deliveryNumber == null) {
                deliveryNumber = 0;
            }
            deliveryNumberMap.put(omsOrderDelivery.getOrderItemId(), deliveryNumber + omsOrderDelivery.getDeliveryNumber());
        });
        for (OmsOrderItem omsOrderItem : orderItemList) {
            Integer realDelivery = deliveryNumberMap.get(omsOrderItem.getId());
            if (realDelivery == null || realDelivery < omsOrderItem.getProductQuantity()) {
                return false;
            }
        }
        return true;
    }
}
