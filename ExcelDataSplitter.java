import java.io.*;
import java.util.*;

public class ExcelDataSplitter {
    
    public static void main(String[] args) {
        System.out.println("=== Excel MCCMNC数据分裂工具 ===");
        
        try {
            // 检查输入文件
            String inputFile = "doc/test.xlsx";
            File excelFile = new File(inputFile);
            
            if (!excelFile.exists()) {
                System.err.println("错误: 找不到文件 " + inputFile);
                return;
            }
            
            System.out.println("找到Excel文件: " + inputFile);
            System.out.println("文件大小: " + excelFile.length() + " 字节");
            
            // 由于没有外部库，我们需要先将Excel转换为CSV
            String csvFile = "doc/test.csv";
            String outputCsvFile = "doc/test1.csv";
            
            System.out.println("\n请按以下步骤操作：");
            System.out.println("1. 打开Excel文件: " + inputFile);
            System.out.println("2. 另存为CSV格式: " + csvFile);
            System.out.println("3. 重新运行此程序");
            
            // 检查CSV文件是否存在
            File csvInputFile = new File(csvFile);
            if (!csvInputFile.exists()) {
                System.out.println("\n等待CSV文件创建完成后重新运行程序...");
                return;
            }
            
            // 处理CSV文件
            System.out.println("\n开始处理CSV文件...");
            boolean success = processCsvFile(csvFile, outputCsvFile);
            
            if (success) {
                System.out.println("\n处理完成！");
                System.out.println("请将 " + outputCsvFile + " 导入Excel并另存为 doc/test1.xlsx");
            }
            
        } catch (Exception e) {
            System.err.println("处理过程中出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public static boolean processCsvFile(String inputFile, String outputFile) {
        try {
            List<String[]> allRows = new ArrayList<>();
            int mccmncColumnIndex = -1;
            int originalRowCount = 0;
            int splitRowCount = 0;
            
            // 读取CSV文件
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(new FileInputStream(inputFile), "UTF-8"))) {
                
                String line;
                boolean isFirstLine = true;
                
                while ((line = reader.readLine()) != null) {
                    originalRowCount++;
                    String[] columns = parseCSVLine(line);
                    
                    if (isFirstLine) {
                        // 处理表头
                        for (int i = 0; i < columns.length; i++) {
                            if ("MCCMNC".equalsIgnoreCase(columns[i].trim())) {
                                mccmncColumnIndex = i;
                                break;
                            }
                        }
                        
                        if (mccmncColumnIndex == -1) {
                            System.err.println("错误: 未找到MCCMNC列");
                            System.out.println("可用的列: " + Arrays.toString(columns));
                            return false;
                        }
                        
                        System.out.println("找到MCCMNC列，索引: " + mccmncColumnIndex);
                        allRows.add(columns);
                        isFirstLine = false;
                        continue;
                    }
                    
                    // 处理数据行
                    if (mccmncColumnIndex < columns.length) {
                        String mccmncValue = columns[mccmncColumnIndex];
                        
                        if (mccmncValue != null && mccmncValue.contains("/")) {
                            // 分割包含'/'的数据
                            String[] parts = mccmncValue.split("/");
                            splitRowCount++;
                            
                            System.out.println("行 " + originalRowCount + ": '" + 
                                             mccmncValue + "' 分裂为 " + parts.length + " 行");
                            
                            // 为每个分割后的值创建新行
                            for (String part : parts) {
                                String[] newRow = columns.clone();
                                newRow[mccmncColumnIndex] = part.trim();
                                allRows.add(newRow);
                            }
                        } else {
                            // 不包含'/'的行直接添加
                            allRows.add(columns);
                        }
                    } else {
                        // 如果该行的列数不够，直接添加
                        allRows.add(columns);
                    }
                }
            }
            
            // 写入结果文件
            try (PrintWriter writer = new PrintWriter(
                    new OutputStreamWriter(new FileOutputStream(outputFile), "UTF-8"))) {
                
                for (String[] row : allRows) {
                    writer.println(formatCSVLine(row));
                }
            }
            
            System.out.println("\n=== 处理结果 ===");
            System.out.println("原始数据行数: " + originalRowCount);
            System.out.println("分裂了 " + splitRowCount + " 行包含'/'的数据");
            System.out.println("处理后数据行数: " + allRows.size());
            System.out.println("结果已保存到: " + outputFile);
            
            return true;
            
        } catch (IOException e) {
            System.err.println("文件处理错误: " + e.getMessage());
            return false;
        }
    }
    
    // 解析CSV行（简单实现）
    private static String[] parseCSVLine(String line) {
        List<String> fields = new ArrayList<>();
        StringBuilder currentField = new StringBuilder();
        boolean inQuotes = false;
        
        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);
            
            if (c == '"') {
                if (inQuotes && i + 1 < line.length() && line.charAt(i + 1) == '"') {
                    // 转义的引号
                    currentField.append('"');
                    i++; // 跳过下一个引号
                } else {
                    inQuotes = !inQuotes;
                }
            } else if (c == ',' && !inQuotes) {
                fields.add(currentField.toString());
                currentField = new StringBuilder();
            } else {
                currentField.append(c);
            }
        }
        
        fields.add(currentField.toString());
        return fields.toArray(new String[0]);
    }
    
    // 格式化CSV行
    private static String formatCSVLine(String[] fields) {
        StringBuilder line = new StringBuilder();
        
        for (int i = 0; i < fields.length; i++) {
            if (i > 0) {
                line.append(",");
            }
            
            String field = fields[i];
            if (field.contains(",") || field.contains("\"") || field.contains("\n")) {
                // 需要引号包围
                line.append("\"").append(field.replace("\"", "\"\"")).append("\"");
            } else {
                line.append(field);
            }
        }
        
        return line.toString();
    }
}
