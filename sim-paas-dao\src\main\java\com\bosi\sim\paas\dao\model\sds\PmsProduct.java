package com.bosi.esim.mall.dao.model.pms;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.esim.mall.dao.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@TableName("pms_product")
public class PmsProduct extends BaseEntity {

    /**
     * 以下是基础信息
     */
    private String brandId;

    private String categoryId;

    private String productName;

    @ApiModelProperty(value = "货号")
    private String productSn;

    @ApiModelProperty(value = "副标题")
    private String subTitle;

    @ApiModelProperty(value = "价格")
    private BigDecimal price;

    @ApiModelProperty(value = "原始价格")
    private BigDecimal originalPrice;

    private String pic;

    @ApiModelProperty(value = "商品描述")
    private String description;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "单位")
    private String unit;

    private String keywords;

    private String note;

    @ApiModelProperty(value = "画册图片，连产品图片限制为5张，以逗号分割")
    private String albumPics;

    private String detailTitle;

    private String detailDesc;

    @ApiModelProperty(value = "产品详情网页内容")
    private String detailHtml;

    @ApiModelProperty(value = "移动端网页详情")
    private String detailMobileHtml;

    @ApiModelProperty(value = "商品重量，默认为克")
    private BigDecimal weight;

    @ApiModelProperty(value = "是否虚拟商品")
    private Boolean whetherVirtually;

    @ApiModelProperty(value = "是否上架")
    private Boolean whetherPublish;


    /**
     * 以下是库存信息
     */
    private String attributeId;

    /**
     * 以下是营销信息
     */
    @ApiModelProperty(value = "赠送的成长值")
    private Integer giftGrowth;

    @ApiModelProperty(value = "赠送的积分")
    private Integer giftPoint;

    @ApiModelProperty(value = "以逗号分割的产品服务：1->无忧退货；2->快速退款；3->免费包邮")
    private String serviceIds;

    @ApiModelProperty(value = "促销类型：0->无促销；1->折扣类型；2->满减类型；")
    private Integer promotionType;

    @ApiModelProperty(value = "促销开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date promotionStartTime;

    @ApiModelProperty(value = "促销结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date promotionEndTime;

    @ApiModelProperty(value = "促销会员限购数量")
    private Integer promotionPerMemberLimit;

    @ApiModelProperty(value = "品牌名称")
    @TableField(exist = false)
    private String brandName;

    @ApiModelProperty(value = "商品分类名称")
    @TableField(exist = false)
    private String categoryName;

    @ApiModelProperty(value = "总销量")
    @TableField(exist = false)
    private Integer totalSale;

    @ApiModelProperty(value = "总库存")
    @TableField(exist = false)
    private Integer totalStock;

    //商品库存信息
    @TableField(exist = false)
    private List<PmsProductSku> productSkuList;

    //商品打折信息
    @TableField(exist = false)
    private List<PmsProductLadder> productLadderList;

    //商品满减信息
    @TableField(exist = false)
    private List<PmsProductFullReduction> productFullReductionList;

    @TableField(exist = false)
    private List<PmsProductAttributeType> productAttributeTypeList;

}
