package com.bosi.sim.paas.tenant.server.controller;

import com.alibaba.fastjson2.JSONObject;
import com.bosi.sim.paas.common.core.api.BizCode;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.exception.CommonResult;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.common.optlog.annotation.OperateLog;
import com.bosi.sim.paas.common.security.auth.CurrentAuthorization;
import com.bosi.sim.paas.common.security.util.PasswordUtils;
import com.bosi.sim.paas.dao.model.tds.TdsUser;
import com.bosi.sim.paas.tenant.server.service.TmsUserService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 个人信息 业务处理
 */
@RestController
@Api(tags = "TmsUserProfileController", value = "用户信息")
@RequestMapping("/tms/user/profile")
public class TmsUserProfileController {
    @Autowired
    private TmsUserService userService;

    /**
     * 个人信息
     */
    @GetMapping
    public CommonResult profile() {
        String username = CurrentAuthorization.getUsername();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("user", userService.selectUserByUserName(username));
        jsonObject.put("roleGroup", userService.selectUserRoleGroup(username));
        return CommonResult.success(jsonObject);
    }

    /**
     * 修改用户
     */
    @OperateLog("个人信息修改")
    @PutMapping
    public CommonResult updateProfile(@RequestBody TdsUser user) {
        TdsUser currentUser = new TdsUser();
        currentUser.setId(CurrentAuthorization.getUserId());
        currentUser.setNickName(user.getNickName());
        currentUser.setEmail(user.getEmail());
        currentUser.setPhone(user.getPhone());
        currentUser.setSex(user.getSex());
        if (StringUtils.isNotEmpty(user.getPhone()) && !userService.checkPhoneUnique(CurrentAuthorization.getUserId(), user.getPhone())) {
            throw BizException.build(BizCode.USER_PHONE_ALREADY_EXISTED);
        }
        if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(CurrentAuthorization.getUserId(), user.getEmail())) {
            throw BizException.build(BizCode.USER_EMAIL_ALREADY_EXISTED);
        }
        userService.updateUserProfile(currentUser);
        return CommonResult.success();
    }

    /**
     * 重置密码
     */
    @OperateLog("修改密码")
    @PutMapping("/updatePwd")
    public CommonResult updatePwd(String oldPassword, String newPassword) {
        String username = CurrentAuthorization.getUsername();
        TdsUser user = userService.selectUserByUserName(username);
        String password = user.getPassword();
        if (!PasswordUtils.matchesPassword(oldPassword, password)) {
            throw BizException.build(BizCode.USER_OLD_PASSWORD_ERROR);
        }
        newPassword = PasswordUtils.encryptPassword(newPassword);
        userService.resetUserPwd(username, newPassword);
        return CommonResult.success();
    }

}
