<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bosi.sim.paas.admin.server.dao.TmsMenuDao">

    <select id="selectMenuList" resultType="com.bosi.sim.paas.dao.model.tds.TdsMenu">
		select * from tms_menu
		<where>
			<if test="true">
				AND whether_delete = false
			</if>
			<if test="menuName != null and menuName != ''">
				AND menu_name like concat('%', #{menuName}, '%')
			</if>
			<if test="whetherVisible != null">
				AND whether_visible = #{whetherVisible}
			</if>
			<if test="whetherEnable != null">
				AND whether_enable = #{whetherEnable}
			</if>
		</where>
		order by parent_id, order_num
	</select>

	<select id="selectMenuTreeAll" resultType="com.bosi.sim.paas.dao.model.tds.TdsMenu">
		select
			distinct m.id,
			m.parent_id,
			m.menu_name,
			m.path,
			m.component,
			m.query,
			m.whether_visible,
			m.whether_enable,
			m.perms,
			m.whether_frame,
			m.menu_type,
			m.icon,
			m.order_num,
			m.create_time
		from
		    tms_menu m
		where
		    m.menu_type in ('M', 'C')
		and
		    m.whether_enable = true
		and
		    m.whether_delete = false
		order by m.parent_id, m.order_num
	</select>

	<select id="selectMenuListByUserId" resultType="com.bosi.sim.paas.dao.model.tds.TdsMenu">
		select
			distinct m.id,
			m.parent_id,
			m.menu_name,
			m.path,
			m.component,
			m.query,
			m.whether_visible,
			m.whether_enable,
			m.perms,
			m.whether_frame,
			m.menu_type,
			m.icon,
			m.order_num,
			m.create_time
		from tms_menu m
		left join tms_role_menu rm on m.id = rm.menu_id
		left join tms_user_role ur on rm.role_id = ur.role_id
		left join tms_role ro on ur.role_id = ro.id
		where ur.user_id = #{userId}
		<if test="true">
			and m.whether_delete = false
			and rm.whether_delete = false
			and ur.whether_delete = false
			and ro.whether_delete = false
		</if>
		<if test="menuName != null and menuName != ''">
			and m.menu_name like concat('%', #{menuName}, '%')
		</if>
		<if test="whetherVisible != null">
			and m.whether_visible = #{whetherVisible}
		</if>
		<if test="whetherEnable != null">
			and m.whether_enable = #{whetherEnable}
		</if>
		order by m.parent_id, m.order_num
	</select>

    <select id="selectMenuTreeByUserId" parameterType="String" resultType="com.bosi.sim.paas.dao.model.tds.TdsMenu">
		select
			distinct m.id,
			m.parent_id,
			m.menu_name,
			m.path,
			m.component,
			m.query,
			m.whether_visible,
			m.whether_enable,
			m.perms,
			m.whether_frame,
			m.menu_type,
			m.icon,
			m.order_num,
			m.create_time
		from tms_menu m
		left join tms_role_menu rm on m.id = rm.menu_id
		left join tms_user_role ur on rm.role_id = ur.role_id
		left join tms_role ro on ur.role_id = ro.id
		left join tms_user u on ur.user_id = u.id
		where
		    u.id = #{userId}
		and
		    m.menu_type in ('M', 'C')
		and
		    m.whether_enable = true
		and
			ro.whether_enable = true
		and
		    m.whether_delete = false
		and
			rm.whether_delete = false
		and
			ur.whether_delete = false
		and
			ro.whether_delete = false
		and
			u.whether_delete = false
		order by m.parent_id, m.order_num
	</select>

	<select id="selectMenuListByRoleId" resultType="com.bosi.sim.paas.dao.model.tds.TdsMenu">
		select
			m.id,
			m.parent_id,
			m.menu_name,
			m.path,
			m.component,
			m.query,
			m.whether_visible,
			m.whether_enable,
			m.perms,
			m.whether_frame,
			m.menu_type,
			m.icon,
			m.order_num,
			m.create_time
		from tms_menu m
        left join tms_role_menu rm on m.id = rm.menu_id
        where
            m.whether_delete = false
        and
			rm.whether_delete = false
		and
            rm.role_id = #{roleId}
        and
            m.id not in (select m.parent_id from tms_menu m inner join tms_role_menu rm on m.id = rm.menu_id and rm.role_id = #{roleId} and rm.whether_delete = false and m.whether_delete = false)
		order by m.parent_id, m.order_num
	</select>

	<select id="selectMenuPerms" resultType="String">
		select distinct m.perms
		from tms_menu m
		left join tms_role_menu rm on m.id = rm.menu_id
		left join tms_user_role ur on rm.role_id = ur.role_id
		and m.whether_delete = false
		and rm.whether_delete = false
		and ur.whether_delete = false
	</select>

	<select id="selectMenuPermsByUserId" parameterType="String" resultType="String">
		select
		    distinct m.perms
		from tms_menu m
		left join tms_role_menu rm on m.id = rm.menu_id
		left join tms_user_role ur on rm.role_id = ur.role_id
		left join tms_role r on r.id = ur.role_id
		where
		    m.whether_enable = true
		and
		    r.whether_enable = true
		and
		    ur.user_id = #{userId}
		and
		    m.whether_delete = false
		and
			rm.whether_delete = false
		and
			ur.whether_delete = false
		and
			r.whether_delete = false
	</select>

	<select id="selectMenuPermsByRoleId" parameterType="String" resultType="String">
		select
		    distinct m.perms
		from
		    tms_menu m
		left join
		    tms_role_menu rm on m.id = rm.menu_id
		where
		    m.whether_enable = true
		and
		    rm.role_id = #{roleId}
		and
		    m.whether_delete = false
		and
			rm.whether_delete = false
	</select>

	<select id="hasChildByMenuId" resultType="Integer">
	    select count(1) from tms_menu where parent_id = #{menuId} and whether_delete = false
	</select>

	<select id="checkMenuNameUnique" resultType="com.bosi.sim.paas.dao.model.tds.TdsMenu">
		select *
		from tms_menu
		where menu_name=#{menuName} and parent_id = #{parentId} and whether_delete = false limit 1
	</select>

</mapper>
