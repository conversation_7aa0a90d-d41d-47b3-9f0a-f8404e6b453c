package com.bosi.sim.paas.dao.enums.sds;


public enum SdsReturnApplyStatusEnum {

    WAIT_APPLY(0, "待处理"),

    IN_RETURN(1, "退货中"),

    APPLY_OK(2, "已完成"),

    APPLY_REFUSE(3, "已拒绝");


    private Integer returnApplyStatus;

    private String desc;

    SdsReturnApplyStatusEnum(Integer returnApplyStatus, String desc) {
        this.returnApplyStatus = returnApplyStatus;
        this.desc = desc;
    }

    public Integer getReturnApplyStatus() {
        return returnApplyStatus;
    }

    public String getDesc() {
        return desc;
    }
}
