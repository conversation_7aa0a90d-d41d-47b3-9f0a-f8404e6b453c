{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';\n\n// API 基础配置\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api';\n\n// 创建 axios 实例\nconst apiClient: AxiosInstance = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 请求拦截器 - 添加认证 token\napiClient.interceptors.request.use(\n  (config) => {\n    // 检查是否在浏览器环境\n    if (typeof window !== 'undefined') {\n      const token = localStorage.getItem('token');\n      if (token && config.headers) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器 - 处理错误\napiClient.interceptors.response.use(\n  (response) => response,\n  (error) => {\n    // 检查是否在浏览器环境\n    if (typeof window !== 'undefined') {\n      // 处理 401 未授权错误\n      if (error.response && error.response.status === 401) {\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n      }\n    }\n    return Promise.reject(error);\n  }\n);\n\n// 通用 API 请求方法\nexport const api = {\n  get: <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {\n    return apiClient.get<T, AxiosResponse<T>>(url, config).then((response) => response.data);\n  },\n  post: <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n    return apiClient.post<T, AxiosResponse<T>>(url, data, config).then((response) => response.data);\n  },\n  put: <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n    return apiClient.put<T, AxiosResponse<T>>(url, data, config).then((response) => response.data);\n  },\n  delete: <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {\n    return apiClient.delete<T, AxiosResponse<T>>(url, config).then((response) => response.data);\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;AAGqB;AAHrB;;AAEA,WAAW;AACX,MAAM,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;AAExD,cAAc;AACd,MAAM,YAA2B,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC5C,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,qBAAqB;AACrB,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAChC,CAAC;IACC,aAAa;IACb,wCAAmC;QACjC,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,SAAS,OAAO,OAAO,EAAE;YAC3B,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;QAClD;IACF;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,eAAe;AACf,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC,WAAa,UACd,CAAC;IACC,aAAa;IACb,wCAAmC;QACjC,eAAe;QACf,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;YACnD,aAAa,UAAU,CAAC;YACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM,MAAM;IACjB,KAAK,CAAI,KAAa;QACpB,OAAO,UAAU,GAAG,CAAsB,KAAK,QAAQ,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IACzF;IACA,MAAM,CAAI,KAAa,MAAY;QACjC,OAAO,UAAU,IAAI,CAAsB,KAAK,MAAM,QAAQ,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IAChG;IACA,KAAK,CAAI,KAAa,MAAY;QAChC,OAAO,UAAU,GAAG,CAAsB,KAAK,MAAM,QAAQ,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IAC/F;IACA,QAAQ,CAAI,KAAa;QACvB,OAAO,UAAU,MAAM,CAAsB,KAAK,QAAQ,IAAI,CAAC,CAAC,WAAa,SAAS,IAAI;IAC5F;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/services/auth.service.ts"], "sourcesContent": ["import { AuthResponse, LoginCredentials, User } from '@/types';\nimport api from './api';\n\n// 模拟数据\nconst mockAdminUser: User = {\n  id: '1',\n  username: 'admin',\n  name: '管理员',\n  email: '<EMAIL>',\n  avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',\n  status: 'active',\n  roleIds: ['1'],\n  createdAt: new Date().toISOString(),\n  updatedAt: new Date().toISOString(),\n};\n\nconst mockToken = 'mock-jwt-token';\n\n// 认证服务\nexport const authService = {\n  // 登录\n  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.post<AuthResponse>('/auth/login', credentials);\n\n      // 模拟登录逻辑\n      if (credentials.username === 'admin' && credentials.password === 'admin123') {\n        // 模拟延迟\n        await new Promise(resolve => setTimeout(resolve, 500));\n\n        // 保存 token 到本地存储\n        if (typeof window !== 'undefined') {\n          localStorage.setItem('token', mockToken);\n        }\n\n        return {\n          token: mockToken,\n          user: mockAdminUser\n        };\n      } else {\n        throw new Error('用户名或密码错误');\n      }\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 登出\n  logout: async (): Promise<void> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.post<void>('/auth/logout');\n\n      // 模拟登出逻辑\n      if (typeof window !== 'undefined') {\n        localStorage.removeItem('token');\n      }\n      await new Promise(resolve => setTimeout(resolve, 300));\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取当前用户信息\n  getCurrentUser: async (): Promise<User> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.get<User>('/auth/me');\n\n      // 模拟获取用户信息\n      if (typeof window === 'undefined') {\n        throw new Error('服务器端不支持此操作');\n      }\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        throw new Error('未登录');\n      }\n\n      // 模拟延迟\n      await new Promise(resolve => setTimeout(resolve, 300));\n\n      return mockAdminUser;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 检查是否已登录\n  isAuthenticated: (): boolean => {\n    if (typeof window === 'undefined') {\n      return false; // 服务器端渲染时返回 false\n    }\n    return !!localStorage.getItem('token');\n  }\n};\n\nexport default authService;\n"], "names": [], "mappings": ";;;;AAGA,OAAO;AACP,MAAM,gBAAsB;IAC1B,IAAI;IACJ,UAAU;IACV,MAAM;IACN,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,SAAS;QAAC;KAAI;IACd,WAAW,IAAI,OAAO,WAAW;IACjC,WAAW,IAAI,OAAO,WAAW;AACnC;AAEA,MAAM,YAAY;AAGX,MAAM,cAAc;IACzB,KAAK;IACL,OAAO,OAAO;QACZ,IAAI;YACF,oBAAoB;YACpB,6DAA6D;YAE7D,SAAS;YACT,IAAI,YAAY,QAAQ,KAAK,WAAW,YAAY,QAAQ,KAAK,YAAY;gBAC3E,OAAO;gBACP,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,iBAAiB;gBACjB,wCAAmC;oBACjC,aAAa,OAAO,CAAC,SAAS;gBAChC;gBAEA,OAAO;oBACL,OAAO;oBACP,MAAM;gBACR;YACF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,KAAK;IACL,QAAQ;QACN,IAAI;YACF,oBAAoB;YACpB,yCAAyC;YAEzC,SAAS;YACT,wCAAmC;gBACjC,aAAa,UAAU,CAAC;YAC1B;YACA,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,WAAW;IACX,gBAAgB;QACd,IAAI;YACF,oBAAoB;YACpB,oCAAoC;YAEpC,WAAW;YACX,uCAAmC;;YAEnC;YAEA,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO;YACP,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,UAAU;IACV,iBAAiB;QACf,uCAAmC;;QAEnC;QACA,OAAO,CAAC,CAAC,aAAa,OAAO,CAAC;IAChC;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/services/user.service.ts"], "sourcesContent": ["import { PaginatedResponse, PaginationParams, User, UserCreateInput, UserUpdateInput } from '@/types';\nimport api from './api';\n\n// 模拟数据\nconst mockUsers: User[] = [\n  {\n    id: '1',\n    username: 'admin',\n    name: '管理员',\n    email: '<EMAIL>',\n    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',\n    status: 'active',\n    roleIds: ['1'],\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: '2',\n    username: 'user1',\n    name: '普通用户1',\n    email: '<EMAIL>',\n    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user1',\n    status: 'active',\n    roleIds: ['2'],\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: '3',\n    username: 'user2',\n    name: '普通用户2',\n    email: '<EMAIL>',\n    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user2',\n    status: 'inactive',\n    roleIds: ['2'],\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n];\n\n// 用户服务\nexport const userService = {\n  // 获取用户列表\n  getUsers: async (params: PaginationParams): Promise<PaginatedResponse<User>> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.get<PaginatedResponse<User>>('/users', { params });\n      \n      // 模拟获取用户列表\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const { page, pageSize } = params;\n      const startIndex = (page - 1) * pageSize;\n      const endIndex = startIndex + pageSize;\n      const paginatedUsers = mockUsers.slice(startIndex, endIndex);\n      \n      return {\n        data: paginatedUsers,\n        total: mockUsers.length,\n        page,\n        pageSize\n      };\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取单个用户\n  getUser: async (id: string): Promise<User> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.get<User>(`/users/${id}`);\n      \n      // 模拟获取单个用户\n      await new Promise(resolve => setTimeout(resolve, 300));\n      \n      const user = mockUsers.find(user => user.id === id);\n      if (!user) {\n        throw new Error('用户不存在');\n      }\n      \n      return user;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 创建用户\n  createUser: async (userData: UserCreateInput): Promise<User> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.post<User>('/users', userData);\n      \n      // 模拟创建用户\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const newUser: User = {\n        id: String(mockUsers.length + 1),\n        username: userData.username,\n        name: userData.name,\n        email: userData.email,\n        status: userData.status,\n        roleIds: userData.roleIds,\n        avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${userData.username}`,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n      };\n      \n      // 在实际应用中，这里会由后端处理\n      mockUsers.push(newUser);\n      \n      return newUser;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 更新用户\n  updateUser: async (userData: UserUpdateInput): Promise<User> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.put<User>(`/users/${userData.id}`, userData);\n      \n      // 模拟更新用户\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const userIndex = mockUsers.findIndex(user => user.id === userData.id);\n      if (userIndex === -1) {\n        throw new Error('用户不存在');\n      }\n      \n      const updatedUser = {\n        ...mockUsers[userIndex],\n        ...userData,\n        updatedAt: new Date().toISOString(),\n      };\n      \n      // 在实际应用中，这里会由后端处理\n      mockUsers[userIndex] = updatedUser;\n      \n      return updatedUser;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 删除用户\n  deleteUser: async (id: string): Promise<void> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.delete<void>(`/users/${id}`);\n      \n      // 模拟删除用户\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const userIndex = mockUsers.findIndex(user => user.id === id);\n      if (userIndex === -1) {\n        throw new Error('用户不存在');\n      }\n      \n      // 在实际应用中，这里会由后端处理\n      mockUsers.splice(userIndex, 1);\n    } catch (error) {\n      throw error;\n    }\n  },\n};\n\nexport default userService;\n"], "names": [], "mappings": ";;;;AAGA,OAAO;AACP,MAAM,YAAoB;IACxB;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,SAAS;YAAC;SAAI;QACd,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,SAAS;YAAC;SAAI;QACd,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,UAAU;QACV,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,SAAS;YAAC;SAAI;QACd,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;CACD;AAGM,MAAM,cAAc;IACzB,SAAS;IACT,UAAU,OAAO;QACf,IAAI;YACF,oBAAoB;YACpB,iEAAiE;YAEjE,WAAW;YACX,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG;YAC3B,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;YAChC,MAAM,WAAW,aAAa;YAC9B,MAAM,iBAAiB,UAAU,KAAK,CAAC,YAAY;YAEnD,OAAO;gBACL,MAAM;gBACN,OAAO,UAAU,MAAM;gBACvB;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,SAAS;IACT,SAAS,OAAO;QACd,IAAI;YACF,oBAAoB;YACpB,wCAAwC;YAExC,WAAW;YACX,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAChD,IAAI,CAAC,MAAM;gBACT,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,OAAO;IACP,YAAY,OAAO;QACjB,IAAI;YACF,oBAAoB;YACpB,6CAA6C;YAE7C,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,UAAgB;gBACpB,IAAI,OAAO,UAAU,MAAM,GAAG;gBAC9B,UAAU,SAAS,QAAQ;gBAC3B,MAAM,SAAS,IAAI;gBACnB,OAAO,SAAS,KAAK;gBACrB,QAAQ,SAAS,MAAM;gBACvB,SAAS,SAAS,OAAO;gBACzB,QAAQ,CAAC,gDAAgD,EAAE,SAAS,QAAQ,EAAE;gBAC9E,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,kBAAkB;YAClB,UAAU,IAAI,CAAC;YAEf,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,OAAO;IACP,YAAY,OAAO;QACjB,IAAI;YACF,oBAAoB;YACpB,2DAA2D;YAE3D,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,YAAY,UAAU,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,SAAS,EAAE;YACrE,IAAI,cAAc,CAAC,GAAG;gBACpB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,cAAc;gBAClB,GAAG,SAAS,CAAC,UAAU;gBACvB,GAAG,QAAQ;gBACX,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,kBAAkB;YAClB,SAAS,CAAC,UAAU,GAAG;YAEvB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,OAAO;IACP,YAAY,OAAO;QACjB,IAAI;YACF,oBAAoB;YACpB,2CAA2C;YAE3C,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,YAAY,UAAU,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC1D,IAAI,cAAc,CAAC,GAAG;gBACpB,MAAM,IAAI,MAAM;YAClB;YAEA,kBAAkB;YAClB,UAAU,MAAM,CAAC,WAAW;QAC9B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/services/role.service.ts"], "sourcesContent": ["import { PaginatedResponse, PaginationParams, Role, RoleCreateInput, RoleUpdateInput } from '@/types';\nimport api from './api';\n\n// 模拟数据\nconst mockRoles: Role[] = [\n  {\n    id: '1',\n    name: '超级管理员',\n    description: '拥有所有权限',\n    menuIds: ['1', '2', '3', '4', '5', '6', '7', '8'],\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: '2',\n    name: '普通用户',\n    description: '拥有基本权限',\n    menuIds: ['1', '2'],\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: '3',\n    name: '访客',\n    description: '只有查看权限',\n    menuIds: ['1'],\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n];\n\n// 角色服务\nexport const roleService = {\n  // 获取角色列表\n  getRoles: async (params: PaginationParams): Promise<PaginatedResponse<Role>> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.get<PaginatedResponse<Role>>('/roles', { params });\n      \n      // 模拟获取角色列表\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const { page, pageSize } = params;\n      const startIndex = (page - 1) * pageSize;\n      const endIndex = startIndex + pageSize;\n      const paginatedRoles = mockRoles.slice(startIndex, endIndex);\n      \n      return {\n        data: paginatedRoles,\n        total: mockRoles.length,\n        page,\n        pageSize\n      };\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取所有角色（不分页）\n  getAllRoles: async (): Promise<Role[]> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.get<Role[]>('/roles/all');\n      \n      // 模拟获取所有角色\n      await new Promise(resolve => setTimeout(resolve, 300));\n      \n      return mockRoles;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取单个角色\n  getRole: async (id: string): Promise<Role> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.get<Role>(`/roles/${id}`);\n      \n      // 模拟获取单个角色\n      await new Promise(resolve => setTimeout(resolve, 300));\n      \n      const role = mockRoles.find(role => role.id === id);\n      if (!role) {\n        throw new Error('角色不存在');\n      }\n      \n      return role;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 创建角色\n  createRole: async (roleData: RoleCreateInput): Promise<Role> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.post<Role>('/roles', roleData);\n      \n      // 模拟创建角色\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const newRole: Role = {\n        id: String(mockRoles.length + 1),\n        name: roleData.name,\n        description: roleData.description,\n        menuIds: roleData.menuIds,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n      };\n      \n      // 在实际应用中，这里会由后端处理\n      mockRoles.push(newRole);\n      \n      return newRole;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 更新角色\n  updateRole: async (roleData: RoleUpdateInput): Promise<Role> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.put<Role>(`/roles/${roleData.id}`, roleData);\n      \n      // 模拟更新角色\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const roleIndex = mockRoles.findIndex(role => role.id === roleData.id);\n      if (roleIndex === -1) {\n        throw new Error('角色不存在');\n      }\n      \n      const updatedRole = {\n        ...mockRoles[roleIndex],\n        ...roleData,\n        updatedAt: new Date().toISOString(),\n      };\n      \n      // 在实际应用中，这里会由后端处理\n      mockRoles[roleIndex] = updatedRole;\n      \n      return updatedRole;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 删除角色\n  deleteRole: async (id: string): Promise<void> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.delete<void>(`/roles/${id}`);\n      \n      // 模拟删除角色\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const roleIndex = mockRoles.findIndex(role => role.id === id);\n      if (roleIndex === -1) {\n        throw new Error('角色不存在');\n      }\n      \n      // 在实际应用中，这里会由后端处理\n      mockRoles.splice(roleIndex, 1);\n    } catch (error) {\n      throw error;\n    }\n  },\n};\n\nexport default roleService;\n"], "names": [], "mappings": ";;;;AAGA,OAAO;AACP,MAAM,YAAoB;IACxB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,SAAS;YAAC;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;SAAI;QACjD,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,SAAS;YAAC;YAAK;SAAI;QACnB,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,SAAS;YAAC;SAAI;QACd,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;CACD;AAGM,MAAM,cAAc;IACzB,SAAS;IACT,UAAU,OAAO;QACf,IAAI;YACF,oBAAoB;YACpB,iEAAiE;YAEjE,WAAW;YACX,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG;YAC3B,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;YAChC,MAAM,WAAW,aAAa;YAC9B,MAAM,iBAAiB,UAAU,KAAK,CAAC,YAAY;YAEnD,OAAO;gBACL,MAAM;gBACN,OAAO,UAAU,MAAM;gBACvB;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,cAAc;IACd,aAAa;QACX,IAAI;YACF,oBAAoB;YACpB,wCAAwC;YAExC,WAAW;YACX,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,SAAS;IACT,SAAS,OAAO;QACd,IAAI;YACF,oBAAoB;YACpB,wCAAwC;YAExC,WAAW;YACX,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAChD,IAAI,CAAC,MAAM;gBACT,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,OAAO;IACP,YAAY,OAAO;QACjB,IAAI;YACF,oBAAoB;YACpB,6CAA6C;YAE7C,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,UAAgB;gBACpB,IAAI,OAAO,UAAU,MAAM,GAAG;gBAC9B,MAAM,SAAS,IAAI;gBACnB,aAAa,SAAS,WAAW;gBACjC,SAAS,SAAS,OAAO;gBACzB,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,kBAAkB;YAClB,UAAU,IAAI,CAAC;YAEf,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,OAAO;IACP,YAAY,OAAO;QACjB,IAAI;YACF,oBAAoB;YACpB,2DAA2D;YAE3D,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,YAAY,UAAU,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,SAAS,EAAE;YACrE,IAAI,cAAc,CAAC,GAAG;gBACpB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,cAAc;gBAClB,GAAG,SAAS,CAAC,UAAU;gBACvB,GAAG,QAAQ;gBACX,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,kBAAkB;YAClB,SAAS,CAAC,UAAU,GAAG;YAEvB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,OAAO;IACP,YAAY,OAAO;QACjB,IAAI;YACF,oBAAoB;YACpB,2CAA2C;YAE3C,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,YAAY,UAAU,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC1D,IAAI,cAAc,CAAC,GAAG;gBACpB,MAAM,IAAI,MAAM;YAClB;YAEA,kBAAkB;YAClB,UAAU,MAAM,CAAC,WAAW;QAC9B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/services/menu.service.ts"], "sourcesContent": ["import { Menu, MenuCreateInput, MenuUpdateInput, PaginatedResponse, PaginationParams } from '@/types';\nimport api from './api';\n\n// 模拟数据\nconst mockMenus: Menu[] = [\n  {\n    id: '1',\n    name: '仪表盘',\n    path: '/dashboard',\n    icon: 'dashboard',\n    parentId: null,\n    order: 1,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: '2',\n    name: '系统管理',\n    path: '/system',\n    icon: 'setting',\n    parentId: null,\n    order: 2,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: '3',\n    name: '用户管理',\n    path: '/system/users',\n    icon: 'user',\n    parentId: '2',\n    order: 1,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: '4',\n    name: '角色管理',\n    path: '/system/roles',\n    icon: 'team',\n    parentId: '2',\n    order: 2,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n  {\n    id: '5',\n    name: '菜单管理',\n    path: '/system/menus',\n    icon: 'menu',\n    parentId: '2',\n    order: 3,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  },\n];\n\n// 构建菜单树\nconst buildMenuTree = (menus: Menu[]): Menu[] => {\n  const menuMap = new Map<string, Menu>();\n  const result: Menu[] = [];\n\n  // 先将所有菜单放入 Map 中\n  menus.forEach(menu => {\n    menuMap.set(menu.id, { ...menu, children: [] });\n  });\n\n  // 构建树形结构\n  menus.forEach(menu => {\n    const menuWithChildren = menuMap.get(menu.id)!;\n    \n    if (menu.parentId === null) {\n      // 根菜单\n      result.push(menuWithChildren);\n    } else {\n      // 子菜单\n      const parentMenu = menuMap.get(menu.parentId);\n      if (parentMenu) {\n        if (!parentMenu.children) {\n          parentMenu.children = [];\n        }\n        parentMenu.children.push(menuWithChildren);\n      }\n    }\n  });\n\n  // 对菜单进行排序\n  const sortMenus = (menus: Menu[]): Menu[] => {\n    return menus\n      .sort((a, b) => a.order - b.order)\n      .map(menu => ({\n        ...menu,\n        children: menu.children ? sortMenus(menu.children) : undefined,\n      }));\n  };\n\n  return sortMenus(result);\n};\n\n// 菜单服务\nexport const menuService = {\n  // 获取菜单列表\n  getMenus: async (params: PaginationParams): Promise<PaginatedResponse<Menu>> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.get<PaginatedResponse<Menu>>('/menus', { params });\n      \n      // 模拟获取菜单列表\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const { page, pageSize } = params;\n      const startIndex = (page - 1) * pageSize;\n      const endIndex = startIndex + pageSize;\n      const paginatedMenus = mockMenus.slice(startIndex, endIndex);\n      \n      return {\n        data: paginatedMenus,\n        total: mockMenus.length,\n        page,\n        pageSize\n      };\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取菜单树\n  getMenuTree: async (): Promise<Menu[]> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.get<Menu[]>('/menus/tree');\n      \n      // 模拟获取菜单树\n      await new Promise(resolve => setTimeout(resolve, 300));\n      \n      return buildMenuTree(mockMenus);\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 获取单个菜单\n  getMenu: async (id: string): Promise<Menu> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.get<Menu>(`/menus/${id}`);\n      \n      // 模拟获取单个菜单\n      await new Promise(resolve => setTimeout(resolve, 300));\n      \n      const menu = mockMenus.find(menu => menu.id === id);\n      if (!menu) {\n        throw new Error('菜单不存在');\n      }\n      \n      return menu;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 创建菜单\n  createMenu: async (menuData: MenuCreateInput): Promise<Menu> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.post<Menu>('/menus', menuData);\n      \n      // 模拟创建菜单\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const newMenu: Menu = {\n        id: String(mockMenus.length + 1),\n        name: menuData.name,\n        path: menuData.path,\n        icon: menuData.icon,\n        parentId: menuData.parentId || null,\n        order: menuData.order || mockMenus.length + 1,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n      };\n      \n      // 在实际应用中，这里会由后端处理\n      mockMenus.push(newMenu);\n      \n      return newMenu;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 更新菜单\n  updateMenu: async (menuData: MenuUpdateInput): Promise<Menu> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.put<Menu>(`/menus/${menuData.id}`, menuData);\n      \n      // 模拟更新菜单\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const menuIndex = mockMenus.findIndex(menu => menu.id === menuData.id);\n      if (menuIndex === -1) {\n        throw new Error('菜单不存在');\n      }\n      \n      const updatedMenu = {\n        ...mockMenus[menuIndex],\n        ...menuData,\n        updatedAt: new Date().toISOString(),\n      };\n      \n      // 在实际应用中，这里会由后端处理\n      mockMenus[menuIndex] = updatedMenu;\n      \n      return updatedMenu;\n    } catch (error) {\n      throw error;\n    }\n  },\n\n  // 删除菜单\n  deleteMenu: async (id: string): Promise<void> => {\n    try {\n      // 实际项目中，这里会调用后端 API\n      // return api.delete<void>(`/menus/${id}`);\n      \n      // 模拟删除菜单\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      const menuIndex = mockMenus.findIndex(menu => menu.id === id);\n      if (menuIndex === -1) {\n        throw new Error('菜单不存在');\n      }\n      \n      // 在实际应用中，这里会由后端处理\n      mockMenus.splice(menuIndex, 1);\n    } catch (error) {\n      throw error;\n    }\n  },\n};\n\nexport default menuService;\n"], "names": [], "mappings": ";;;;AAGA,OAAO;AACP,MAAM,YAAoB;IACxB;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;QACP,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;QACP,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;QACP,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;QACP,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,MAAM;QACN,UAAU;QACV,OAAO;QACP,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;CACD;AAED,QAAQ;AACR,MAAM,gBAAgB,CAAC;IACrB,MAAM,UAAU,IAAI;IACpB,MAAM,SAAiB,EAAE;IAEzB,iBAAiB;IACjB,MAAM,OAAO,CAAC,CAAA;QACZ,QAAQ,GAAG,CAAC,KAAK,EAAE,EAAE;YAAE,GAAG,IAAI;YAAE,UAAU,EAAE;QAAC;IAC/C;IAEA,SAAS;IACT,MAAM,OAAO,CAAC,CAAA;QACZ,MAAM,mBAAmB,QAAQ,GAAG,CAAC,KAAK,EAAE;QAE5C,IAAI,KAAK,QAAQ,KAAK,MAAM;YAC1B,MAAM;YACN,OAAO,IAAI,CAAC;QACd,OAAO;YACL,MAAM;YACN,MAAM,aAAa,QAAQ,GAAG,CAAC,KAAK,QAAQ;YAC5C,IAAI,YAAY;gBACd,IAAI,CAAC,WAAW,QAAQ,EAAE;oBACxB,WAAW,QAAQ,GAAG,EAAE;gBAC1B;gBACA,WAAW,QAAQ,CAAC,IAAI,CAAC;YAC3B;QACF;IACF;IAEA,UAAU;IACV,MAAM,YAAY,CAAC;QACjB,OAAO,MACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACZ,GAAG,IAAI;gBACP,UAAU,KAAK,QAAQ,GAAG,UAAU,KAAK,QAAQ,IAAI;YACvD,CAAC;IACL;IAEA,OAAO,UAAU;AACnB;AAGO,MAAM,cAAc;IACzB,SAAS;IACT,UAAU,OAAO;QACf,IAAI;YACF,oBAAoB;YACpB,iEAAiE;YAEjE,WAAW;YACX,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG;YAC3B,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;YAChC,MAAM,WAAW,aAAa;YAC9B,MAAM,iBAAiB,UAAU,KAAK,CAAC,YAAY;YAEnD,OAAO;gBACL,MAAM;gBACN,OAAO,UAAU,MAAM;gBACvB;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,QAAQ;IACR,aAAa;QACX,IAAI;YACF,oBAAoB;YACpB,yCAAyC;YAEzC,UAAU;YACV,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,OAAO,cAAc;QACvB,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,SAAS;IACT,SAAS,OAAO;QACd,IAAI;YACF,oBAAoB;YACpB,wCAAwC;YAExC,WAAW;YACX,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAChD,IAAI,CAAC,MAAM;gBACT,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,OAAO;IACP,YAAY,OAAO;QACjB,IAAI;YACF,oBAAoB;YACpB,6CAA6C;YAE7C,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,UAAgB;gBACpB,IAAI,OAAO,UAAU,MAAM,GAAG;gBAC9B,MAAM,SAAS,IAAI;gBACnB,MAAM,SAAS,IAAI;gBACnB,MAAM,SAAS,IAAI;gBACnB,UAAU,SAAS,QAAQ,IAAI;gBAC/B,OAAO,SAAS,KAAK,IAAI,UAAU,MAAM,GAAG;gBAC5C,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,kBAAkB;YAClB,UAAU,IAAI,CAAC;YAEf,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,OAAO;IACP,YAAY,OAAO;QACjB,IAAI;YACF,oBAAoB;YACpB,2DAA2D;YAE3D,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,YAAY,UAAU,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,SAAS,EAAE;YACrE,IAAI,cAAc,CAAC,GAAG;gBACpB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,cAAc;gBAClB,GAAG,SAAS,CAAC,UAAU;gBACvB,GAAG,QAAQ;gBACX,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,kBAAkB;YAClB,SAAS,CAAC,UAAU,GAAG;YAEvB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,OAAO;IACP,YAAY,OAAO;QACjB,IAAI;YACF,oBAAoB;YACpB,2CAA2C;YAE3C,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,YAAY,UAAU,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC1D,IAAI,cAAc,CAAC,GAAG;gBACpB,MAAM,IAAI,MAAM;YAClB;YAEA,kBAAkB;YAClB,UAAU,MAAM,CAAC,WAAW;QAC9B,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 715, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/services/index.ts"], "sourcesContent": ["export * from './api';\nexport * from './auth.service';\nexport * from './user.service';\nexport * from './role.service';\nexport * from './menu.service';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport { authService } from '@/services';\nimport { LoginCredentials, User } from '@/types';\nimport { createContext, useCallback, useContext, useEffect, useState } from 'react';\n\ninterface AuthContextType {\n  user: User | null;\n  loading: boolean;\n  error: string | null;\n  login: (credentials: LoginCredentials) => Promise<void>;\n  logout: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const AuthProvider = ({ children }: { children: React.ReactNode }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // 获取当前用户信息\n  const fetchCurrentUser = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // 检查是否已登录\n      if (!authService.isAuthenticated()) {\n        setUser(null);\n        setLoading(false);\n        return;\n      }\n\n      // 获取当前用户信息\n      const currentUser = await authService.getCurrentUser();\n      setUser(currentUser);\n    } catch (error) {\n      console.error('获取用户信息失败:', error);\n      setError('获取用户信息失败');\n      // 清除无效的 token\n      if (typeof window !== 'undefined') {\n        localStorage.removeItem('token');\n      }\n      setUser(null);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n\n  // 登录\n  const login = async (credentials: LoginCredentials) => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // 调用登录 API\n      const response = await authService.login(credentials);\n\n      // 保存用户信息\n      setUser(response.user);\n    } catch (error: any) {\n      console.error('登录失败:', error);\n      setError(error.message || '登录失败');\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 登出\n  const logout = async () => {\n    try {\n      setLoading(true);\n\n      // 调用登出 API\n      await authService.logout();\n\n      // 清除用户信息\n      setUser(null);\n    } catch (error) {\n      console.error('登出失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 初始化时获取用户信息\n  useEffect(() => {\n    fetchCurrentUser();\n  }, [fetchCurrentUser]);\n\n  return (\n    <AuthContext.Provider value={{ user, loading, error, login, logout }}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\n// 自定义钩子，用于在组件中访问认证上下文\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport default AuthContext;\n"], "names": [], "mappings": ";;;;;;AAEA;AAAA;AAEA;;;AAJA;;;AAcA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,WAAW;IACX,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YACnC,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,UAAU;gBACV,IAAI,CAAC,qIAAA,CAAA,cAAW,CAAC,eAAe,IAAI;oBAClC,QAAQ;oBACR,WAAW;oBACX;gBACF;gBAEA,WAAW;gBACX,MAAM,cAAc,MAAM,qIAAA,CAAA,cAAW,CAAC,cAAc;gBACpD,QAAQ;YACV,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,aAAa;gBAC3B,SAAS;gBACT,cAAc;gBACd,wCAAmC;oBACjC,aAAa,UAAU,CAAC;gBAC1B;gBACA,QAAQ;YACV,SAAU;gBACR,WAAW;YACb;QACF;qDAAG,EAAE;IAEL,KAAK;IACL,MAAM,QAAQ,OAAO;QACnB,IAAI;YACF,WAAW;YACX,SAAS;YAET,WAAW;YACX,MAAM,WAAW,MAAM,qIAAA,CAAA,cAAW,CAAC,KAAK,CAAC;YAEzC,SAAS;YACT,QAAQ,SAAS,IAAI;QACvB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,SAAS;YACvB,SAAS,MAAM,OAAO,IAAI;YAC1B,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,KAAK;IACL,MAAM,SAAS;QACb,IAAI;YACF,WAAW;YAEX,WAAW;YACX,MAAM,qIAAA,CAAA,cAAW,CAAC,MAAM;YAExB,SAAS;YACT,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,SAAS;QACzB,SAAU;YACR,WAAW;QACb;IACF;IAEA,aAAa;IACb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG;QAAC;KAAiB;IAErB,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;YAAE;YAAM;YAAS;YAAO;YAAO;QAAO;kBAChE;;;;;;AAGP;GAjFa;KAAA;AAoFN,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa;uCAQE", "debugId": null}}]}