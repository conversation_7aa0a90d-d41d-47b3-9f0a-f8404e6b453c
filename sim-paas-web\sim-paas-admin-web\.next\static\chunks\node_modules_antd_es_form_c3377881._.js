(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/antd/es/form/context.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FormContext": (()=>FormContext),
    "FormItemInputContext": (()=>FormItemInputContext),
    "FormItemPrefixContext": (()=>FormItemPrefixContext),
    "FormProvider": (()=>FormProvider),
    "NoFormStyle": (()=>NoFormStyle),
    "NoStyleItemContext": (()=>NoStyleItemContext),
    "VariantContext": (()=>VariantContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$field$2d$form$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-field-form/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$field$2d$form$2f$es$2f$FormContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-field-form/es/FormContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$omit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/omit.js [app-client] (ecmascript)");
"use client";
;
;
;
const FormContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])({
    labelAlign: 'right',
    vertical: false,
    itemRef: ()=>{}
});
const NoStyleItemContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
const FormProvider = (props)=>{
    const providerProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$omit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props, [
        'prefixCls'
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$field$2d$form$2f$es$2f$FormContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormProvider"], Object.assign({}, providerProps));
};
const FormItemPrefixContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])({
    prefixCls: ''
});
const FormItemInputContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])({});
if ("TURBOPACK compile-time truthy", 1) {
    FormItemInputContext.displayName = 'FormItemInputContext';
}
const NoFormStyle = (_ref)=>{
    let { children, status, override } = _ref;
    const formItemInputContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(FormItemInputContext);
    const newFormItemInputContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "NoFormStyle.useMemo[newFormItemInputContext]": ()=>{
            const newContext = Object.assign({}, formItemInputContext);
            if (override) {
                delete newContext.isFormItemInput;
            }
            if (status) {
                delete newContext.status;
                delete newContext.hasFeedback;
                delete newContext.feedbackIcon;
            }
            return newContext;
        }
    }["NoFormStyle.useMemo[newFormItemInputContext]"], [
        status,
        override,
        formItemInputContext
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(FormItemInputContext.Provider, {
        value: newFormItemInputContext
    }, children);
};
const VariantContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
}}),
"[project]/node_modules/antd/es/form/hooks/useDebounce.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useDebounce)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
function useDebounce(value) {
    const [cacheValue, setCacheValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(value);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useDebounce.useEffect": ()=>{
            const timeout = setTimeout({
                "useDebounce.useEffect.timeout": ()=>{
                    setCacheValue(value);
                }
            }["useDebounce.useEffect.timeout"], value.length ? 0 : 10);
            return ({
                "useDebounce.useEffect": ()=>{
                    clearTimeout(timeout);
                }
            })["useDebounce.useEffect"];
        }
    }["useDebounce.useEffect"], [
        value
    ]);
    return cacheValue;
}
}}),
"[project]/node_modules/antd/es/form/style/explain.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const genFormValidateMotionStyle = (token)=>{
    const { componentCls } = token;
    const helpCls = `${componentCls}-show-help`;
    const helpItemCls = `${componentCls}-show-help-item`;
    return {
        [helpCls]: {
            // Explain holder
            transition: `opacity ${token.motionDurationFast} ${token.motionEaseInOut}`,
            '&-appear, &-enter': {
                opacity: 0,
                '&-active': {
                    opacity: 1
                }
            },
            '&-leave': {
                opacity: 1,
                '&-active': {
                    opacity: 0
                }
            },
            // Explain
            [helpItemCls]: {
                overflow: 'hidden',
                transition: `height ${token.motionDurationFast} ${token.motionEaseInOut},
                     opacity ${token.motionDurationFast} ${token.motionEaseInOut},
                     transform ${token.motionDurationFast} ${token.motionEaseInOut} !important`,
                [`&${helpItemCls}-appear, &${helpItemCls}-enter`]: {
                    transform: `translateY(-5px)`,
                    opacity: 0,
                    '&-active': {
                        transform: 'translateY(0)',
                        opacity: 1
                    }
                },
                [`&${helpItemCls}-leave-active`]: {
                    transform: `translateY(-5px)`
                }
            }
        }
    };
};
const __TURBOPACK__default__export__ = genFormValidateMotionStyle;
}}),
"[project]/node_modules/antd/es/form/style/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "prepareComponentToken": (()=>prepareComponentToken),
    "prepareToken": (()=>prepareToken)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs/es/util/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$motion$2f$collapse$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__genCollapseMotion$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/style/motion/collapse.js [app-client] (ecmascript) <export default as genCollapseMotion>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$motion$2f$zoom$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/style/motion/zoom.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/util/genStyleUtils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__ = __turbopack_context__.i("[project]/node_modules/@ant-design/cssinjs-utils/es/util/statistic.js [app-client] (ecmascript) <export merge as mergeToken>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$style$2f$explain$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/style/explain.js [app-client] (ecmascript)");
;
;
;
;
;
const resetForm = (token)=>({
        legend: {
            display: 'block',
            width: '100%',
            marginBottom: token.marginLG,
            padding: 0,
            color: token.colorTextDescription,
            fontSize: token.fontSizeLG,
            lineHeight: 'inherit',
            border: 0,
            borderBottom: `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.lineWidth)} ${token.lineType} ${token.colorBorder}`
        },
        'input[type="search"]': {
            boxSizing: 'border-box'
        },
        // Position radios and checkboxes better
        'input[type="radio"], input[type="checkbox"]': {
            lineHeight: 'normal'
        },
        'input[type="file"]': {
            display: 'block'
        },
        // Make range inputs behave like textual form controls
        'input[type="range"]': {
            display: 'block',
            width: '100%'
        },
        // Make multiple select elements height not fixed
        'select[multiple], select[size]': {
            height: 'auto'
        },
        // Focus for file, radio, and checkbox
        [`input[type='file']:focus,
  input[type='radio']:focus,
  input[type='checkbox']:focus`]: {
            outline: 0,
            boxShadow: `0 0 0 ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.controlOutlineWidth)} ${token.controlOutline}`
        },
        // Adjust output element
        output: {
            display: 'block',
            paddingTop: 15,
            color: token.colorText,
            fontSize: token.fontSize,
            lineHeight: token.lineHeight
        }
    });
const genFormSize = (token, height)=>{
    const { formItemCls } = token;
    return {
        [formItemCls]: {
            [`${formItemCls}-label > label`]: {
                height
            },
            [`${formItemCls}-control-input`]: {
                minHeight: height
            }
        }
    };
};
const genFormStyle = (token)=>{
    const { componentCls } = token;
    return {
        [token.componentCls]: Object.assign(Object.assign(Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resetComponent"])(token)), resetForm(token)), {
            [`${componentCls}-text`]: {
                display: 'inline-block',
                paddingInlineEnd: token.paddingSM
            },
            // ================================================================
            // =                             Size                             =
            // ================================================================
            '&-small': Object.assign({}, genFormSize(token, token.controlHeightSM)),
            '&-large': Object.assign({}, genFormSize(token, token.controlHeightLG))
        })
    };
};
const genFormItemStyle = (token)=>{
    const { formItemCls, iconCls, rootPrefixCls, antCls, labelRequiredMarkColor, labelColor, labelFontSize, labelHeight, labelColonMarginInlineStart, labelColonMarginInlineEnd, itemMarginBottom } = token;
    return {
        [formItemCls]: Object.assign(Object.assign({}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resetComponent"])(token)), {
            marginBottom: itemMarginBottom,
            verticalAlign: 'top',
            '&-with-help': {
                transition: 'none'
            },
            [`&-hidden,
        &-hidden${antCls}-row`]: {
                // https://github.com/ant-design/ant-design/issues/26141
                display: 'none'
            },
            '&-has-warning': {
                [`${formItemCls}-split`]: {
                    color: token.colorError
                }
            },
            '&-has-error': {
                [`${formItemCls}-split`]: {
                    color: token.colorWarning
                }
            },
            // ==============================================================
            // =                            Label                           =
            // ==============================================================
            [`${formItemCls}-label`]: {
                flexGrow: 0,
                overflow: 'hidden',
                whiteSpace: 'nowrap',
                textAlign: 'end',
                verticalAlign: 'middle',
                '&-left': {
                    textAlign: 'start'
                },
                '&-wrap': {
                    overflow: 'unset',
                    lineHeight: token.lineHeight,
                    whiteSpace: 'unset'
                },
                '> label': {
                    position: 'relative',
                    display: 'inline-flex',
                    alignItems: 'center',
                    maxWidth: '100%',
                    height: labelHeight,
                    color: labelColor,
                    fontSize: labelFontSize,
                    [`> ${iconCls}`]: {
                        fontSize: token.fontSize,
                        verticalAlign: 'top'
                    },
                    [`&${formItemCls}-required`]: {
                        '&::before': {
                            display: 'inline-block',
                            marginInlineEnd: token.marginXXS,
                            color: labelRequiredMarkColor,
                            fontSize: token.fontSize,
                            fontFamily: 'SimSun, sans-serif',
                            lineHeight: 1,
                            content: '"*"'
                        },
                        [`&${formItemCls}-required-mark-hidden, &${formItemCls}-required-mark-optional`]: {
                            '&::before': {
                                display: 'none'
                            }
                        }
                    },
                    // Optional mark
                    [`${formItemCls}-optional`]: {
                        display: 'inline-block',
                        marginInlineStart: token.marginXXS,
                        color: token.colorTextDescription,
                        [`&${formItemCls}-required-mark-hidden`]: {
                            display: 'none'
                        }
                    },
                    // Optional mark
                    [`${formItemCls}-tooltip`]: {
                        color: token.colorTextDescription,
                        cursor: 'help',
                        writingMode: 'horizontal-tb',
                        marginInlineStart: token.marginXXS
                    },
                    '&::after': {
                        content: '":"',
                        position: 'relative',
                        marginBlock: 0,
                        marginInlineStart: labelColonMarginInlineStart,
                        marginInlineEnd: labelColonMarginInlineEnd
                    },
                    [`&${formItemCls}-no-colon::after`]: {
                        content: '"\\a0"'
                    }
                }
            },
            // ==============================================================
            // =                            Input                           =
            // ==============================================================
            [`${formItemCls}-control`]: {
                ['--ant-display']: 'flex',
                flexDirection: 'column',
                flexGrow: 1,
                [`&:first-child:not([class^="'${rootPrefixCls}-col-'"]):not([class*="' ${rootPrefixCls}-col-'"])`]: {
                    width: '100%'
                },
                '&-input': {
                    position: 'relative',
                    display: 'flex',
                    alignItems: 'center',
                    minHeight: token.controlHeight,
                    '&-content': {
                        flex: 'auto',
                        maxWidth: '100%'
                    }
                }
            },
            // ==============================================================
            // =                           Explain                          =
            // ==============================================================
            [formItemCls]: {
                '&-additional': {
                    display: 'flex',
                    flexDirection: 'column'
                },
                '&-explain, &-extra': {
                    clear: 'both',
                    color: token.colorTextDescription,
                    fontSize: token.fontSize,
                    lineHeight: token.lineHeight
                },
                '&-explain-connected': {
                    width: '100%'
                },
                '&-extra': {
                    minHeight: token.controlHeightSM,
                    transition: `color ${token.motionDurationMid} ${token.motionEaseOut}` // sync input color transition
                },
                '&-explain': {
                    '&-error': {
                        color: token.colorError
                    },
                    '&-warning': {
                        color: token.colorWarning
                    }
                }
            },
            [`&-with-help ${formItemCls}-explain`]: {
                height: 'auto',
                opacity: 1
            },
            // ==============================================================
            // =                        Feedback Icon                       =
            // ==============================================================
            [`${formItemCls}-feedback-icon`]: {
                fontSize: token.fontSize,
                textAlign: 'center',
                visibility: 'visible',
                animationName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$motion$2f$zoom$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zoomIn"],
                animationDuration: token.motionDurationMid,
                animationTimingFunction: token.motionEaseOutBack,
                pointerEvents: 'none',
                '&-success': {
                    color: token.colorSuccess
                },
                '&-error': {
                    color: token.colorError
                },
                '&-warning': {
                    color: token.colorWarning
                },
                '&-validating': {
                    color: token.colorPrimary
                }
            }
        })
    };
};
const genHorizontalStyle = (token, className)=>{
    const { formItemCls } = token;
    return {
        [`${className}-horizontal`]: {
            [`${formItemCls}-label`]: {
                flexGrow: 0
            },
            [`${formItemCls}-control`]: {
                flex: '1 1 0',
                // https://github.com/ant-design/ant-design/issues/32777
                // https://github.com/ant-design/ant-design/issues/33773
                minWidth: 0
            },
            // Do not change this to `ant-col-24`! `-24` match all the responsive rules
            // https://github.com/ant-design/ant-design/issues/32980
            // https://github.com/ant-design/ant-design/issues/34903
            // https://github.com/ant-design/ant-design/issues/44538
            [`${formItemCls}-label[class$='-24'], ${formItemCls}-label[class*='-24 ']`]: {
                [`& + ${formItemCls}-control`]: {
                    minWidth: 'unset'
                }
            }
        }
    };
};
const genInlineStyle = (token)=>{
    const { componentCls, formItemCls, inlineItemMarginBottom } = token;
    return {
        [`${componentCls}-inline`]: {
            display: 'flex',
            flexWrap: 'wrap',
            [formItemCls]: {
                flex: 'none',
                marginInlineEnd: token.margin,
                marginBottom: inlineItemMarginBottom,
                '&-row': {
                    flexWrap: 'nowrap'
                },
                [`> ${formItemCls}-label,
        > ${formItemCls}-control`]: {
                    display: 'inline-block',
                    verticalAlign: 'top'
                },
                [`> ${formItemCls}-label`]: {
                    flex: 'none'
                },
                [`${componentCls}-text`]: {
                    display: 'inline-block'
                },
                [`${formItemCls}-has-feedback`]: {
                    display: 'inline-block'
                }
            }
        }
    };
};
const makeVerticalLayoutLabel = (token)=>({
        padding: token.verticalLabelPadding,
        margin: token.verticalLabelMargin,
        whiteSpace: 'initial',
        textAlign: 'start',
        '> label': {
            margin: 0,
            '&::after': {
                // https://github.com/ant-design/ant-design/issues/43538
                visibility: 'hidden'
            }
        }
    });
const makeVerticalLayout = (token)=>{
    const { componentCls, formItemCls, rootPrefixCls } = token;
    return {
        [`${formItemCls} ${formItemCls}-label`]: makeVerticalLayoutLabel(token),
        // ref: https://github.com/ant-design/ant-design/issues/45122
        [`${componentCls}:not(${componentCls}-inline)`]: {
            [formItemCls]: {
                flexWrap: 'wrap',
                [`${formItemCls}-label, ${formItemCls}-control`]: {
                    // When developer pass `xs: { span }`,
                    // It should follow the `xs` screen config
                    // ref: https://github.com/ant-design/ant-design/issues/44386
                    [`&:not([class*=" ${rootPrefixCls}-col-xs"])`]: {
                        flex: '0 0 100%',
                        maxWidth: '100%'
                    }
                }
            }
        }
    };
};
const genVerticalStyle = (token)=>{
    const { componentCls, formItemCls, antCls } = token;
    return {
        [`${componentCls}-vertical`]: {
            [`${formItemCls}:not(${formItemCls}-horizontal)`]: {
                [`${formItemCls}-row`]: {
                    flexDirection: 'column'
                },
                [`${formItemCls}-label > label`]: {
                    height: 'auto'
                },
                [`${formItemCls}-control`]: {
                    width: '100%'
                },
                [`${formItemCls}-label,
        ${antCls}-col-24${formItemCls}-label,
        ${antCls}-col-xl-24${formItemCls}-label`]: makeVerticalLayoutLabel(token)
            }
        },
        [`@media (max-width: ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.screenXSMax)})`]: [
            makeVerticalLayout(token),
            {
                [componentCls]: {
                    [`${formItemCls}:not(${formItemCls}-horizontal)`]: {
                        [`${antCls}-col-xs-24${formItemCls}-label`]: makeVerticalLayoutLabel(token)
                    }
                }
            }
        ],
        [`@media (max-width: ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.screenSMMax)})`]: {
            [componentCls]: {
                [`${formItemCls}:not(${formItemCls}-horizontal)`]: {
                    [`${antCls}-col-sm-24${formItemCls}-label`]: makeVerticalLayoutLabel(token)
                }
            }
        },
        [`@media (max-width: ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.screenMDMax)})`]: {
            [componentCls]: {
                [`${formItemCls}:not(${formItemCls}-horizontal)`]: {
                    [`${antCls}-col-md-24${formItemCls}-label`]: makeVerticalLayoutLabel(token)
                }
            }
        },
        [`@media (max-width: ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.screenLGMax)})`]: {
            [componentCls]: {
                [`${formItemCls}:not(${formItemCls}-horizontal)`]: {
                    [`${antCls}-col-lg-24${formItemCls}-label`]: makeVerticalLayoutLabel(token)
                }
            }
        }
    };
};
const genItemVerticalStyle = (token)=>{
    const { formItemCls, antCls } = token;
    return {
        [`${formItemCls}-vertical`]: {
            [`${formItemCls}-row`]: {
                flexDirection: 'column'
            },
            [`${formItemCls}-label > label`]: {
                height: 'auto'
            },
            [`${formItemCls}-control`]: {
                width: '100%'
            }
        },
        [`${formItemCls}-vertical ${formItemCls}-label,
      ${antCls}-col-24${formItemCls}-label,
      ${antCls}-col-xl-24${formItemCls}-label`]: makeVerticalLayoutLabel(token),
        [`@media (max-width: ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.screenXSMax)})`]: [
            makeVerticalLayout(token),
            {
                [formItemCls]: {
                    [`${antCls}-col-xs-24${formItemCls}-label`]: makeVerticalLayoutLabel(token)
                }
            }
        ],
        [`@media (max-width: ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.screenSMMax)})`]: {
            [formItemCls]: {
                [`${antCls}-col-sm-24${formItemCls}-label`]: makeVerticalLayoutLabel(token)
            }
        },
        [`@media (max-width: ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.screenMDMax)})`]: {
            [formItemCls]: {
                [`${antCls}-col-md-24${formItemCls}-label`]: makeVerticalLayoutLabel(token)
            }
        },
        [`@media (max-width: ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2f$es$2f$util$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unit"])(token.screenLGMax)})`]: {
            [formItemCls]: {
                [`${antCls}-col-lg-24${formItemCls}-label`]: makeVerticalLayoutLabel(token)
            }
        }
    };
};
const prepareComponentToken = (token)=>({
        labelRequiredMarkColor: token.colorError,
        labelColor: token.colorTextHeading,
        labelFontSize: token.fontSize,
        labelHeight: token.controlHeight,
        labelColonMarginInlineStart: token.marginXXS / 2,
        labelColonMarginInlineEnd: token.marginXS,
        itemMarginBottom: token.marginLG,
        verticalLabelPadding: `0 0 ${token.paddingXS}px`,
        verticalLabelMargin: 0,
        inlineItemMarginBottom: 0
    });
const prepareToken = (token, rootPrefixCls)=>{
    const formToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$ant$2d$design$2f$cssinjs$2d$utils$2f$es$2f$util$2f$statistic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__merge__as__mergeToken$3e$__["mergeToken"])(token, {
        formItemCls: `${token.componentCls}-item`,
        rootPrefixCls
    });
    return formToken;
};
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genStyleHooks"])('Form', (token, _ref)=>{
    let { rootPrefixCls } = _ref;
    const formToken = prepareToken(token, rootPrefixCls);
    return [
        genFormStyle(formToken),
        genFormItemStyle(formToken),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$style$2f$explain$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(formToken),
        genHorizontalStyle(formToken, formToken.componentCls),
        genHorizontalStyle(formToken, formToken.formItemCls),
        genInlineStyle(formToken),
        genVerticalStyle(formToken),
        genItemVerticalStyle(formToken),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$motion$2f$collapse$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__genCollapseMotion$3e$__["genCollapseMotion"])(formToken),
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$style$2f$motion$2f$zoom$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zoomIn"]
    ];
}, prepareComponentToken, {
    // Let From style before the Grid
    // ref https://github.com/ant-design/ant-design/issues/44386
    order: -1000
});
}}),
"[project]/node_modules/antd/es/form/ErrorList.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$motion$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-motion/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$motion$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-motion/es/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$motion$2f$es$2f$CSSMotionList$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CSSMotionList$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-motion/es/CSSMotionList.js [app-client] (ecmascript) <export default as CSSMotionList>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$motion$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/motion.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useCSSVarCls$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/hooks/useCSSVarCls.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useDebounce$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/hooks/useDebounce.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/style/index.js [app-client] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
const EMPTY_LIST = [];
function toErrorEntity(error, prefix, errorStatus) {
    let index = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;
    return {
        key: typeof error === 'string' ? error : `${prefix}-${index}`,
        error,
        errorStatus
    };
}
const ErrorList = (_ref)=>{
    let { help, helpStatus, errors = EMPTY_LIST, warnings = EMPTY_LIST, className: rootClassName, fieldId, onVisibleChanged } = _ref;
    const { prefixCls } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormItemPrefixContext"]);
    const baseClassName = `${prefixCls}-item-explain`;
    const rootCls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useCSSVarCls$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls);
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, rootCls);
    const collapseMotion = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "ErrorList.useMemo[collapseMotion]": ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$motion$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls)
    }["ErrorList.useMemo[collapseMotion]"], [
        prefixCls
    ]);
    // We have to debounce here again since somewhere use ErrorList directly still need no shaking
    // ref: https://github.com/ant-design/ant-design/issues/36336
    const debounceErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useDebounce$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(errors);
    const debounceWarnings = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useDebounce$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(warnings);
    const fullKeyList = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "ErrorList.useMemo[fullKeyList]": ()=>{
            if (help !== undefined && help !== null) {
                return [
                    toErrorEntity(help, 'help', helpStatus)
                ];
            }
            return [].concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(debounceErrors.map({
                "ErrorList.useMemo[fullKeyList]": (error, index)=>toErrorEntity(error, 'error', 'error', index)
            }["ErrorList.useMemo[fullKeyList]"])), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(debounceWarnings.map({
                "ErrorList.useMemo[fullKeyList]": (warning, index)=>toErrorEntity(warning, 'warning', 'warning', index)
            }["ErrorList.useMemo[fullKeyList]"])));
        }
    }["ErrorList.useMemo[fullKeyList]"], [
        help,
        helpStatus,
        debounceErrors,
        debounceWarnings
    ]);
    const filledKeyFullKeyList = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "ErrorList.useMemo[filledKeyFullKeyList]": ()=>{
            const keysCount = {};
            fullKeyList.forEach({
                "ErrorList.useMemo[filledKeyFullKeyList]": (_ref2)=>{
                    let { key } = _ref2;
                    keysCount[key] = (keysCount[key] || 0) + 1;
                }
            }["ErrorList.useMemo[filledKeyFullKeyList]"]);
            return fullKeyList.map({
                "ErrorList.useMemo[filledKeyFullKeyList]": (entity, index)=>Object.assign(Object.assign({}, entity), {
                        key: keysCount[entity.key] > 1 ? `${entity.key}-fallback-${index}` : entity.key
                    })
            }["ErrorList.useMemo[filledKeyFullKeyList]"]);
        }
    }["ErrorList.useMemo[filledKeyFullKeyList]"], [
        fullKeyList
    ]);
    const helpProps = {};
    if (fieldId) {
        helpProps.id = `${fieldId}_help`;
    }
    return wrapCSSVar(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$motion$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"], {
        motionDeadline: collapseMotion.motionDeadline,
        motionName: `${prefixCls}-show-help`,
        visible: !!filledKeyFullKeyList.length,
        onVisibleChanged: onVisibleChanged
    }, (holderProps)=>{
        const { className: holderClassName, style: holderStyle } = holderProps;
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", Object.assign({}, helpProps, {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(baseClassName, holderClassName, cssVarCls, rootCls, rootClassName, hashId),
            style: holderStyle
        }), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$motion$2f$es$2f$CSSMotionList$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CSSMotionList$3e$__["CSSMotionList"], Object.assign({
            keys: filledKeyFullKeyList
        }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$motion$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls), {
            motionName: `${prefixCls}-show-help-item`,
            component: false
        }), (itemProps)=>{
            const { key, error, errorStatus, className: itemClassName, style: itemStyle } = itemProps;
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
                key: key,
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(itemClassName, {
                    [`${baseClassName}-${errorStatus}`]: errorStatus
                }),
                style: itemStyle
            }, error);
        }));
    }));
};
const __TURBOPACK__default__export__ = ErrorList;
}}),
"[project]/node_modules/antd/es/form/util.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// form item name black list.  in form ,you can use form.id get the form item element.
// use object hasOwnProperty will get better performance if black list is longer.
__turbopack_context__.s({
    "getFieldId": (()=>getFieldId),
    "getStatus": (()=>getStatus),
    "toArray": (()=>toArray)
});
const formItemNameBlackList = [
    'parentNode'
];
// default form item id prefix.
const defaultItemNamePrefixCls = 'form_item';
function toArray(candidate) {
    if (candidate === undefined || candidate === false) return [];
    return Array.isArray(candidate) ? candidate : [
        candidate
    ];
}
function getFieldId(namePath, formName) {
    if (!namePath.length) {
        return undefined;
    }
    const mergedId = namePath.join('_');
    if (formName) {
        return `${formName}_${mergedId}`;
    }
    const isIllegalName = formItemNameBlackList.includes(mergedId);
    return isIllegalName ? `${defaultItemNamePrefixCls}_${mergedId}` : mergedId;
}
function getStatus(errors, warnings, meta, defaultValidateStatus, hasFeedback, validateStatus) {
    let status = defaultValidateStatus;
    if (validateStatus !== undefined) {
        status = validateStatus;
    } else if (meta.validating) {
        status = 'validating';
    } else if (errors.length) {
        status = 'error';
    } else if (warnings.length) {
        status = 'warning';
    } else if (meta.touched || hasFeedback && meta.validated) {
        // success feedback should display when pass hasFeedback prop and current value is valid value
        status = 'success';
    }
    return status;
}
}}),
"[project]/node_modules/antd/es/form/hooks/useForm.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useForm)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$field$2d$form$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-field-form/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$field$2d$form$2f$es$2f$useForm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useForm$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-field-form/es/useForm.js [app-client] (ecmascript) <export default as useForm>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$findDOMNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/Dom/findDOMNode.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$scroll$2d$into$2d$view$2d$if$2d$needed$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/scroll-into-view-if-needed/dist/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$util$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/util.js [app-client] (ecmascript)");
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
;
;
;
;
;
function toNamePathStr(name) {
    const namePath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$util$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toArray"])(name);
    return namePath.join('_');
}
function getFieldDOMNode(name, wrapForm) {
    const field = wrapForm.getFieldInstance(name);
    const fieldDom = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$findDOMNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDOM"])(field);
    if (fieldDom) {
        return fieldDom;
    }
    const fieldId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$util$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFieldId"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$util$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toArray"])(name), wrapForm.__INTERNAL__.name);
    if (fieldId) {
        return document.getElementById(fieldId);
    }
}
function useForm(form) {
    const [rcForm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$field$2d$form$2f$es$2f$useForm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useForm$3e$__["useForm"])();
    const itemsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])({});
    const wrapForm = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useForm.useMemo[wrapForm]": ()=>form !== null && form !== void 0 ? form : Object.assign(Object.assign({}, rcForm), {
                __INTERNAL__: {
                    itemRef: {
                        "useForm.useMemo[wrapForm]": (name)=>({
                                "useForm.useMemo[wrapForm]": (node)=>{
                                    const namePathStr = toNamePathStr(name);
                                    if (node) {
                                        itemsRef.current[namePathStr] = node;
                                    } else {
                                        delete itemsRef.current[namePathStr];
                                    }
                                }
                            })["useForm.useMemo[wrapForm]"]
                    }["useForm.useMemo[wrapForm]"]
                },
                scrollToField: {
                    "useForm.useMemo[wrapForm]": function(name) {
                        let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
                        const { focus } = options, restOpt = __rest(options, [
                            "focus"
                        ]);
                        const node = getFieldDOMNode(name, wrapForm);
                        if (node) {
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$scroll$2d$into$2d$view$2d$if$2d$needed$2f$dist$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(node, Object.assign({
                                scrollMode: 'if-needed',
                                block: 'nearest'
                            }, restOpt));
                            // Focus if scroll success
                            if (focus) {
                                wrapForm.focusField(name);
                            }
                        }
                    }
                }["useForm.useMemo[wrapForm]"],
                focusField: {
                    "useForm.useMemo[wrapForm]": (name)=>{
                        var _a, _b;
                        const itemRef = wrapForm.getFieldInstance(name);
                        if (typeof (itemRef === null || itemRef === void 0 ? void 0 : itemRef.focus) === 'function') {
                            itemRef.focus();
                        } else {
                            (_b = (_a = getFieldDOMNode(name, wrapForm)) === null || _a === void 0 ? void 0 : _a.focus) === null || _b === void 0 ? void 0 : _b.call(_a);
                        }
                    }
                }["useForm.useMemo[wrapForm]"],
                getFieldInstance: {
                    "useForm.useMemo[wrapForm]": (name)=>{
                        const namePathStr = toNamePathStr(name);
                        return itemsRef.current[namePathStr];
                    }
                }["useForm.useMemo[wrapForm]"]
            })
    }["useForm.useMemo[wrapForm]"], [
        form,
        rcForm
    ]);
    return [
        wrapForm
    ];
}
}}),
"[project]/node_modules/antd/es/form/hooks/useFormWarning.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useFormWarning)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/warning.js [app-client] (ecmascript)");
;
;
const names = {};
function useFormWarning(_ref) {
    let { name } = _ref;
    const warning = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["devUseWarning"])('Form');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useFormWarning.useEffect": ()=>{
            if (name) {
                names[name] = (names[name] || 0) + 1;
                ("TURBOPACK compile-time truthy", 1) ? warning(names[name] <= 1, 'usage', 'There exist multiple Form with same `name`.') : ("TURBOPACK unreachable", undefined);
                return ({
                    "useFormWarning.useEffect": ()=>{
                        names[name] -= 1;
                    }
                })["useFormWarning.useEffect"];
            }
        }
    }["useFormWarning.useEffect"], [
        name
    ]);
}
}}),
"[project]/node_modules/antd/es/form/Form.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$field$2d$form$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-field-form/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$field$2d$form$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-field-form/es/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$DisabledContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/DisabledContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useCSSVarCls$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/hooks/useCSSVarCls.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useSize$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/hooks/useSize.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$SizeContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/SizeContext.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useForm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/hooks/useForm.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useFormWarning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/hooks/useFormWarning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$validateMessagesContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/validateMessagesContext.js [app-client] (ecmascript)");
"use client";
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
;
;
;
;
;
;
;
;
;
;
;
;
;
const InternalForm = (props, ref)=>{
    const contextDisabled = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$DisabledContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
    const { getPrefixCls, direction, requiredMark: contextRequiredMark, colon: contextColon, scrollToFirstError: contextScrollToFirstError, className: contextClassName, style: contextStyle } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useComponentConfig"])('form');
    const { prefixCls: customizePrefixCls, className, rootClassName, size, disabled = contextDisabled, form, colon, labelAlign, labelWrap, labelCol, wrapperCol, hideRequiredMark, layout = 'horizontal', scrollToFirstError, requiredMark, onFinishFailed, name, style, feedbackIcons, variant } = props, restFormProps = __rest(props, [
        "prefixCls",
        "className",
        "rootClassName",
        "size",
        "disabled",
        "form",
        "colon",
        "labelAlign",
        "labelWrap",
        "labelCol",
        "wrapperCol",
        "hideRequiredMark",
        "layout",
        "scrollToFirstError",
        "requiredMark",
        "onFinishFailed",
        "name",
        "style",
        "feedbackIcons",
        "variant"
    ]);
    const mergedSize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useSize$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(size);
    const contextValidateMessages = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$validateMessagesContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
    if ("TURBOPACK compile-time truthy", 1) {
        // eslint-disable-next-line react-hooks/rules-of-hooks
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useFormWarning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(props);
    }
    const mergedRequiredMark = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "InternalForm.useMemo[mergedRequiredMark]": ()=>{
            if (requiredMark !== undefined) {
                return requiredMark;
            }
            if (hideRequiredMark) {
                return false;
            }
            if (contextRequiredMark !== undefined) {
                return contextRequiredMark;
            }
            return true;
        }
    }["InternalForm.useMemo[mergedRequiredMark]"], [
        hideRequiredMark,
        requiredMark,
        contextRequiredMark
    ]);
    const mergedColon = colon !== null && colon !== void 0 ? colon : contextColon;
    const prefixCls = getPrefixCls('form', customizePrefixCls);
    // Style
    const rootCls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useCSSVarCls$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls);
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, rootCls);
    const formClassName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, `${prefixCls}-${layout}`, {
        [`${prefixCls}-hide-required-mark`]: mergedRequiredMark === false,
        // todo: remove in next major version
        [`${prefixCls}-rtl`]: direction === 'rtl',
        [`${prefixCls}-${mergedSize}`]: mergedSize
    }, cssVarCls, rootCls, hashId, contextClassName, className, rootClassName);
    const [wrapForm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useForm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(form);
    const { __INTERNAL__ } = wrapForm;
    __INTERNAL__.name = name;
    const formContextValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "InternalForm.useMemo[formContextValue]": ()=>({
                name,
                labelAlign,
                labelCol,
                labelWrap,
                wrapperCol,
                vertical: layout === 'vertical',
                colon: mergedColon,
                requiredMark: mergedRequiredMark,
                itemRef: __INTERNAL__.itemRef,
                form: wrapForm,
                feedbackIcons
            })
    }["InternalForm.useMemo[formContextValue]"], [
        name,
        labelAlign,
        labelCol,
        wrapperCol,
        layout,
        mergedColon,
        mergedRequiredMark,
        wrapForm,
        feedbackIcons
    ]);
    const nativeElementRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, {
        "InternalForm.useImperativeHandle": ()=>{
            var _a;
            return Object.assign(Object.assign({}, wrapForm), {
                nativeElement: (_a = nativeElementRef.current) === null || _a === void 0 ? void 0 : _a.nativeElement
            });
        }
    }["InternalForm.useImperativeHandle"]);
    const scrollToField = (options, fieldName)=>{
        if (options) {
            let defaultScrollToFirstError = {
                block: 'nearest'
            };
            if (typeof options === 'object') {
                defaultScrollToFirstError = Object.assign(Object.assign({}, defaultScrollToFirstError), options);
            }
            wrapForm.scrollToField(fieldName, defaultScrollToFirstError);
        }
    };
    const onInternalFinishFailed = (errorInfo)=>{
        onFinishFailed === null || onFinishFailed === void 0 ? void 0 : onFinishFailed(errorInfo);
        if (errorInfo.errorFields.length) {
            const fieldName = errorInfo.errorFields[0].name;
            if (scrollToFirstError !== undefined) {
                scrollToField(scrollToFirstError, fieldName);
                return;
            }
            if (contextScrollToFirstError !== undefined) {
                scrollToField(contextScrollToFirstError, fieldName);
            }
        }
    };
    return wrapCSSVar(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VariantContext"].Provider, {
        value: variant
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$DisabledContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DisabledContextProvider"], {
        disabled: disabled
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$SizeContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Provider, {
        value: mergedSize
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormProvider"], {
        // This is not list in API, we pass with spread
        validateMessages: contextValidateMessages
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormContext"].Provider, {
        value: formContextValue
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$field$2d$form$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"], Object.assign({
        id: name
    }, restFormProps, {
        name: name,
        onFinishFailed: onInternalFinishFailed,
        form: wrapForm,
        ref: nativeElementRef,
        style: Object.assign(Object.assign({}, contextStyle), style),
        className: formClassName
    }))))))));
};
const Form = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(InternalForm);
if ("TURBOPACK compile-time truthy", 1) {
    Form.displayName = 'Form';
}
;
const __TURBOPACK__default__export__ = Form;
}}),
"[project]/node_modules/antd/es/form/hooks/useForm.js [app-client] (ecmascript) <export default as useForm>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useForm": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useForm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useForm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/hooks/useForm.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/antd/es/form/hooks/useChildren.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useChildren)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Children$2f$toArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/Children/toArray.js [app-client] (ecmascript)");
;
function useChildren(children) {
    if (typeof children === 'function') {
        return children;
    }
    const childList = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Children$2f$toArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(children);
    return childList.length <= 1 ? childList[0] : childList;
}
}}),
"[project]/node_modules/antd/es/form/hooks/useFormItemStatus.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/context.js [app-client] (ecmascript)");
;
;
;
const useFormItemStatus = ()=>{
    const { status, errors = [], warnings = [] } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormItemInputContext"]);
    if ("TURBOPACK compile-time truthy", 1) {
        const warning = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["devUseWarning"])('Form.Item');
        ("TURBOPACK compile-time truthy", 1) ? warning(status !== undefined, 'usage', 'Form.Item.useStatus should be used under Form.Item component. For more information: https://u.ant.design/form-item-usestatus') : ("TURBOPACK unreachable", undefined);
    }
    return {
        status,
        errors,
        warnings
    };
};
// Only used for compatible package. Not promise this will work on future version.
useFormItemStatus.Context = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormItemInputContext"];
const __TURBOPACK__default__export__ = useFormItemStatus;
}}),
"[project]/node_modules/antd/es/form/hooks/useFrameState.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useFrameState)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$raf$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/raf.js [app-client] (ecmascript)");
;
;
function useFrameState(defaultValue) {
    const [value, setValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(defaultValue);
    const frameRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const batchRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])([]);
    const destroyRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useFrameState.useEffect": ()=>{
            destroyRef.current = false;
            return ({
                "useFrameState.useEffect": ()=>{
                    destroyRef.current = true;
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$raf$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].cancel(frameRef.current);
                    frameRef.current = null;
                }
            })["useFrameState.useEffect"];
        }
    }["useFrameState.useEffect"], []);
    function setFrameValue(updater) {
        if (destroyRef.current) {
            return;
        }
        if (frameRef.current === null) {
            batchRef.current = [];
            frameRef.current = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$raf$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>{
                frameRef.current = null;
                setValue((prevValue)=>{
                    let current = prevValue;
                    batchRef.current.forEach((func)=>{
                        current = func(current);
                    });
                    return current;
                });
            });
        }
        batchRef.current.push(updater);
    }
    return [
        value,
        setFrameValue
    ];
}
}}),
"[project]/node_modules/antd/es/form/hooks/useItemRef.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useItemRef)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/ref.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/context.js [app-client] (ecmascript)");
;
;
;
function useItemRef() {
    const { itemRef } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormContext"]);
    const cacheRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])({});
    function getRef(name, children) {
        // Outer caller already check the `supportRef`
        const childrenRef = children && typeof children === 'object' && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNodeRef"])(children);
        const nameStr = name.join('_');
        if (cacheRef.current.name !== nameStr || cacheRef.current.originRef !== childrenRef) {
            cacheRef.current.name = nameStr;
            cacheRef.current.originRef = childrenRef;
            cacheRef.current.ref = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["composeRef"])(itemRef(name), childrenRef);
        }
        return cacheRef.current.ref;
    }
    return getRef;
}
}}),
"[project]/node_modules/antd/es/form/style/fallbackCmp.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Fallback of IE.
 * Safe to remove.
 */ // Style as inline component
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/theme/util/genStyleUtils.js [app-client] (ecmascript)");
;
;
// ============================= Fallback =============================
const genFallbackStyle = (token)=>{
    const { formItemCls } = token;
    return {
        '@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none)': {
            // Fallback for IE, safe to remove we not support it anymore
            [`${formItemCls}-control`]: {
                display: 'flex'
            }
        }
    };
};
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$theme$2f$util$2f$genStyleUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["genSubStyleComponent"])([
    'Form',
    'item-item'
], (token, _ref)=>{
    let { rootPrefixCls } = _ref;
    const formToken = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["prepareToken"])(token, rootPrefixCls);
    return [
        genFallbackStyle(formToken)
    ];
});
}}),
"[project]/node_modules/antd/es/form/FormItemInput.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$utils$2f$get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__get$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/utils/get.js [app-client] (ecmascript) <export default as get>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$utils$2f$set$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__set$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/utils/set.js [app-client] (ecmascript) <export default as set>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useLayoutEffect.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$grid$2f$col$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/grid/col.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$ErrorList$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/ErrorList.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$style$2f$fallbackCmp$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/style/fallbackCmp.js [app-client] (ecmascript)");
"use client";
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
;
;
;
;
;
;
;
;
const GRID_MAX = 24;
const FormItemInput = (props)=>{
    const { prefixCls, status, labelCol, wrapperCol, children, errors, warnings, _internalItemRender: formItemRender, extra, help, fieldId, marginBottom, onErrorVisibleChanged, label } = props;
    const baseClassName = `${prefixCls}-item`;
    const formContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormContext"]);
    const mergedWrapperCol = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "FormItemInput.useMemo[mergedWrapperCol]": ()=>{
            let mergedWrapper = Object.assign({}, wrapperCol || formContext.wrapperCol || {});
            if (label === null && !labelCol && !wrapperCol && formContext.labelCol) {
                const list = [
                    undefined,
                    'xs',
                    'sm',
                    'md',
                    'lg',
                    'xl',
                    'xxl'
                ];
                list.forEach({
                    "FormItemInput.useMemo[mergedWrapperCol]": (size)=>{
                        const _size = size ? [
                            size
                        ] : [];
                        const formLabel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$utils$2f$get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__get$3e$__["get"])(formContext.labelCol, _size);
                        const formLabelObj = typeof formLabel === 'object' ? formLabel : {};
                        const wrapper = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$utils$2f$get$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__get$3e$__["get"])(mergedWrapper, _size);
                        const wrapperObj = typeof wrapper === 'object' ? wrapper : {};
                        if ('span' in formLabelObj && !('offset' in wrapperObj) && formLabelObj.span < GRID_MAX) {
                            mergedWrapper = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$utils$2f$set$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__set$3e$__["set"])(mergedWrapper, [].concat(_size, [
                                'offset'
                            ]), formLabelObj.span);
                        }
                    }
                }["FormItemInput.useMemo[mergedWrapperCol]"]);
            }
            return mergedWrapper;
        }
    }["FormItemInput.useMemo[mergedWrapperCol]"], [
        wrapperCol,
        formContext
    ]);
    const className = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(`${baseClassName}-control`, mergedWrapperCol.className);
    // Pass to sub FormItem should not with col info
    const subFormContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "FormItemInput.useMemo[subFormContext]": ()=>{
            const { labelCol, wrapperCol } = formContext, rest = __rest(formContext, [
                "labelCol",
                "wrapperCol"
            ]);
            return rest;
        }
    }["FormItemInput.useMemo[subFormContext]"], [
        formContext
    ]);
    const extraRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [extraHeight, setExtraHeight] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        "FormItemInput.useLayoutEffect": ()=>{
            if (extra && extraRef.current) {
                setExtraHeight(extraRef.current.clientHeight);
            } else {
                setExtraHeight(0);
            }
        }
    }["FormItemInput.useLayoutEffect"], [
        extra
    ]);
    const inputDom = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: `${baseClassName}-control-input`
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: `${baseClassName}-control-input-content`
    }, children));
    const formItemContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "FormItemInput.useMemo[formItemContext]": ()=>({
                prefixCls,
                status
            })
    }["FormItemInput.useMemo[formItemContext]"], [
        prefixCls,
        status
    ]);
    const errorListDom = marginBottom !== null || errors.length || warnings.length ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormItemPrefixContext"].Provider, {
        value: formItemContext
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$ErrorList$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        fieldId: fieldId,
        errors: errors,
        warnings: warnings,
        help: help,
        helpStatus: status,
        className: `${baseClassName}-explain-connected`,
        onVisibleChanged: onErrorVisibleChanged
    })) : null;
    const extraProps = {};
    if (fieldId) {
        extraProps.id = `${fieldId}_extra`;
    }
    // If extra = 0, && will goes wrong
    // 0&&error -> 0
    const extraDom = extra ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", Object.assign({}, extraProps, {
        className: `${baseClassName}-extra`,
        ref: extraRef
    }), extra) : null;
    const additionalDom = errorListDom || extraDom ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: `${baseClassName}-additional`,
        style: marginBottom ? {
            minHeight: marginBottom + extraHeight
        } : {}
    }, errorListDom, extraDom) : null;
    const dom = formItemRender && formItemRender.mark === 'pro_table_render' && formItemRender.render ? formItemRender.render(props, {
        input: inputDom,
        errorList: errorListDom,
        extra: extraDom
    }) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, inputDom, additionalDom);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormContext"].Provider, {
        value: subFormContext
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$grid$2f$col$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], Object.assign({}, mergedWrapperCol, {
        className: className
    }), dom), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$style$2f$fallbackCmp$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        prefixCls: prefixCls
    }));
};
const __TURBOPACK__default__export__ = FormItemInput;
}}),
"[project]/node_modules/antd/es/form/FormItemLabel.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$QuestionCircleOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/QuestionCircleOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$grid$2f$col$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/grid/col.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$locale$2f$useLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useLocale$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/locale/useLocale.js [app-client] (ecmascript) <export default as useLocale>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$locale$2f$en_US$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/locale/en_US.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tooltip$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/tooltip/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/context.js [app-client] (ecmascript)");
"use client";
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
;
;
;
;
;
;
;
;
function toTooltipProps(tooltip) {
    if (!tooltip) {
        return null;
    }
    if (typeof tooltip === 'object' && !/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidElement"])(tooltip)) {
        return tooltip;
    }
    return {
        title: tooltip
    };
}
const FormItemLabel = (_ref)=>{
    let { prefixCls, label, htmlFor, labelCol, labelAlign, colon, required, requiredMark, tooltip, vertical } = _ref;
    var _a;
    const [formLocale] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$locale$2f$useLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useLocale$3e$__["useLocale"])('Form');
    const { labelAlign: contextLabelAlign, labelCol: contextLabelCol, labelWrap, colon: contextColon } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormContext"]);
    if (!label) {
        return null;
    }
    const mergedLabelCol = labelCol || contextLabelCol || {};
    const mergedLabelAlign = labelAlign || contextLabelAlign;
    const labelClsBasic = `${prefixCls}-item-label`;
    const labelColClassName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(labelClsBasic, mergedLabelAlign === 'left' && `${labelClsBasic}-left`, mergedLabelCol.className, {
        [`${labelClsBasic}-wrap`]: !!labelWrap
    });
    let labelChildren = label;
    // Keep label is original where there should have no colon
    const computedColon = colon === true || contextColon !== false && colon !== false;
    const haveColon = computedColon && !vertical;
    // Remove duplicated user input colon
    if (haveColon && typeof label === 'string' && label.trim()) {
        labelChildren = label.replace(/[:|：]\s*$/, '');
    }
    // Tooltip
    const tooltipProps = toTooltipProps(tooltip);
    if (tooltipProps) {
        const { icon = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$QuestionCircleOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], null) } = tooltipProps, restTooltipProps = __rest(tooltipProps, [
            "icon"
        ]);
        const tooltipNode = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$tooltip$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], Object.assign({}, restTooltipProps), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cloneElement"])(icon, {
            className: `${prefixCls}-item-tooltip`,
            title: '',
            onClick: (e)=>{
                // Prevent label behavior in tooltip icon
                // https://github.com/ant-design/ant-design/issues/46154
                e.preventDefault();
            },
            tabIndex: null
        }));
        labelChildren = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, labelChildren, tooltipNode);
    }
    // Required Mark
    const isOptionalMark = requiredMark === 'optional';
    const isRenderMark = typeof requiredMark === 'function';
    const hideRequiredMark = requiredMark === false;
    if (isRenderMark) {
        labelChildren = requiredMark(labelChildren, {
            required: !!required
        });
    } else if (isOptionalMark && !required) {
        labelChildren = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, labelChildren, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("span", {
            className: `${prefixCls}-item-optional`,
            title: ""
        }, (formLocale === null || formLocale === void 0 ? void 0 : formLocale.optional) || ((_a = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$locale$2f$en_US$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Form) === null || _a === void 0 ? void 0 : _a.optional)));
    }
    // https://github.com/ant-design/ant-design/pull/52950#discussion_r1980880316
    let markType;
    if (hideRequiredMark) {
        markType = 'hidden';
    } else if (isOptionalMark || isRenderMark) {
        markType = 'optional';
    }
    const labelClassName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        [`${prefixCls}-item-required`]: required,
        [`${prefixCls}-item-required-mark-${markType}`]: markType,
        [`${prefixCls}-item-no-colon`]: !computedColon
    });
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$grid$2f$col$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], Object.assign({}, mergedLabelCol, {
        className: labelColClassName
    }), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("label", {
        htmlFor: htmlFor,
        className: labelClassName,
        title: typeof label === 'string' ? label : ''
    }, labelChildren));
};
const __TURBOPACK__default__export__ = FormItemLabel;
}}),
"[project]/node_modules/antd/es/form/FormItem/StatusProvider.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>StatusProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CheckCircleFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/CheckCircleFilled.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CloseCircleFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/CloseCircleFilled.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$ExclamationCircleFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/ExclamationCircleFilled.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$LoadingOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/node_modules/@ant-design/icons/es/icons/LoadingOutlined.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$util$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/util.js [app-client] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
const iconMap = {
    success: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CheckCircleFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    warning: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$ExclamationCircleFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    error: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$CloseCircleFilled$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    validating: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$node_modules$2f40$ant$2d$design$2f$icons$2f$es$2f$icons$2f$LoadingOutlined$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
};
function StatusProvider(_ref) {
    let { children, errors, warnings, hasFeedback, validateStatus, prefixCls, meta, noStyle } = _ref;
    const itemPrefixCls = `${prefixCls}-item`;
    const { feedbackIcons } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormContext"]);
    const mergedValidateStatus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$util$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getStatus"])(errors, warnings, meta, null, !!hasFeedback, validateStatus);
    const { isFormItemInput: parentIsFormItemInput, status: parentStatus, hasFeedback: parentHasFeedback, feedbackIcon: parentFeedbackIcon } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormItemInputContext"]);
    // ====================== Context =======================
    const formItemStatusContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "StatusProvider.useMemo[formItemStatusContext]": ()=>{
            var _a;
            let feedbackIcon;
            if (hasFeedback) {
                const customIcons = hasFeedback !== true && hasFeedback.icons || feedbackIcons;
                const customIconNode = mergedValidateStatus && ((_a = customIcons === null || customIcons === void 0 ? void 0 : customIcons({
                    status: mergedValidateStatus,
                    errors,
                    warnings
                })) === null || _a === void 0 ? void 0 : _a[mergedValidateStatus]);
                const IconNode = mergedValidateStatus && iconMap[mergedValidateStatus];
                feedbackIcon = customIconNode !== false && IconNode ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("span", {
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(`${itemPrefixCls}-feedback-icon`, `${itemPrefixCls}-feedback-icon-${mergedValidateStatus}`)
                }, customIconNode || /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(IconNode, null)) : null;
            }
            const context = {
                status: mergedValidateStatus || '',
                errors,
                warnings,
                hasFeedback: !!hasFeedback,
                feedbackIcon,
                isFormItemInput: true
            };
            // No style will follow parent context
            if (noStyle) {
                context.status = (mergedValidateStatus !== null && mergedValidateStatus !== void 0 ? mergedValidateStatus : parentStatus) || '';
                context.isFormItemInput = parentIsFormItemInput;
                context.hasFeedback = !!(hasFeedback !== null && hasFeedback !== void 0 ? hasFeedback : parentHasFeedback);
                context.feedbackIcon = hasFeedback !== undefined ? context.feedbackIcon : parentFeedbackIcon;
            }
            return context;
        }
    }["StatusProvider.useMemo[formItemStatusContext]"], [
        mergedValidateStatus,
        hasFeedback,
        noStyle,
        parentIsFormItemInput,
        parentStatus
    ]);
    // ======================= Render =======================
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormItemInputContext"].Provider, {
        value: formItemStatusContext
    }, children);
}
}}),
"[project]/node_modules/antd/es/form/FormItem/ItemHolder.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ItemHolder)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$isVisible$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/Dom/isVisible.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useLayoutEffect.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$omit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/omit.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$grid$2f$row$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Row$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/grid/row.js [app-client] (ecmascript) <export default as Row>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$FormItemInput$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/FormItemInput.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$FormItemLabel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/FormItemLabel.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useDebounce$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/hooks/useDebounce.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$util$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/util.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$FormItem$2f$StatusProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/FormItem/StatusProvider.js [app-client] (ecmascript)");
"use client";
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
;
;
;
;
;
;
;
;
;
;
;
;
function ItemHolder(props) {
    const { prefixCls, className, rootClassName, style, help, errors, warnings, validateStatus, meta, hasFeedback, hidden, children, fieldId, required, isRequired, onSubItemMetaChange, layout } = props, restProps = __rest(props, [
        "prefixCls",
        "className",
        "rootClassName",
        "style",
        "help",
        "errors",
        "warnings",
        "validateStatus",
        "meta",
        "hasFeedback",
        "hidden",
        "children",
        "fieldId",
        "required",
        "isRequired",
        "onSubItemMetaChange",
        "layout"
    ]);
    const itemPrefixCls = `${prefixCls}-item`;
    const { requiredMark, vertical: formVertical } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormContext"]);
    const vertical = formVertical || layout === 'vertical';
    // ======================== Margin ========================
    const itemRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const debounceErrors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useDebounce$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(errors);
    const debounceWarnings = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useDebounce$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(warnings);
    const hasHelp = help !== undefined && help !== null;
    const hasError = !!(hasHelp || errors.length || warnings.length);
    const isOnScreen = !!itemRef.current && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$isVisible$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(itemRef.current);
    const [marginBottom, setMarginBottom] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        "ItemHolder.useLayoutEffect": ()=>{
            if (hasError && itemRef.current) {
                // The element must be part of the DOMTree to use getComputedStyle
                // https://stackoverflow.com/questions/35360711/getcomputedstyle-returns-a-cssstyledeclaration-but-all-properties-are-empty-on-a
                const itemStyle = getComputedStyle(itemRef.current);
                setMarginBottom(parseInt(itemStyle.marginBottom, 10));
            }
        }
    }["ItemHolder.useLayoutEffect"], [
        hasError,
        isOnScreen
    ]);
    const onErrorVisibleChanged = (nextVisible)=>{
        if (!nextVisible) {
            setMarginBottom(null);
        }
    };
    // ======================== Status ========================
    const getValidateState = function() {
        let isDebounce = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;
        const _errors = isDebounce ? debounceErrors : meta.errors;
        const _warnings = isDebounce ? debounceWarnings : meta.warnings;
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$util$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getStatus"])(_errors, _warnings, meta, '', !!hasFeedback, validateStatus);
    };
    const mergedValidateStatus = getValidateState();
    // ======================== Render ========================
    const itemClassName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(itemPrefixCls, className, rootClassName, {
        [`${itemPrefixCls}-with-help`]: hasHelp || debounceErrors.length || debounceWarnings.length,
        // Status
        [`${itemPrefixCls}-has-feedback`]: mergedValidateStatus && hasFeedback,
        [`${itemPrefixCls}-has-success`]: mergedValidateStatus === 'success',
        [`${itemPrefixCls}-has-warning`]: mergedValidateStatus === 'warning',
        [`${itemPrefixCls}-has-error`]: mergedValidateStatus === 'error',
        [`${itemPrefixCls}-is-validating`]: mergedValidateStatus === 'validating',
        [`${itemPrefixCls}-hidden`]: hidden,
        // Layout
        [`${itemPrefixCls}-${layout}`]: layout
    });
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: itemClassName,
        style: style,
        ref: itemRef
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$grid$2f$row$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Row$3e$__["Row"], Object.assign({
        className: `${itemPrefixCls}-row`
    }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$omit$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(restProps, [
        '_internalItemRender',
        'colon',
        'dependencies',
        'extra',
        'fieldKey',
        'getValueFromEvent',
        'getValueProps',
        'htmlFor',
        'id',
        // It is deprecated because `htmlFor` is its replacement.
        'initialValue',
        'isListField',
        'label',
        'labelAlign',
        'labelCol',
        'labelWrap',
        'messageVariables',
        'name',
        'normalize',
        'noStyle',
        'preserve',
        'requiredMark',
        'rules',
        'shouldUpdate',
        'trigger',
        'tooltip',
        'validateFirst',
        'validateTrigger',
        'valuePropName',
        'wrapperCol',
        'validateDebounce'
    ])), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$FormItemLabel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], Object.assign({
        htmlFor: fieldId
    }, props, {
        requiredMark: requiredMark,
        required: required !== null && required !== void 0 ? required : isRequired,
        prefixCls: prefixCls,
        vertical: vertical
    })), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$FormItemInput$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], Object.assign({}, props, meta, {
        errors: debounceErrors,
        warnings: debounceWarnings,
        prefixCls: prefixCls,
        status: mergedValidateStatus,
        help: help,
        marginBottom: marginBottom,
        onErrorVisibleChanged: onErrorVisibleChanged
    }), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NoStyleItemContext"].Provider, {
        value: onSubItemMetaChange
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$FormItem$2f$StatusProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        prefixCls: prefixCls,
        meta: meta,
        errors: meta.errors,
        warnings: meta.warnings,
        hasFeedback: hasFeedback,
        // Already calculated
        validateStatus: mergedValidateStatus
    }, children)))), !!marginBottom && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: `${itemPrefixCls}-margin-offset`,
        style: {
            marginBottom: -marginBottom
        }
    }));
}
}}),
"[project]/node_modules/antd/es/form/FormItem/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$field$2d$form$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-field-form/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$field$2d$form$2f$es$2f$Field$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Field$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-field-form/es/Field.js [app-client] (ecmascript) <export default as Field>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$field$2d$form$2f$es$2f$FieldContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FieldContext$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-field-form/es/FieldContext.js [app-client] (ecmascript) <export default as FieldContext>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$field$2d$form$2f$es$2f$ListContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ListContext$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-field-form/es/ListContext.js [app-client] (ecmascript) <export default as ListContext>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useState$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useState.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/ref.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$reactNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/reactNode.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useCSSVarCls$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/hooks/useCSSVarCls.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useChildren$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/hooks/useChildren.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useFormItemStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/hooks/useFormItemStatus.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useFrameState$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/hooks/useFrameState.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useItemRef$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/hooks/useItemRef.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/style/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$util$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/util.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$FormItem$2f$ItemHolder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/FormItem/ItemHolder.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$FormItem$2f$StatusProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/FormItem/StatusProvider.js [app-client] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const NAME_SPLIT = '__SPLIT__';
const _ValidateStatuses = [
    'success',
    'warning',
    'error',
    'validating',
    ''
];
// https://github.com/ant-design/ant-design/issues/46417
// `getValueProps` may modify the value props name,
// we should check if the control is similar.
function isSimilarControl(a, b) {
    const keysA = Object.keys(a);
    const keysB = Object.keys(b);
    return keysA.length === keysB.length && keysA.every((key)=>{
        const propValueA = a[key];
        const propValueB = b[key];
        return propValueA === propValueB || typeof propValueA === 'function' || typeof propValueB === 'function';
    });
}
const MemoInput = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])((_ref)=>{
    let { children } = _ref;
    return children;
}, (prev, next)=>isSimilarControl(prev.control, next.control) && prev.update === next.update && prev.childProps.length === next.childProps.length && prev.childProps.every((value, index)=>value === next.childProps[index]));
function genEmptyMeta() {
    return {
        errors: [],
        warnings: [],
        touched: false,
        validating: false,
        name: [],
        validated: false
    };
}
function InternalFormItem(props) {
    const { name, noStyle, className, dependencies, prefixCls: customizePrefixCls, shouldUpdate, rules, children, required, label, messageVariables, trigger = 'onChange', validateTrigger, hidden, help, layout } = props;
    const { getPrefixCls } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfigContext"]);
    const { name: formName } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormContext"]);
    const mergedChildren = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useChildren$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(children);
    const isRenderProps = typeof mergedChildren === 'function';
    const notifyParentMetaChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NoStyleItemContext"]);
    const { validateTrigger: contextValidateTrigger } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$field$2d$form$2f$es$2f$FieldContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FieldContext$3e$__["FieldContext"]);
    const mergedValidateTrigger = validateTrigger !== undefined ? validateTrigger : contextValidateTrigger;
    const hasName = !(name === undefined || name === null);
    const prefixCls = getPrefixCls('form', customizePrefixCls);
    // Style
    const rootCls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$hooks$2f$useCSSVarCls$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls);
    const [wrapCSSVar, hashId, cssVarCls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$style$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prefixCls, rootCls);
    // ========================= Warn =========================
    const warning = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["devUseWarning"])('Form.Item');
    if ("TURBOPACK compile-time truthy", 1) {
        ("TURBOPACK compile-time truthy", 1) ? warning(name !== null, 'usage', '`null` is passed as `name` property') : ("TURBOPACK unreachable", undefined);
    }
    // ========================= MISC =========================
    // Get `noStyle` required info
    const listContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$field$2d$form$2f$es$2f$ListContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ListContext$3e$__["ListContext"]);
    const fieldKeyPathRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // ======================== Errors ========================
    // >>>>> Collect sub field errors
    const [subFieldErrors, setSubFieldErrors] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useFrameState$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({});
    // >>>>> Current field errors
    const [meta, setMeta] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useState$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        "InternalFormItem.useState": ()=>genEmptyMeta()
    }["InternalFormItem.useState"]);
    const onMetaChange = (nextMeta)=>{
        // This keyInfo is not correct when field is removed
        // Since origin keyManager no longer keep the origin key anymore
        // Which means we need cache origin one and reuse when removed
        const keyInfo = listContext === null || listContext === void 0 ? void 0 : listContext.getKey(nextMeta.name);
        // Destroy will reset all the meta
        setMeta(nextMeta.destroy ? genEmptyMeta() : nextMeta, true);
        // Bump to parent since noStyle
        if (noStyle && help !== false && notifyParentMetaChange) {
            let namePath = nextMeta.name;
            if (!nextMeta.destroy) {
                if (keyInfo !== undefined) {
                    const [fieldKey, restPath] = keyInfo;
                    namePath = [
                        fieldKey
                    ].concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(restPath));
                    fieldKeyPathRef.current = namePath;
                }
            } else {
                // Use origin cache data
                namePath = fieldKeyPathRef.current || namePath;
            }
            notifyParentMetaChange(nextMeta, namePath);
        }
    };
    // >>>>> Collect noStyle Field error to the top FormItem
    const onSubItemMetaChange = (subMeta, uniqueKeys)=>{
        // Only `noStyle` sub item will trigger
        setSubFieldErrors((prevSubFieldErrors)=>{
            const clone = Object.assign({}, prevSubFieldErrors);
            // name: ['user', 1] + key: [4] = ['user', 4]
            const mergedNamePath = [].concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(subMeta.name.slice(0, -1)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(uniqueKeys));
            const mergedNameKey = mergedNamePath.join(NAME_SPLIT);
            if (subMeta.destroy) {
                // Remove
                delete clone[mergedNameKey];
            } else {
                // Update
                clone[mergedNameKey] = subMeta;
            }
            return clone;
        });
    };
    // >>>>> Get merged errors
    const [mergedErrors, mergedWarnings] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "InternalFormItem.useMemo": ()=>{
            const errorList = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(meta.errors);
            const warningList = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(meta.warnings);
            Object.values(subFieldErrors).forEach({
                "InternalFormItem.useMemo": (subFieldError)=>{
                    errorList.push.apply(errorList, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(subFieldError.errors || []));
                    warningList.push.apply(warningList, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(subFieldError.warnings || []));
                }
            }["InternalFormItem.useMemo"]);
            return [
                errorList,
                warningList
            ];
        }
    }["InternalFormItem.useMemo"], [
        subFieldErrors,
        meta.errors,
        meta.warnings
    ]);
    // ===================== Children Ref =====================
    const getItemRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useItemRef$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    // ======================== Render ========================
    function renderLayout(baseChildren, fieldId, isRequired) {
        if (noStyle && !hidden) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$FormItem$2f$StatusProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                prefixCls: prefixCls,
                hasFeedback: props.hasFeedback,
                validateStatus: props.validateStatus,
                meta: meta,
                errors: mergedErrors,
                warnings: mergedWarnings,
                noStyle: true
            }, baseChildren);
        }
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$FormItem$2f$ItemHolder$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], Object.assign({
            key: "row"
        }, props, {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(className, cssVarCls, rootCls, hashId),
            prefixCls: prefixCls,
            fieldId: fieldId,
            isRequired: isRequired,
            errors: mergedErrors,
            warnings: mergedWarnings,
            meta: meta,
            onSubItemMetaChange: onSubItemMetaChange,
            layout: layout
        }), baseChildren);
    }
    if (!hasName && !isRenderProps && !dependencies) {
        return wrapCSSVar(renderLayout(mergedChildren));
    }
    let variables = {};
    if (typeof label === 'string') {
        variables.label = label;
    } else if (name) {
        variables.label = String(name);
    }
    if (messageVariables) {
        variables = Object.assign(Object.assign({}, variables), messageVariables);
    }
    // >>>>> With Field
    return wrapCSSVar(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$field$2d$form$2f$es$2f$Field$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Field$3e$__["Field"], Object.assign({}, props, {
        messageVariables: variables,
        trigger: trigger,
        validateTrigger: mergedValidateTrigger,
        onMetaChange: onMetaChange
    }), (control, renderMeta, context)=>{
        const mergedName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$util$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toArray"])(name).length && renderMeta ? renderMeta.name : [];
        const fieldId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$util$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFieldId"])(mergedName, formName);
        const isRequired = required !== undefined ? required : !!(rules === null || rules === void 0 ? void 0 : rules.some((rule)=>{
            if (rule && typeof rule === 'object' && rule.required && !rule.warningOnly) {
                return true;
            }
            if (typeof rule === 'function') {
                const ruleEntity = rule(context);
                return (ruleEntity === null || ruleEntity === void 0 ? void 0 : ruleEntity.required) && !(ruleEntity === null || ruleEntity === void 0 ? void 0 : ruleEntity.warningOnly);
            }
            return false;
        }));
        // ======================= Children =======================
        const mergedControl = Object.assign({}, control);
        let childNode = null;
        ("TURBOPACK compile-time truthy", 1) ? warning(!(shouldUpdate && dependencies), 'usage', "`shouldUpdate` and `dependencies` shouldn't be used together. See https://u.ant.design/form-deps.") : ("TURBOPACK unreachable", undefined);
        if (Array.isArray(mergedChildren) && hasName) {
            ("TURBOPACK compile-time truthy", 1) ? warning(false, 'usage', 'A `Form.Item` with a `name` prop must have a single child element. For information on how to render more complex form items, see https://u.ant.design/complex-form-item.') : ("TURBOPACK unreachable", undefined);
            childNode = mergedChildren;
        } else if (isRenderProps && (!(shouldUpdate || dependencies) || hasName)) {
            ("TURBOPACK compile-time truthy", 1) ? warning(!!(shouldUpdate || dependencies), 'usage', 'A `Form.Item` with a render function must have either `shouldUpdate` or `dependencies`.') : ("TURBOPACK unreachable", undefined);
            ("TURBOPACK compile-time truthy", 1) ? warning(!hasName, 'usage', 'A `Form.Item` with a render function cannot be a field, and thus cannot have a `name` prop.') : ("TURBOPACK unreachable", undefined);
        } else if (dependencies && !isRenderProps && !hasName) {
            ("TURBOPACK compile-time truthy", 1) ? warning(false, 'usage', 'Must set `name` or use a render function when `dependencies` is set.') : ("TURBOPACK unreachable", undefined);
        } else if (/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidElement"])(mergedChildren)) {
            ("TURBOPACK compile-time truthy", 1) ? warning(mergedChildren.props.defaultValue === undefined, 'usage', '`defaultValue` will not work on controlled Field. You should use `initialValues` of Form instead.') : ("TURBOPACK unreachable", undefined);
            const childProps = Object.assign(Object.assign({}, mergedChildren.props), mergedControl);
            if (!childProps.id) {
                childProps.id = fieldId;
            }
            if (help || mergedErrors.length > 0 || mergedWarnings.length > 0 || props.extra) {
                const describedbyArr = [];
                if (help || mergedErrors.length > 0) {
                    describedbyArr.push(`${fieldId}_help`);
                }
                if (props.extra) {
                    describedbyArr.push(`${fieldId}_extra`);
                }
                childProps['aria-describedby'] = describedbyArr.join(' ');
            }
            if (mergedErrors.length > 0) {
                childProps['aria-invalid'] = 'true';
            }
            if (isRequired) {
                childProps['aria-required'] = 'true';
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["supportRef"])(mergedChildren)) {
                childProps.ref = getItemRef(mergedName, mergedChildren);
            }
            // We should keep user origin event handler
            const triggers = new Set([].concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$util$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toArray"])(trigger)), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$util$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toArray"])(mergedValidateTrigger))));
            triggers.forEach((eventName)=>{
                childProps[eventName] = function() {
                    var _a2, _c2;
                    var _a, _b, _c;
                    for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
                        args[_key] = arguments[_key];
                    }
                    (_a = mergedControl[eventName]) === null || _a === void 0 ? void 0 : (_a2 = _a).call.apply(_a2, [
                        mergedControl
                    ].concat(args));
                    (_c = (_b = mergedChildren.props)[eventName]) === null || _c === void 0 ? void 0 : (_c2 = _c).call.apply(_c2, [
                        _b
                    ].concat(args));
                };
            });
            // List of props that need to be watched for changes -> if changes are detected in MemoInput -> rerender
            const watchingChildProps = [
                childProps['aria-required'],
                childProps['aria-invalid'],
                childProps['aria-describedby']
            ];
            childNode = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(MemoInput, {
                control: mergedControl,
                update: mergedChildren,
                childProps: watchingChildProps
            }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$reactNode$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cloneElement"])(mergedChildren, childProps));
        } else if (isRenderProps && (shouldUpdate || dependencies) && !hasName) {
            childNode = mergedChildren(context);
        } else {
            ("TURBOPACK compile-time truthy", 1) ? warning(!mergedName.length || !!noStyle, 'usage', '`name` is only used for validate React element. If you are using Form.Item as layout display, please remove `name` instead.') : ("TURBOPACK unreachable", undefined);
            childNode = mergedChildren;
        }
        return renderLayout(childNode, fieldId, isRequired);
    }));
}
const FormItem = InternalFormItem;
FormItem.useStatus = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useFormItemStatus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
const __TURBOPACK__default__export__ = FormItem;
}}),
"[project]/node_modules/antd/es/form/FormList.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$field$2d$form$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-field-form/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$field$2d$form$2f$es$2f$List$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__List$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-field-form/es/List.js [app-client] (ecmascript) <export default as List>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/context.js [app-client] (ecmascript)");
"use client";
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
;
;
;
;
;
const FormList = (_a)=>{
    var { prefixCls: customizePrefixCls, children } = _a, props = __rest(_a, [
        "prefixCls",
        "children"
    ]);
    if ("TURBOPACK compile-time truthy", 1) {
        const warning = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["devUseWarning"])('Form.List');
        ("TURBOPACK compile-time truthy", 1) ? warning(typeof props.name === 'number' || (Array.isArray(props.name) ? !!props.name.length : !!props.name), 'usage', 'Miss `name` prop.') : ("TURBOPACK unreachable", undefined);
    }
    const { getPrefixCls } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfigContext"]);
    const prefixCls = getPrefixCls('form', customizePrefixCls);
    const contextValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "FormList.useMemo[contextValue]": ()=>({
                prefixCls,
                status: 'error'
            })
    }["FormList.useMemo[contextValue]"], [
        prefixCls
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$field$2d$form$2f$es$2f$List$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__List$3e$__["List"], Object.assign({}, props), (fields, operation, meta)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormItemPrefixContext"].Provider, {
            value: contextValue
        }, children(fields.map((field)=>Object.assign(Object.assign({}, field), {
                fieldKey: field.key
            })), operation, {
            errors: meta.errors,
            warnings: meta.warnings
        })));
};
const __TURBOPACK__default__export__ = FormList;
}}),
"[project]/node_modules/antd/es/form/hooks/useFormInstance.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useFormInstance)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/context.js [app-client] (ecmascript)");
;
;
function useFormInstance() {
    const { form } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormContext"]);
    return form;
}
}}),
"[project]/node_modules/antd/es/form/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/_util/warning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$ErrorList$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/ErrorList.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/Form.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useForm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useForm$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/hooks/useForm.js [app-client] (ecmascript) <export default as useForm>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$field$2d$form$2f$es$2f$useWatch$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useWatch$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-field-form/es/useWatch.js [app-client] (ecmascript) <export default as useWatch>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$FormItem$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/FormItem/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$FormList$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/FormList.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useFormInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/hooks/useFormInstance.js [app-client] (ecmascript)");
"use client";
;
;
;
;
;
;
;
const Form = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$Form$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"];
Form.Item = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$FormItem$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
Form.List = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$FormList$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
Form.ErrorList = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$ErrorList$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
Form.useForm = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useForm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useForm$3e$__["useForm"];
Form.useFormInstance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$hooks$2f$useFormInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
Form.useWatch = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$field$2d$form$2f$es$2f$useWatch$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__useWatch$3e$__["useWatch"];
Form.Provider = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FormProvider"];
Form.create = ()=>{
    ("TURBOPACK compile-time truthy", 1) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$_util$2f$warning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(false, 'Form', 'antd v4 removed `Form.create`. Please remove or use `@ant-design/compatible` instead.') : ("TURBOPACK unreachable", undefined);
};
const __TURBOPACK__default__export__ = Form;
}}),
"[project]/node_modules/antd/es/form/index.js [app-client] (ecmascript) <export default as Form>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Form": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/index.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/antd/es/form/hooks/useVariants.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/form/context.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/context.js [app-client] (ecmascript)");
;
;
;
/**
 * Compatible for legacy `bordered` prop.
 */ const useVariant = function(component, variant) {
    let legacyBordered = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : undefined;
    var _a, _b;
    const { variant: configVariant, [component]: componentConfig } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfigContext"]);
    const ctxVariant = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$form$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VariantContext"]);
    const configComponentVariant = componentConfig === null || componentConfig === void 0 ? void 0 : componentConfig.variant;
    let mergedVariant;
    if (typeof variant !== 'undefined') {
        mergedVariant = variant;
    } else if (legacyBordered === false) {
        mergedVariant = 'borderless';
    } else {
        // form variant > component global variant > global variant
        mergedVariant = (_b = (_a = ctxVariant !== null && ctxVariant !== void 0 ? ctxVariant : configComponentVariant) !== null && _a !== void 0 ? _a : configVariant) !== null && _b !== void 0 ? _b : 'outlined';
    }
    const enableVariantCls = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$context$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Variants"].includes(mergedVariant);
    return [
        mergedVariant,
        enableVariantCls
    ];
};
const __TURBOPACK__default__export__ = useVariant;
}}),
}]);

//# sourceMappingURL=node_modules_antd_es_form_c3377881._.js.map