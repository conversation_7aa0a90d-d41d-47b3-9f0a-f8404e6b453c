package com.bosi.sim.paas.tenant.server.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.dao.model.sds.SmsReferralCode;
import com.bosi.sim.paas.dao.model.sds.jsonclz.DivideAccountJsonarrayClass;
import com.bosi.sim.paas.dao.model.tds.TdsTenantAccount;
import com.bosi.sim.paas.tenant.server.dao.SmsReferralCodeDao;
import com.bosi.sim.paas.tenant.server.dao.TmsUserAccountDao;
import com.bosi.sim.paas.tenant.server.service.SmsReferralCodeService;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 分销商管理 服务实现
 */
@Service
public class SmsReferralCodeServiceImpl implements SmsReferralCodeService {

    @Autowired
    private SmsReferralCodeDao referralCodeDao;

    @Autowired
    private TmsUserAccountDao userAccountDao;

    @Override
    public CommonPage<SmsReferralCode> page(Page<SmsReferralCode> page, SmsReferralCode referralCode) {
        Page<SmsReferralCode> pageResult = referralCodeDao.page(page, referralCode);
        Set<String> accountIds = Sets.newHashSet();
        pageResult.getRecords().forEach(entity -> {
            List<DivideAccountJsonarrayClass> classList = JSON.parseArray(entity.getDivideAccountJsonarray(), DivideAccountJsonarrayClass.class);
            classList.forEach(jsonarrayClass -> accountIds.add(jsonarrayClass.getAccountId()));
            entity.setDivideAccountJsonarrayClassList(classList);
        });
        List<TdsTenantAccount> accounts = userAccountDao.listByIds(accountIds);
        Map<String, TdsTenantAccount> accountMap = Maps.uniqueIndex(accounts, TdsTenantAccount::getId);
        pageResult.getRecords().forEach(entity -> entity.getDivideAccountJsonarrayClassList().forEach(obj -> {
            TdsTenantAccount account = accountMap.get(obj.getAccountId());
            if (account != null) {
                obj.setAccountUserName(account.getUserName());
            }
        }));
        return CommonPage.restPage(pageResult);
    }

}
