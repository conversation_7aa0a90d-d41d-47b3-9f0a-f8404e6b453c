package com.bosi.sim.paas.dao.enums.ads;

public enum AdsMisfirePolicyEnum {

    MISFIRE_DEFAULT(0, "默认"),

    MISFIRE_IGNORE_MISFIRES(1, "立即触发执行"),

    MISFIRE_FIRE_AND_PROCEED(2, "立即触发执行"),

    MISFIRE_DO_NOTHING(3, "不触发立即执行");

    private Integer misfirePolicy;

    private String desc;

    AdsMisfirePolicyEnum(Integer misfirePolicy, String desc) {
        this.misfirePolicy = misfirePolicy;
        this.desc = desc;
    }

    public Integer getMisfirePolicy() {
        return misfirePolicy;
    }

    public String getDesc() {
        return desc;
    }

    public static AdsMisfirePolicyEnum getByMisfirePolicy(Integer misfirePolicy) {
        for (AdsMisfirePolicyEnum cmsMisfirePolicyEnum : AdsMisfirePolicyEnum.values()) {
            if (misfirePolicy.equals(cmsMisfirePolicyEnum.getMisfirePolicy())) {
                return cmsMisfirePolicyEnum;
            }
        }
        return null;
    }
}
