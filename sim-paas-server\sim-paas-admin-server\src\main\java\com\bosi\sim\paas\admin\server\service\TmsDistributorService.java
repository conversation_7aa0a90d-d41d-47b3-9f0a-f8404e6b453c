package com.bosi.sim.paas.admin.server.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.dao.model.tds.TdsTenant;
import com.bosi.sim.paas.admin.server.domain.TmsDistributorCreateParam;

import java.util.List;

/**
 * 部门管理 服务层
 */
public interface TmsDistributorService {

    CommonPage<TdsTenant> page(Page<TdsTenant> page, TdsTenant tmsDistributor);

    List<TdsTenant> listAll();

    TdsTenant selectDistributorById(String distributorId);

    boolean checkDistributorExistUser(String distributorId);

    int insertDistributor(TmsDistributorCreateParam createParam);

    int updateDistributor(TdsTenant dept);

    int deleteDistributorById(String distributorId);

}
