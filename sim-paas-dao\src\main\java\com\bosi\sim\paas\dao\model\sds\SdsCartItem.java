package com.bosi.sim.paas.dao.model.sds;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("sds_cart_item")
public class SdsCartItem extends BaseEntity {
    private String productId;

    private String productSkuId;

    private String memberId;

    private String referralCode;

    @ApiModelProperty(value = "购买数量")
    private Integer quantity;

    private String deviceUidJson;

    @ApiModelProperty(value = "添加到购物车的价格")
    @TableField(exist = false)
    private BigDecimal price;

    @ApiModelProperty(value = "商品主图")
    @TableField(exist = false)
    private String pic;

    @ApiModelProperty(value = "商品名称")
    @TableField(exist = false)
    private String productName;

    @ApiModelProperty(value = "商品副标题（卖点）")
    @TableField(exist = false)
    private String subTitle;

    @ApiModelProperty(value = "商品sku条码")
    @TableField(exist = false)
    private String skuCode;

    @ApiModelProperty(value = "商品sku名称")
    @TableField(exist = false)
    private String skuName;

    @ApiModelProperty(value = "商品分类")
    @TableField(exist = false)
    private String productCategoryId;

    @ApiModelProperty(value = "商品分类")
    @TableField(exist = false)
    private String categoryName;

    @ApiModelProperty(value = "商品分类")
    @TableField(exist = false)
    private String brandId;

    @TableField(exist = false)
    private String brandName;

    @TableField(exist = false)
    private String productSn;

    @ApiModelProperty(value = "商品销售属性:[{'key':'颜色','value':'颜色'},{'key':'容量','value':'4G'}]")
    @TableField(exist = false)
    private String spData;

}
