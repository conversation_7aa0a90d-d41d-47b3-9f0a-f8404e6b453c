package com.bosi.sim.paas.admin.server.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.dao.mapper.tms.TmsUserAccountMapper;
import com.bosi.sim.paas.dao.model.tds.TdsTenantAccount;
import com.bosi.sim.paas.admin.server.dao.TmsUserAccountDao;
import com.bosi.sim.paas.admin.server.service.TmsUserAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 分销商管理 服务实现
 */
@Service
public class TmsUserAccountServiceImpl implements TmsUserAccountService {

    @Autowired
    private TmsUserAccountDao userAccountDao;

    @Autowired
    private TmsUserAccountMapper tmsUserAccountMapper;

    @Override
    public CommonPage<TdsTenantAccount> page(Page<TdsTenantAccount> page, TdsTenantAccount userAccount) {
        return CommonPage.restPage(userAccountDao.page(page, userAccount));
    }

    @Override
    public int updateStatus(TdsTenantAccount userAccount) {
        TdsTenantAccount info = tmsUserAccountMapper.selectById(userAccount.getId());
        if (info.getWhetherEnable().equals(userAccount.getWhetherEnable())) {
            return 0;
        }
        TdsTenantAccount update = new TdsTenantAccount();
        update.setId(userAccount.getId());
        update.setWhetherEnable(userAccount.getWhetherEnable());
        return tmsUserAccountMapper.updateById(update);
    }

    @Override
    public List<TdsTenantAccount> listAccountByDistributorId(String distributorId) {
        return userAccountDao.listAccountByDistributorId(distributorId);
    }
}
