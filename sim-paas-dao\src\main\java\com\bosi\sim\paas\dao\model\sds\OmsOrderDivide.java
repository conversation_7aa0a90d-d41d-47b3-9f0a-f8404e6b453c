package com.bosi.sim.paas.dao.model.sds;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.esim.mall.dao.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@TableName("oms_order_divide")
public class OmsOrderDivide extends BaseEntity {
    @ApiModelProperty(value = "订单id")
    private String orderId;

    private String orderItemId;

    private Integer settleMode;

    private String distributorId;

    private Integer settleSituation;

    @TableField(exist = false)
    private String memberId;

    @TableField(exist = false)
    private String memberUsername;

    @TableField(exist = false)
    private String orderSn;

    @TableField(exist = false)
    private String distributorName;

    @TableField(exist = false)
    private String productId;

    @TableField(exist = false)
    private String productSkuId;

    @TableField(exist = false)
    private String productSkuCode;

    @TableField(exist = false)
    private String productSkuName;

    @TableField(exist = false)
    private BigDecimal perRealAmount;

    @TableField(exist = false)
    private BigDecimal totalRealAmount;

    @TableField(exist = false)
    private BigDecimal divideTotalAmount;

    @TableField(exist = false)
    private Integer productQuantity;

    @TableField(exist = false)
    private String divideReferralCode;

    @TableField(exist = false)
    private String accountUserName;

    @TableField(exist = false)
    private List<OmsOrderDivideSettle> omsOrderDivideSettleList;

}
