package com.bosi.sim.paas.tenant.server.controller;


import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.exception.CommonResult;
import com.bosi.sim.paas.common.core.utils.PageUtil;
import com.bosi.sim.paas.common.optlog.annotation.OperateLog;
import com.bosi.sim.paas.common.security.annotation.RequiresPermissions;
import com.bosi.sim.paas.common.security.auth.CurrentAuthorization;
import com.bosi.sim.paas.dao.model.tds.TdsOperateLog;
import com.bosi.sim.paas.tenant.server.enums.DistributorTokenExtendKeyEnum;
import com.bosi.sim.paas.tenant.server.service.TmsOperateLogService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * 操作日志记录
 */
@RestController
@Api(tags = "UmsOperatelogController", value = "操作日志管理")
@RequestMapping("/tms/operateLog")
public class TmsOperateLogController {
    @Autowired
    private TmsOperateLogService operateLogService;

    @RequiresPermissions("tms:operateLog:page")
    @GetMapping("/page")
    public CommonResult page(
            @RequestParam(value = "pageNum") Integer pageNum,
            @RequestParam(value = "pageSize") Integer pageSize,
            TdsOperateLog operLog) {
        operLog.setDistributorId(CurrentAuthorization.getStringFromExtendJson(DistributorTokenExtendKeyEnum.DISTRIBUTORID_KEY.getKey()));
        CommonPage<TdsOperateLog> commonPage = operateLogService.page(PageUtil.buildPage(pageNum, pageSize, "oper_time", false), operLog);
        return CommonResult.success(commonPage);
    }

    @OperateLog("删除操作日志")
    @RequiresPermissions("tms:operateLog:remove")
    @DeleteMapping("/{operIds}")
    public CommonResult remove(@PathVariable String[] operIds) {
        return CommonResult.success(operateLogService.deleteOperLogByIds(operIds));
    }
}
