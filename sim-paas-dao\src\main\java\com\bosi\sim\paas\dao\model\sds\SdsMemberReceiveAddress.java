package com.bosi.sim.paas.dao.model.sds;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("sds_member_receive_address")
public class SdsMemberReceiveAddress extends BaseEntity {
    private String memberId;

    @ApiModelProperty(value = "收货人名称")
    private String name;

    private String phoneNumber;

    @ApiModelProperty(value = "是否为默认")
    private Boolean whetherDefault;

    @ApiModelProperty(value = "区域")
    private String region;

    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty(value = "级联地址（多个逗号隔开）")
    private String levelAddresses;

    @ApiModelProperty(value = "详细地址(街道)")
    private String detailAddress;


}
