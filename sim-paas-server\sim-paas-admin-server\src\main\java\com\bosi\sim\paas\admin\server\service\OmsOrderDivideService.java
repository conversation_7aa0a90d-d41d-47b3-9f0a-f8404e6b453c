package com.bosi.sim.paas.admin.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.dao.pojo.dto.SdsOrderDivideInfoDto;
import com.bosi.sim.paas.dao.model.sds.OmsOrderDivide;
import com.bosi.sim.paas.dao.model.sds.OmsOrderDivideSettleHistory;

import java.util.List;

public interface OmsOrderDivideService {

    CommonPage<OmsOrderDivide> page(Page<OmsOrderDivide> page, OmsOrderDivide omsOrderDivide);

    SdsOrderDivideInfoDto list(OmsOrderDivide omsOrderDivide);

    void tosettle(String jobId, List<String> orderDivideSettleIds);

    List<OmsOrderDivideSettleHistory> listSettleHistoryBySettleId(String settleId);

}
