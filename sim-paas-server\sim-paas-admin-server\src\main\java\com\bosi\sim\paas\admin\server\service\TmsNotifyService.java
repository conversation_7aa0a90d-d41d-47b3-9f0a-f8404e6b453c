package com.bosi.sim.paas.admin.server.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.dao.model.tds.TdsTenant;
import com.bosi.sim.paas.dao.model.tds.TmsNotify;
import com.bosi.sim.paas.dao.model.tds.TdsNotifyRecord;

/**
 * 通知 业务层
 */
public interface TmsNotifyService {

    CommonPage<TmsNotify> page(Page<TmsNotify> page, TmsNotify notify);

    TmsNotify getById(String id);

    int insert(TmsNotify notify);

    int update(TmsNotify notify);

    int deleteByIds(String[] ids);

    CommonPage<TdsNotifyRecord> pageRecord(Page<TdsNotifyRecord> page, TdsNotifyRecord notifyRecord);

    CommonPage<TdsTenant> pageDistributorForNotify(Page<TdsTenant> page, TdsTenant distributor);

    int insertRecord(TdsNotifyRecord notifyRecord);

    int deleteRecordByIds(String[] ids);

}
