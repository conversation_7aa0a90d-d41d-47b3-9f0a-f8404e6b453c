package com.bosi.sim.paas.admin.server.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.dao.model.tds.TdsTenantAccount;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;


public interface TmsUserAccountDao {

    Page<TdsTenantAccount> page(Page<TdsTenantAccount> iPage, @Param("params") TdsTenantAccount tmsUserAccount);

    List<TdsTenantAccount> listAccountByDistributorId(String distributorId);

    int increaseAvailableAmount(@Param("id") String id, @Param("amount") BigDecimal amount);

    int decreaseIntransitBalance(@Param("id") String id, @Param("amount") BigDecimal amount);

    int increaseIntransitBalance(@Param("id") String id, @Param("amount") BigDecimal amount);
}
