(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/services/api.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "api": (()=>api),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
;
// API 基础配置
const API_BASE_URL = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_API_URL || '/api';
// 创建 axios 实例
const apiClient = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json'
    }
});
// 请求拦截器 - 添加认证 token
apiClient.interceptors.request.use((config)=>{
    // 检查是否在浏览器环境
    if ("TURBOPACK compile-time truthy", 1) {
        const token = localStorage.getItem('token');
        if (token && config.headers) {
            config.headers.Authorization = `Bearer ${token}`;
        }
    }
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// 响应拦截器 - 处理错误
apiClient.interceptors.response.use((response)=>response, (error)=>{
    // 检查是否在浏览器环境
    if ("TURBOPACK compile-time truthy", 1) {
        // 处理 401 未授权错误
        if (error.response && error.response.status === 401) {
            localStorage.removeItem('token');
            window.location.href = '/login';
        }
    }
    return Promise.reject(error);
});
const api = {
    get: (url, config)=>{
        return apiClient.get(url, config).then((response)=>response.data);
    },
    post: (url, data, config)=>{
        return apiClient.post(url, data, config).then((response)=>response.data);
    },
    put: (url, data, config)=>{
        return apiClient.put(url, data, config).then((response)=>response.data);
    },
    delete: (url, config)=>{
        return apiClient.delete(url, config).then((response)=>response.data);
    }
};
const __TURBOPACK__default__export__ = api;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/auth.service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "authService": (()=>authService),
    "default": (()=>__TURBOPACK__default__export__)
});
// 模拟数据
const mockAdminUser = {
    id: '1',
    username: 'admin',
    name: '管理员',
    email: '<EMAIL>',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
    status: 'active',
    roleIds: [
        '1'
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
};
const mockToken = 'mock-jwt-token';
const authService = {
    // 登录
    login: async (credentials)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.post<AuthResponse>('/auth/login', credentials);
            // 模拟登录逻辑
            if (credentials.username === 'admin' && credentials.password === 'admin123') {
                // 模拟延迟
                await new Promise((resolve)=>setTimeout(resolve, 500));
                // 保存 token 到本地存储
                if ("TURBOPACK compile-time truthy", 1) {
                    localStorage.setItem('token', mockToken);
                }
                return {
                    token: mockToken,
                    user: mockAdminUser
                };
            } else {
                throw new Error('用户名或密码错误');
            }
        } catch (error) {
            throw error;
        }
    },
    // 登出
    logout: async ()=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.post<void>('/auth/logout');
            // 模拟登出逻辑
            if ("TURBOPACK compile-time truthy", 1) {
                localStorage.removeItem('token');
            }
            await new Promise((resolve)=>setTimeout(resolve, 300));
        } catch (error) {
            throw error;
        }
    },
    // 获取当前用户信息
    getCurrentUser: async ()=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.get<User>('/auth/me');
            // 模拟获取用户信息
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            const token = localStorage.getItem('token');
            if (!token) {
                throw new Error('未登录');
            }
            // 模拟延迟
            await new Promise((resolve)=>setTimeout(resolve, 300));
            return mockAdminUser;
        } catch (error) {
            throw error;
        }
    },
    // 检查是否已登录
    isAuthenticated: ()=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        return !!localStorage.getItem('token');
    }
};
const __TURBOPACK__default__export__ = authService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/user.service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "userService": (()=>userService)
});
// 模拟数据
const mockUsers = [
    {
        id: '1',
        username: 'admin',
        name: '管理员',
        email: '<EMAIL>',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
        status: 'active',
        roleIds: [
            '1'
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        id: '2',
        username: 'user1',
        name: '普通用户1',
        email: '<EMAIL>',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user1',
        status: 'active',
        roleIds: [
            '2'
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        id: '3',
        username: 'user2',
        name: '普通用户2',
        email: '<EMAIL>',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user2',
        status: 'inactive',
        roleIds: [
            '2'
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    }
];
const userService = {
    // 获取用户列表
    getUsers: async (params)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.get<PaginatedResponse<User>>('/users', { params });
            // 模拟获取用户列表
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const { page, pageSize } = params;
            const startIndex = (page - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const paginatedUsers = mockUsers.slice(startIndex, endIndex);
            return {
                data: paginatedUsers,
                total: mockUsers.length,
                page,
                pageSize
            };
        } catch (error) {
            throw error;
        }
    },
    // 获取单个用户
    getUser: async (id)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.get<User>(`/users/${id}`);
            // 模拟获取单个用户
            await new Promise((resolve)=>setTimeout(resolve, 300));
            const user = mockUsers.find((user)=>user.id === id);
            if (!user) {
                throw new Error('用户不存在');
            }
            return user;
        } catch (error) {
            throw error;
        }
    },
    // 创建用户
    createUser: async (userData)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.post<User>('/users', userData);
            // 模拟创建用户
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const newUser = {
                id: String(mockUsers.length + 1),
                username: userData.username,
                name: userData.name,
                email: userData.email,
                status: userData.status,
                roleIds: userData.roleIds,
                avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${userData.username}`,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            // 在实际应用中，这里会由后端处理
            mockUsers.push(newUser);
            return newUser;
        } catch (error) {
            throw error;
        }
    },
    // 更新用户
    updateUser: async (userData)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.put<User>(`/users/${userData.id}`, userData);
            // 模拟更新用户
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const userIndex = mockUsers.findIndex((user)=>user.id === userData.id);
            if (userIndex === -1) {
                throw new Error('用户不存在');
            }
            const updatedUser = {
                ...mockUsers[userIndex],
                ...userData,
                updatedAt: new Date().toISOString()
            };
            // 在实际应用中，这里会由后端处理
            mockUsers[userIndex] = updatedUser;
            return updatedUser;
        } catch (error) {
            throw error;
        }
    },
    // 删除用户
    deleteUser: async (id)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.delete<void>(`/users/${id}`);
            // 模拟删除用户
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const userIndex = mockUsers.findIndex((user)=>user.id === id);
            if (userIndex === -1) {
                throw new Error('用户不存在');
            }
            // 在实际应用中，这里会由后端处理
            mockUsers.splice(userIndex, 1);
        } catch (error) {
            throw error;
        }
    }
};
const __TURBOPACK__default__export__ = userService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/role.service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "roleService": (()=>roleService)
});
// 模拟数据
const mockRoles = [
    {
        id: '1',
        name: '超级管理员',
        description: '拥有所有权限',
        menuIds: [
            '1',
            '2',
            '3',
            '4',
            '5',
            '6',
            '7',
            '8'
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        id: '2',
        name: '普通用户',
        description: '拥有基本权限',
        menuIds: [
            '1',
            '2'
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        id: '3',
        name: '访客',
        description: '只有查看权限',
        menuIds: [
            '1'
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    }
];
const roleService = {
    // 获取角色列表
    getRoles: async (params)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.get<PaginatedResponse<Role>>('/roles', { params });
            // 模拟获取角色列表
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const { page, pageSize } = params;
            const startIndex = (page - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const paginatedRoles = mockRoles.slice(startIndex, endIndex);
            return {
                data: paginatedRoles,
                total: mockRoles.length,
                page,
                pageSize
            };
        } catch (error) {
            throw error;
        }
    },
    // 获取所有角色（不分页）
    getAllRoles: async ()=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.get<Role[]>('/roles/all');
            // 模拟获取所有角色
            await new Promise((resolve)=>setTimeout(resolve, 300));
            return mockRoles;
        } catch (error) {
            throw error;
        }
    },
    // 获取单个角色
    getRole: async (id)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.get<Role>(`/roles/${id}`);
            // 模拟获取单个角色
            await new Promise((resolve)=>setTimeout(resolve, 300));
            const role = mockRoles.find((role)=>role.id === id);
            if (!role) {
                throw new Error('角色不存在');
            }
            return role;
        } catch (error) {
            throw error;
        }
    },
    // 创建角色
    createRole: async (roleData)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.post<Role>('/roles', roleData);
            // 模拟创建角色
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const newRole = {
                id: String(mockRoles.length + 1),
                name: roleData.name,
                description: roleData.description,
                menuIds: roleData.menuIds,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            // 在实际应用中，这里会由后端处理
            mockRoles.push(newRole);
            return newRole;
        } catch (error) {
            throw error;
        }
    },
    // 更新角色
    updateRole: async (roleData)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.put<Role>(`/roles/${roleData.id}`, roleData);
            // 模拟更新角色
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const roleIndex = mockRoles.findIndex((role)=>role.id === roleData.id);
            if (roleIndex === -1) {
                throw new Error('角色不存在');
            }
            const updatedRole = {
                ...mockRoles[roleIndex],
                ...roleData,
                updatedAt: new Date().toISOString()
            };
            // 在实际应用中，这里会由后端处理
            mockRoles[roleIndex] = updatedRole;
            return updatedRole;
        } catch (error) {
            throw error;
        }
    },
    // 删除角色
    deleteRole: async (id)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.delete<void>(`/roles/${id}`);
            // 模拟删除角色
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const roleIndex = mockRoles.findIndex((role)=>role.id === id);
            if (roleIndex === -1) {
                throw new Error('角色不存在');
            }
            // 在实际应用中，这里会由后端处理
            mockRoles.splice(roleIndex, 1);
        } catch (error) {
            throw error;
        }
    }
};
const __TURBOPACK__default__export__ = roleService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/menu.service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "menuService": (()=>menuService)
});
// 模拟数据
const mockMenus = [
    {
        id: '1',
        name: '仪表盘',
        path: '/dashboard',
        icon: 'dashboard',
        parentId: null,
        order: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        id: '2',
        name: '系统管理',
        path: '/system',
        icon: 'setting',
        parentId: null,
        order: 2,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        id: '3',
        name: '用户管理',
        path: '/system/users',
        icon: 'user',
        parentId: '2',
        order: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        id: '4',
        name: '角色管理',
        path: '/system/roles',
        icon: 'team',
        parentId: '2',
        order: 2,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        id: '5',
        name: '菜单管理',
        path: '/system/menus',
        icon: 'menu',
        parentId: '2',
        order: 3,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    }
];
// 构建菜单树
const buildMenuTree = (menus)=>{
    const menuMap = new Map();
    const result = [];
    // 先将所有菜单放入 Map 中
    menus.forEach((menu)=>{
        menuMap.set(menu.id, {
            ...menu,
            children: []
        });
    });
    // 构建树形结构
    menus.forEach((menu)=>{
        const menuWithChildren = menuMap.get(menu.id);
        if (menu.parentId === null) {
            // 根菜单
            result.push(menuWithChildren);
        } else {
            // 子菜单
            const parentMenu = menuMap.get(menu.parentId);
            if (parentMenu) {
                if (!parentMenu.children) {
                    parentMenu.children = [];
                }
                parentMenu.children.push(menuWithChildren);
            }
        }
    });
    // 对菜单进行排序
    const sortMenus = (menus)=>{
        return menus.sort((a, b)=>a.order - b.order).map((menu)=>({
                ...menu,
                children: menu.children ? sortMenus(menu.children) : undefined
            }));
    };
    return sortMenus(result);
};
const menuService = {
    // 获取菜单列表
    getMenus: async (params)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.get<PaginatedResponse<Menu>>('/menus', { params });
            // 模拟获取菜单列表
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const { page, pageSize } = params;
            const startIndex = (page - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const paginatedMenus = mockMenus.slice(startIndex, endIndex);
            return {
                data: paginatedMenus,
                total: mockMenus.length,
                page,
                pageSize
            };
        } catch (error) {
            throw error;
        }
    },
    // 获取菜单树
    getMenuTree: async ()=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.get<Menu[]>('/menus/tree');
            // 模拟获取菜单树
            await new Promise((resolve)=>setTimeout(resolve, 300));
            return buildMenuTree(mockMenus);
        } catch (error) {
            throw error;
        }
    },
    // 获取单个菜单
    getMenu: async (id)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.get<Menu>(`/menus/${id}`);
            // 模拟获取单个菜单
            await new Promise((resolve)=>setTimeout(resolve, 300));
            const menu = mockMenus.find((menu)=>menu.id === id);
            if (!menu) {
                throw new Error('菜单不存在');
            }
            return menu;
        } catch (error) {
            throw error;
        }
    },
    // 创建菜单
    createMenu: async (menuData)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.post<Menu>('/menus', menuData);
            // 模拟创建菜单
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const newMenu = {
                id: String(mockMenus.length + 1),
                name: menuData.name,
                path: menuData.path,
                icon: menuData.icon,
                parentId: menuData.parentId || null,
                order: menuData.order || mockMenus.length + 1,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            // 在实际应用中，这里会由后端处理
            mockMenus.push(newMenu);
            return newMenu;
        } catch (error) {
            throw error;
        }
    },
    // 更新菜单
    updateMenu: async (menuData)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.put<Menu>(`/menus/${menuData.id}`, menuData);
            // 模拟更新菜单
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const menuIndex = mockMenus.findIndex((menu)=>menu.id === menuData.id);
            if (menuIndex === -1) {
                throw new Error('菜单不存在');
            }
            const updatedMenu = {
                ...mockMenus[menuIndex],
                ...menuData,
                updatedAt: new Date().toISOString()
            };
            // 在实际应用中，这里会由后端处理
            mockMenus[menuIndex] = updatedMenu;
            return updatedMenu;
        } catch (error) {
            throw error;
        }
    },
    // 删除菜单
    deleteMenu: async (id)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.delete<void>(`/menus/${id}`);
            // 模拟删除菜单
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const menuIndex = mockMenus.findIndex((menu)=>menu.id === id);
            if (menuIndex === -1) {
                throw new Error('菜单不存在');
            }
            // 在实际应用中，这里会由后端处理
            mockMenus.splice(menuIndex, 1);
        } catch (error) {
            throw error;
        }
    }
};
const __TURBOPACK__default__export__ = menuService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/index.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/auth.service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$user$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/user.service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$role$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/role.service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$menu$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/menu.service.ts [app-client] (ecmascript)");
;
;
;
;
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/index.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/auth.service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$user$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/user.service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$role$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/role.service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$menu$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/menu.service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/index.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "default": (()=>__TURBOPACK__default__export__),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/auth.service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const AuthProvider = ({ children })=>{
    _s();
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // 获取当前用户信息
    const fetchCurrentUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AuthProvider.useCallback[fetchCurrentUser]": async ()=>{
            try {
                setLoading(true);
                setError(null);
                // 检查是否已登录
                if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].isAuthenticated()) {
                    setUser(null);
                    setLoading(false);
                    return;
                }
                // 获取当前用户信息
                const currentUser = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].getCurrentUser();
                setUser(currentUser);
            } catch (error) {
                console.error('获取用户信息失败:', error);
                setError('获取用户信息失败');
                // 清除无效的 token
                if ("TURBOPACK compile-time truthy", 1) {
                    localStorage.removeItem('token');
                }
                setUser(null);
            } finally{
                setLoading(false);
            }
        }
    }["AuthProvider.useCallback[fetchCurrentUser]"], []);
    // 登录
    const login = async (credentials)=>{
        try {
            setLoading(true);
            setError(null);
            // 调用登录 API
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].login(credentials);
            // 保存用户信息
            setUser(response.user);
        } catch (error) {
            console.error('登录失败:', error);
            setError(error.message || '登录失败');
            throw error;
        } finally{
            setLoading(false);
        }
    };
    // 登出
    const logout = async ()=>{
        try {
            setLoading(true);
            // 调用登出 API
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].logout();
            // 清除用户信息
            setUser(null);
        } catch (error) {
            console.error('登出失败:', error);
        } finally{
            setLoading(false);
        }
    };
    // 初始化时获取用户信息
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            fetchCurrentUser();
        }
    }["AuthProvider.useEffect"], [
        fetchCurrentUser
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: {
            user,
            loading,
            error,
            login,
            logout
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 94,
        columnNumber: 5
    }, this);
};
_s(AuthProvider, "TpPp4jL3sjVaBpBzKJKe3E/hBT0=");
_c = AuthProvider;
const useAuth = ()=>{
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
_s1(useAuth, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
const __TURBOPACK__default__export__ = AuthContext;
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/layout/ClientLayout.tsx [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
const e = new Error(`Could not parse module '[project]/src/components/layout/ClientLayout.tsx'

Unexpected token `I18nProvider`. Expected jsx identifier`);
e.code = 'MODULE_UNPARSEABLE';
throw e;}}),
}]);

//# sourceMappingURL=src_7eb347f4._.js.map