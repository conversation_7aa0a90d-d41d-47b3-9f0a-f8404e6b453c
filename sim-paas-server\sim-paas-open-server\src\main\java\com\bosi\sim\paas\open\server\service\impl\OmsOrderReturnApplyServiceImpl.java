package com.bosi.sim.paas.open.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bosi.sim.paas.common.core.api.BizCode;
import com.bosi.sim.paas.common.core.api.SystemCode;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.common.security.auth.CurrentAuthorization;
import com.bosi.sim.paas.dao.enums.sds.SdsOrderStatusEnum;
import com.bosi.sim.paas.dao.enums.sds.SdsReturnApplyStatusEnum;
import com.bosi.sim.paas.dao.mapper.oms.OmsOrderMapper;
import com.bosi.sim.paas.dao.mapper.oms.OmsOrderReturnApplyMapper;
import com.bosi.sim.paas.dao.model.sds.OmsOrder;
import com.bosi.sim.paas.dao.model.sds.OmsOrderReturnApply;
import com.bosi.sim.paas.open.server.service.OmsOrderReturnApplyService;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 订单退货管理Service实现类
 */
@Service
public class OmsOrderReturnApplyServiceImpl implements OmsOrderReturnApplyService {

    @Autowired
    private OmsOrderReturnApplyMapper returnApplyMapper;

    @Autowired
    private OmsOrderMapper omsOrderMapper;

    @Override
    @Transactional
    public int create(OmsOrderReturnApply returnApply) {
        if (StringUtils.isNull(returnApply.getOrderId())) {
            throw BizException.build(BizCode.RETURN_ORDER_NOT_FOUND);
        }
        OmsOrder order = omsOrderMapper.selectById(returnApply.getOrderId());
        if (order == null) {
            throw BizException.build(SystemCode.RESOURCE_NOTFOUND);
        }
        if (!order.getStatus().equals(SdsOrderStatusEnum.WAITDELIVERY.getOrderStatus())
                || !order.getStatus().equals(SdsOrderStatusEnum.DELIVERYED.getOrderStatus())) {
            throw BizException.build(BizCode.ORDER_STATUS_ERROR);
        }
        if (returnApply.getReturnAmount().compareTo(order.getTotalPayAmount()) > 0) {
            throw BizException.build(BizCode.RETURN_AMOUNT_CANNOT_MORE_REAL_AMOUNT);
        }
        LambdaQueryWrapper<OmsOrderReturnApply> countWrapper = new LambdaQueryWrapper<>();
        countWrapper.eq(OmsOrderReturnApply::getOrderId, returnApply.getOrderId());
        countWrapper.in(OmsOrderReturnApply::getStatus, Lists.newArrayList(SdsReturnApplyStatusEnum.IN_RETURN.getReturnApplyStatus(),
                SdsReturnApplyStatusEnum.WAIT_APPLY.getReturnApplyStatus(), SdsReturnApplyStatusEnum.APPLY_OK.getReturnApplyStatus()));
        long inHandleCount = returnApplyMapper.selectCount(countWrapper);
        if (inHandleCount > 0L) {
            throw BizException.build(BizCode.RETURN_APPLYING);
        }
        returnApply.setStatus(SdsReturnApplyStatusEnum.WAIT_APPLY.getReturnApplyStatus());
        returnApply.setMemberId(CurrentAuthorization.getUserId());
        return returnApplyMapper.insert(returnApply);
    }
}
