package com.bosi.sim.paas.dao.model.sds;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@TableName("sds_attribute")
public class SdsAttribute extends BaseEntity {

    private String name;

    @ApiModelProperty(value = "规格数量")
    private Integer specCount;

    @ApiModelProperty(value = "参数数量")
    private Integer paramCount;

    @TableField(exist = false)
    private List<SdsAttributeType> attributeTypeList;

}
