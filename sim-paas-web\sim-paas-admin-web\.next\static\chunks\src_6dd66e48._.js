(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/i18n/locales/en.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const __TURBOPACK__default__export__ = {
    // 通用
    common: {
        welcome: 'Welcome',
        login: 'Login',
        logout: 'Logout',
        username: 'Username',
        password: 'Password',
        email: 'Email',
        name: 'Name',
        status: 'Status',
        role: 'Role',
        roles: 'Roles',
        action: 'Action',
        search: 'Search',
        add: 'Add',
        edit: 'Edit',
        delete: 'Delete',
        cancel: 'Cancel',
        confirm: 'Confirm',
        save: 'Save',
        refresh: 'Refresh',
        back: 'Back',
        success: 'Success',
        error: 'Error',
        warning: 'Warning',
        info: 'Info',
        loading: 'Loading...',
        required: 'Required',
        optional: 'Optional',
        active: 'Active',
        inactive: 'Inactive',
        enabled: 'Enabled',
        disabled: 'Disabled',
        yes: 'Yes',
        no: 'No',
        all: 'All',
        none: 'None',
        total: 'Total {{count}} items',
        language: 'Language',
        switchLanguage: 'Switch Language',
        theme: 'Theme',
        switchTheme: 'Switch Theme',
        light: 'Light',
        dark: 'Dark'
    },
    // 菜单
    menu: {
        dashboard: 'Dashboard',
        system: 'System',
        user: 'User Management',
        role: 'Role Management',
        menu: 'Menu Management',
        home: 'Home'
    },
    // 登录页
    login: {
        title: 'IOT Admin',
        subtitle: 'A clean and beautiful admin system',
        rememberMe: 'Remember me',
        forgotPassword: 'Forgot password?',
        noAccount: 'Don\'t have an account?',
        register: 'Register now',
        loginButton: 'Login',
        defaultAccount: 'Default account: admin / admin123',
        loginSuccess: 'Login successful',
        loginFailed: 'Login failed, please check your username and password'
    },
    // 仪表盘
    dashboard: {
        title: 'Dashboard',
        userCount: 'User Count',
        roleCount: 'Role Count',
        menuCount: 'Menu Count',
        systemInfo: 'System Information',
        systemName: 'System Name',
        version: 'Version',
        frontendFramework: 'Frontend Framework',
        backendTechnology: 'Backend Technology'
    },
    // 用户管理
    user: {
        title: 'User Management',
        addUser: 'Add User',
        editUser: 'Edit User',
        deleteUser: 'Delete User',
        deleteConfirm: 'Are you sure you want to delete this user?',
        username: 'Username',
        name: 'Name',
        email: 'Email',
        password: 'Password',
        role: 'Role',
        status: 'Status',
        createSuccess: 'User created successfully',
        updateSuccess: 'User updated successfully',
        deleteSuccess: 'User deleted successfully',
        pleaseEnterUsername: 'Please enter username',
        pleaseEnterName: 'Please enter name',
        pleaseEnterEmail: 'Please enter email',
        pleaseEnterPassword: 'Please enter password',
        pleaseSelectRole: 'Please select role',
        invalidEmail: 'Please enter a valid email address'
    },
    // 角色管理
    role: {
        title: 'Role Management',
        addRole: 'Add Role',
        editRole: 'Edit Role',
        deleteRole: 'Delete Role',
        deleteConfirm: 'Are you sure you want to delete this role?',
        name: 'Role Name',
        description: 'Description',
        menuPermission: 'Menu Permission',
        createSuccess: 'Role created successfully',
        updateSuccess: 'Role updated successfully',
        deleteSuccess: 'Role deleted successfully',
        pleaseEnterName: 'Please enter role name',
        pleaseEnterDescription: 'Please enter description',
        pleaseSelectMenuPermission: 'Please select menu permission'
    },
    // 菜单管理
    menuManagement: {
        title: 'Menu Management',
        addMenu: 'Add Menu',
        editMenu: 'Edit Menu',
        deleteMenu: 'Delete Menu',
        deleteConfirm: 'Are you sure you want to delete this menu?',
        name: 'Menu Name',
        path: 'Path',
        icon: 'Icon',
        parentMenu: 'Parent Menu',
        order: 'Order',
        rootMenu: 'Root Menu',
        createSuccess: 'Menu created successfully',
        updateSuccess: 'Menu updated successfully',
        deleteSuccess: 'Menu deleted successfully',
        pleaseEnterName: 'Please enter menu name',
        pleaseEnterPath: 'Please enter path',
        pleaseSelectIcon: 'Please select icon',
        pleaseSelectParentMenu: 'Please select parent menu',
        pleaseEnterOrder: 'Please enter order'
    },
    // 标签页
    tabs: {
        refresh: 'Refresh Page',
        close: 'Close Tab',
        closeOthers: 'Close Other Tabs',
        closeAll: 'Close All Tabs',
        refreshSuccess: 'Page refreshed'
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/i18n/locales/zh.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const __TURBOPACK__default__export__ = {
    // 通用
    common: {
        welcome: '欢迎',
        login: '登录',
        logout: '退出登录',
        username: '用户名',
        password: '密码',
        email: '邮箱',
        name: '姓名',
        status: '状态',
        role: '角色',
        roles: '角色',
        action: '操作',
        search: '搜索',
        add: '新增',
        edit: '编辑',
        delete: '删除',
        cancel: '取消',
        confirm: '确认',
        save: '保存',
        refresh: '刷新',
        back: '返回',
        success: '成功',
        error: '错误',
        warning: '警告',
        info: '信息',
        loading: '加载中...',
        required: '必填',
        optional: '可选',
        active: '启用',
        inactive: '禁用',
        enabled: '启用',
        disabled: '禁用',
        yes: '是',
        no: '否',
        all: '全部',
        none: '无',
        total: '共 {{count}} 条记录',
        language: '语言',
        switchLanguage: '切换语言',
        theme: '主题',
        switchTheme: '切换主题',
        light: '亮色',
        dark: '暗色'
    },
    // 菜单
    menu: {
        dashboard: '仪表盘',
        system: '系统管理',
        user: '用户管理',
        role: '角色管理',
        menu: '菜单管理',
        home: '首页'
    },
    // 登录页
    login: {
        title: 'IOT Admin',
        subtitle: '一个简洁美观的后台管理系统',
        rememberMe: '记住我',
        forgotPassword: '忘记密码？',
        noAccount: '没有账号？',
        register: '立即注册',
        loginButton: '登录',
        defaultAccount: '默认账号：admin / admin123',
        loginSuccess: '登录成功',
        loginFailed: '登录失败，请检查用户名和密码'
    },
    // 仪表盘
    dashboard: {
        title: '仪表盘',
        userCount: '用户总数',
        roleCount: '角色总数',
        menuCount: '菜单总数',
        systemInfo: '系统信息',
        systemName: '系统名称',
        version: '当前版本',
        frontendFramework: '前端框架',
        backendTechnology: '后端技术'
    },
    // 用户管理
    user: {
        title: '用户管理',
        addUser: '新增用户',
        editUser: '编辑用户',
        deleteUser: '删除用户',
        deleteConfirm: '确定要删除此用户吗？',
        username: '用户名',
        name: '姓名',
        email: '邮箱',
        password: '密码',
        role: '角色',
        status: '状态',
        createSuccess: '用户创建成功',
        updateSuccess: '用户更新成功',
        deleteSuccess: '用户删除成功',
        pleaseEnterUsername: '请输入用户名',
        pleaseEnterName: '请输入姓名',
        pleaseEnterEmail: '请输入邮箱',
        pleaseEnterPassword: '请输入密码',
        pleaseSelectRole: '请选择角色',
        invalidEmail: '请输入有效的邮箱地址'
    },
    // 角色管理
    role: {
        title: '角色管理',
        addRole: '新增角色',
        editRole: '编辑角色',
        deleteRole: '删除角色',
        deleteConfirm: '确定要删除此角色吗？',
        name: '角色名称',
        description: '描述',
        menuPermission: '菜单权限',
        createSuccess: '角色创建成功',
        updateSuccess: '角色更新成功',
        deleteSuccess: '角色删除成功',
        pleaseEnterName: '请输入角色名称',
        pleaseEnterDescription: '请输入描述',
        pleaseSelectMenuPermission: '请选择菜单权限'
    },
    // 菜单管理
    menuManagement: {
        title: '菜单管理',
        addMenu: '新增菜单',
        editMenu: '编辑菜单',
        deleteMenu: '删除菜单',
        deleteConfirm: '确定要删除此菜单吗？',
        name: '菜单名称',
        path: '路径',
        icon: '图标',
        parentMenu: '上级菜单',
        order: '排序',
        rootMenu: '根菜单',
        createSuccess: '菜单创建成功',
        updateSuccess: '菜单更新成功',
        deleteSuccess: '菜单删除成功',
        pleaseEnterName: '请输入菜单名称',
        pleaseEnterPath: '请输入路径',
        pleaseSelectIcon: '请选择图标',
        pleaseSelectParentMenu: '请选择上级菜单',
        pleaseEnterOrder: '请输入排序'
    },
    // 标签页
    tabs: {
        refresh: '刷新页面',
        close: '关闭标签页',
        closeOthers: '关闭其他标签页',
        closeAll: '关闭所有标签页',
        refreshSuccess: '页面已刷新'
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/i18n/index.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$i18next$2f$dist$2f$esm$2f$i18next$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/i18next/dist/esm/i18next.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$i18next$2f$dist$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/react-i18next/dist/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$i18next$2f$dist$2f$es$2f$initReactI18next$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-i18next/dist/es/initReactI18next.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$i18next$2d$browser$2d$languagedetector$2f$dist$2f$esm$2f$i18nextBrowserLanguageDetector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$locales$2f$en$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/i18n/locales/en.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$locales$2f$zh$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/i18n/locales/zh.ts [app-client] (ecmascript)");
;
;
;
;
;
// 初始化 i18next
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$i18next$2f$dist$2f$esm$2f$i18next$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]// 检测用户语言
.use(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$i18next$2d$browser$2d$languagedetector$2f$dist$2f$esm$2f$i18nextBrowserLanguageDetector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])// 将 i18n 实例传递给 react-i18next
.use(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$i18next$2f$dist$2f$es$2f$initReactI18next$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initReactI18next"])// 初始化 i18next
.init({
    resources: {
        en: {
            translation: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$locales$2f$en$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
        },
        zh: {
            translation: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$locales$2f$zh$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
        }
    },
    fallbackLng: 'zh',
    debug: ("TURBOPACK compile-time value", "development") === 'development',
    interpolation: {
        escapeValue: false // 不转义 HTML
    },
    detection: {
        order: [
            'localStorage',
            'navigator'
        ],
        caches: [
            'localStorage'
        ]
    }
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$i18next$2f$dist$2f$esm$2f$i18next$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/i18n/I18nProvider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>I18nProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/i18n/index.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function I18nProvider({ children }) {
    _s();
    const [initialized, setInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "I18nProvider.useEffect": ()=>{
            // 确保 i18n 在客户端初始化，且只初始化一次
            if (!initialized && __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isInitialized) {
                setInitialized(true);
            }
        }
    }["I18nProvider.useEffect"], [
        initialized
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
_s(I18nProvider, "Kqi3FOcoZthoTYGDbMGQxPYNq5w=");
_c = I18nProvider;
var _c;
__turbopack_context__.k.register(_c, "I18nProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/api.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "api": (()=>api),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
;
// API 基础配置
const API_BASE_URL = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.NEXT_PUBLIC_API_URL || '/api';
// 创建 axios 实例
const apiClient = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: API_BASE_URL,
    headers: {
        'Content-Type': 'application/json'
    }
});
// 请求拦截器 - 添加认证 token
apiClient.interceptors.request.use((config)=>{
    // 检查是否在浏览器环境
    if ("TURBOPACK compile-time truthy", 1) {
        const token = localStorage.getItem('token');
        if (token && config.headers) {
            config.headers.Authorization = `Bearer ${token}`;
        }
    }
    return config;
}, (error)=>{
    return Promise.reject(error);
});
// 响应拦截器 - 处理错误
apiClient.interceptors.response.use((response)=>response, (error)=>{
    // 检查是否在浏览器环境
    if ("TURBOPACK compile-time truthy", 1) {
        // 处理 401 未授权错误
        if (error.response && error.response.status === 401) {
            localStorage.removeItem('token');
            window.location.href = '/login';
        }
    }
    return Promise.reject(error);
});
const api = {
    get: (url, config)=>{
        return apiClient.get(url, config).then((response)=>response.data);
    },
    post: (url, data, config)=>{
        return apiClient.post(url, data, config).then((response)=>response.data);
    },
    put: (url, data, config)=>{
        return apiClient.put(url, data, config).then((response)=>response.data);
    },
    delete: (url, config)=>{
        return apiClient.delete(url, config).then((response)=>response.data);
    }
};
const __TURBOPACK__default__export__ = api;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/auth.service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "authService": (()=>authService),
    "default": (()=>__TURBOPACK__default__export__)
});
// 模拟数据
const mockAdminUser = {
    id: '1',
    username: 'admin',
    name: '管理员',
    email: '<EMAIL>',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
    status: 'active',
    roleIds: [
        '1'
    ],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
};
const mockToken = 'mock-jwt-token';
const authService = {
    // 登录
    login: async (credentials)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.post<AuthResponse>('/auth/login', credentials);
            // 模拟登录逻辑
            if (credentials.username === 'admin' && credentials.password === 'admin123') {
                // 模拟延迟
                await new Promise((resolve)=>setTimeout(resolve, 500));
                // 保存 token 到本地存储
                if ("TURBOPACK compile-time truthy", 1) {
                    localStorage.setItem('token', mockToken);
                }
                return {
                    token: mockToken,
                    user: mockAdminUser
                };
            } else {
                throw new Error('用户名或密码错误');
            }
        } catch (error) {
            throw error;
        }
    },
    // 登出
    logout: async ()=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.post<void>('/auth/logout');
            // 模拟登出逻辑
            if ("TURBOPACK compile-time truthy", 1) {
                localStorage.removeItem('token');
            }
            await new Promise((resolve)=>setTimeout(resolve, 300));
        } catch (error) {
            throw error;
        }
    },
    // 获取当前用户信息
    getCurrentUser: async ()=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.get<User>('/auth/me');
            // 模拟获取用户信息
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            const token = localStorage.getItem('token');
            if (!token) {
                throw new Error('未登录');
            }
            // 模拟延迟
            await new Promise((resolve)=>setTimeout(resolve, 300));
            return mockAdminUser;
        } catch (error) {
            throw error;
        }
    },
    // 检查是否已登录
    isAuthenticated: ()=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        return !!localStorage.getItem('token');
    }
};
const __TURBOPACK__default__export__ = authService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/user.service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "userService": (()=>userService)
});
// 模拟数据
const mockUsers = [
    {
        id: '1',
        username: 'admin',
        name: '管理员',
        email: '<EMAIL>',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
        status: 'active',
        roleIds: [
            '1'
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        id: '2',
        username: 'user1',
        name: '普通用户1',
        email: '<EMAIL>',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user1',
        status: 'active',
        roleIds: [
            '2'
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        id: '3',
        username: 'user2',
        name: '普通用户2',
        email: '<EMAIL>',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user2',
        status: 'inactive',
        roleIds: [
            '2'
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    }
];
const userService = {
    // 获取用户列表
    getUsers: async (params)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.get<PaginatedResponse<User>>('/users', { params });
            // 模拟获取用户列表
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const { page, pageSize } = params;
            const startIndex = (page - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const paginatedUsers = mockUsers.slice(startIndex, endIndex);
            return {
                data: paginatedUsers,
                total: mockUsers.length,
                page,
                pageSize
            };
        } catch (error) {
            throw error;
        }
    },
    // 获取单个用户
    getUser: async (id)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.get<User>(`/users/${id}`);
            // 模拟获取单个用户
            await new Promise((resolve)=>setTimeout(resolve, 300));
            const user = mockUsers.find((user)=>user.id === id);
            if (!user) {
                throw new Error('用户不存在');
            }
            return user;
        } catch (error) {
            throw error;
        }
    },
    // 创建用户
    createUser: async (userData)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.post<User>('/users', userData);
            // 模拟创建用户
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const newUser = {
                id: String(mockUsers.length + 1),
                username: userData.username,
                name: userData.name,
                email: userData.email,
                status: userData.status,
                roleIds: userData.roleIds,
                avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${userData.username}`,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            // 在实际应用中，这里会由后端处理
            mockUsers.push(newUser);
            return newUser;
        } catch (error) {
            throw error;
        }
    },
    // 更新用户
    updateUser: async (userData)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.put<User>(`/users/${userData.id}`, userData);
            // 模拟更新用户
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const userIndex = mockUsers.findIndex((user)=>user.id === userData.id);
            if (userIndex === -1) {
                throw new Error('用户不存在');
            }
            const updatedUser = {
                ...mockUsers[userIndex],
                ...userData,
                updatedAt: new Date().toISOString()
            };
            // 在实际应用中，这里会由后端处理
            mockUsers[userIndex] = updatedUser;
            return updatedUser;
        } catch (error) {
            throw error;
        }
    },
    // 删除用户
    deleteUser: async (id)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.delete<void>(`/users/${id}`);
            // 模拟删除用户
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const userIndex = mockUsers.findIndex((user)=>user.id === id);
            if (userIndex === -1) {
                throw new Error('用户不存在');
            }
            // 在实际应用中，这里会由后端处理
            mockUsers.splice(userIndex, 1);
        } catch (error) {
            throw error;
        }
    }
};
const __TURBOPACK__default__export__ = userService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/role.service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "roleService": (()=>roleService)
});
// 模拟数据
const mockRoles = [
    {
        id: '1',
        name: '超级管理员',
        description: '拥有所有权限',
        menuIds: [
            '1',
            '2',
            '3',
            '4',
            '5',
            '6',
            '7',
            '8'
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        id: '2',
        name: '普通用户',
        description: '拥有基本权限',
        menuIds: [
            '1',
            '2'
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        id: '3',
        name: '访客',
        description: '只有查看权限',
        menuIds: [
            '1'
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    }
];
const roleService = {
    // 获取角色列表
    getRoles: async (params)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.get<PaginatedResponse<Role>>('/roles', { params });
            // 模拟获取角色列表
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const { page, pageSize } = params;
            const startIndex = (page - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const paginatedRoles = mockRoles.slice(startIndex, endIndex);
            return {
                data: paginatedRoles,
                total: mockRoles.length,
                page,
                pageSize
            };
        } catch (error) {
            throw error;
        }
    },
    // 获取所有角色（不分页）
    getAllRoles: async ()=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.get<Role[]>('/roles/all');
            // 模拟获取所有角色
            await new Promise((resolve)=>setTimeout(resolve, 300));
            return mockRoles;
        } catch (error) {
            throw error;
        }
    },
    // 获取单个角色
    getRole: async (id)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.get<Role>(`/roles/${id}`);
            // 模拟获取单个角色
            await new Promise((resolve)=>setTimeout(resolve, 300));
            const role = mockRoles.find((role)=>role.id === id);
            if (!role) {
                throw new Error('角色不存在');
            }
            return role;
        } catch (error) {
            throw error;
        }
    },
    // 创建角色
    createRole: async (roleData)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.post<Role>('/roles', roleData);
            // 模拟创建角色
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const newRole = {
                id: String(mockRoles.length + 1),
                name: roleData.name,
                description: roleData.description,
                menuIds: roleData.menuIds,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            // 在实际应用中，这里会由后端处理
            mockRoles.push(newRole);
            return newRole;
        } catch (error) {
            throw error;
        }
    },
    // 更新角色
    updateRole: async (roleData)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.put<Role>(`/roles/${roleData.id}`, roleData);
            // 模拟更新角色
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const roleIndex = mockRoles.findIndex((role)=>role.id === roleData.id);
            if (roleIndex === -1) {
                throw new Error('角色不存在');
            }
            const updatedRole = {
                ...mockRoles[roleIndex],
                ...roleData,
                updatedAt: new Date().toISOString()
            };
            // 在实际应用中，这里会由后端处理
            mockRoles[roleIndex] = updatedRole;
            return updatedRole;
        } catch (error) {
            throw error;
        }
    },
    // 删除角色
    deleteRole: async (id)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.delete<void>(`/roles/${id}`);
            // 模拟删除角色
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const roleIndex = mockRoles.findIndex((role)=>role.id === id);
            if (roleIndex === -1) {
                throw new Error('角色不存在');
            }
            // 在实际应用中，这里会由后端处理
            mockRoles.splice(roleIndex, 1);
        } catch (error) {
            throw error;
        }
    }
};
const __TURBOPACK__default__export__ = roleService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/menu.service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "menuService": (()=>menuService)
});
// 模拟数据
const mockMenus = [
    {
        id: '1',
        name: '仪表盘',
        path: '/dashboard',
        icon: 'dashboard',
        parentId: null,
        order: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        id: '2',
        name: '系统管理',
        path: '/system',
        icon: 'setting',
        parentId: null,
        order: 2,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        id: '3',
        name: '用户管理',
        path: '/system/users',
        icon: 'user',
        parentId: '2',
        order: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        id: '4',
        name: '角色管理',
        path: '/system/roles',
        icon: 'team',
        parentId: '2',
        order: 2,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    },
    {
        id: '5',
        name: '菜单管理',
        path: '/system/menus',
        icon: 'menu',
        parentId: '2',
        order: 3,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    }
];
// 构建菜单树
const buildMenuTree = (menus)=>{
    const menuMap = new Map();
    const result = [];
    // 先将所有菜单放入 Map 中
    menus.forEach((menu)=>{
        menuMap.set(menu.id, {
            ...menu,
            children: []
        });
    });
    // 构建树形结构
    menus.forEach((menu)=>{
        const menuWithChildren = menuMap.get(menu.id);
        if (menu.parentId === null) {
            // 根菜单
            result.push(menuWithChildren);
        } else {
            // 子菜单
            const parentMenu = menuMap.get(menu.parentId);
            if (parentMenu) {
                if (!parentMenu.children) {
                    parentMenu.children = [];
                }
                parentMenu.children.push(menuWithChildren);
            }
        }
    });
    // 对菜单进行排序
    const sortMenus = (menus)=>{
        return menus.sort((a, b)=>a.order - b.order).map((menu)=>({
                ...menu,
                children: menu.children ? sortMenus(menu.children) : undefined
            }));
    };
    return sortMenus(result);
};
const menuService = {
    // 获取菜单列表
    getMenus: async (params)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.get<PaginatedResponse<Menu>>('/menus', { params });
            // 模拟获取菜单列表
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const { page, pageSize } = params;
            const startIndex = (page - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const paginatedMenus = mockMenus.slice(startIndex, endIndex);
            return {
                data: paginatedMenus,
                total: mockMenus.length,
                page,
                pageSize
            };
        } catch (error) {
            throw error;
        }
    },
    // 获取菜单树
    getMenuTree: async ()=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.get<Menu[]>('/menus/tree');
            // 模拟获取菜单树
            await new Promise((resolve)=>setTimeout(resolve, 300));
            return buildMenuTree(mockMenus);
        } catch (error) {
            throw error;
        }
    },
    // 获取单个菜单
    getMenu: async (id)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.get<Menu>(`/menus/${id}`);
            // 模拟获取单个菜单
            await new Promise((resolve)=>setTimeout(resolve, 300));
            const menu = mockMenus.find((menu)=>menu.id === id);
            if (!menu) {
                throw new Error('菜单不存在');
            }
            return menu;
        } catch (error) {
            throw error;
        }
    },
    // 创建菜单
    createMenu: async (menuData)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.post<Menu>('/menus', menuData);
            // 模拟创建菜单
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const newMenu = {
                id: String(mockMenus.length + 1),
                name: menuData.name,
                path: menuData.path,
                icon: menuData.icon,
                parentId: menuData.parentId || null,
                order: menuData.order || mockMenus.length + 1,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            // 在实际应用中，这里会由后端处理
            mockMenus.push(newMenu);
            return newMenu;
        } catch (error) {
            throw error;
        }
    },
    // 更新菜单
    updateMenu: async (menuData)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.put<Menu>(`/menus/${menuData.id}`, menuData);
            // 模拟更新菜单
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const menuIndex = mockMenus.findIndex((menu)=>menu.id === menuData.id);
            if (menuIndex === -1) {
                throw new Error('菜单不存在');
            }
            const updatedMenu = {
                ...mockMenus[menuIndex],
                ...menuData,
                updatedAt: new Date().toISOString()
            };
            // 在实际应用中，这里会由后端处理
            mockMenus[menuIndex] = updatedMenu;
            return updatedMenu;
        } catch (error) {
            throw error;
        }
    },
    // 删除菜单
    deleteMenu: async (id)=>{
        try {
            // 实际项目中，这里会调用后端 API
            // return api.delete<void>(`/menus/${id}`);
            // 模拟删除菜单
            await new Promise((resolve)=>setTimeout(resolve, 500));
            const menuIndex = mockMenus.findIndex((menu)=>menu.id === id);
            if (menuIndex === -1) {
                throw new Error('菜单不存在');
            }
            // 在实际应用中，这里会由后端处理
            mockMenus.splice(menuIndex, 1);
        } catch (error) {
            throw error;
        }
    }
};
const __TURBOPACK__default__export__ = menuService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/index.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/auth.service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$user$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/user.service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$role$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/role.service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$menu$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/menu.service.ts [app-client] (ecmascript)");
;
;
;
;
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/index.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/api.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/auth.service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$user$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/user.service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$role$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/role.service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$menu$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/menu.service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/services/index.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "default": (()=>__TURBOPACK__default__export__),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/services/index.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/auth.service.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const AuthProvider = ({ children })=>{
    _s();
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // 获取当前用户信息
    const fetchCurrentUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AuthProvider.useCallback[fetchCurrentUser]": async ()=>{
            try {
                setLoading(true);
                setError(null);
                // 检查是否已登录
                if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].isAuthenticated()) {
                    setUser(null);
                    setLoading(false);
                    return;
                }
                // 获取当前用户信息
                const currentUser = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].getCurrentUser();
                setUser(currentUser);
            } catch (error) {
                console.error('获取用户信息失败:', error);
                setError('获取用户信息失败');
                // 清除无效的 token
                if ("TURBOPACK compile-time truthy", 1) {
                    localStorage.removeItem('token');
                }
                setUser(null);
            } finally{
                setLoading(false);
            }
        }
    }["AuthProvider.useCallback[fetchCurrentUser]"], []);
    // 登录
    const login = async (credentials)=>{
        try {
            setLoading(true);
            setError(null);
            // 调用登录 API
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].login(credentials);
            // 保存用户信息
            setUser(response.user);
        } catch (error) {
            console.error('登录失败:', error);
            setError(error.message || '登录失败');
            throw error;
        } finally{
            setLoading(false);
        }
    };
    // 登出
    const logout = async ()=>{
        try {
            setLoading(true);
            // 调用登出 API
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$auth$2e$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["authService"].logout();
            // 清除用户信息
            setUser(null);
        } catch (error) {
            console.error('登出失败:', error);
        } finally{
            setLoading(false);
        }
    };
    // 初始化时获取用户信息
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            fetchCurrentUser();
        }
    }["AuthProvider.useEffect"], [
        fetchCurrentUser
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: {
            user,
            loading,
            error,
            login,
            logout
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 94,
        columnNumber: 5
    }, this);
};
_s(AuthProvider, "TpPp4jL3sjVaBpBzKJKe3E/hBT0=");
_c = AuthProvider;
const useAuth = ()=>{
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
_s1(useAuth, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
const __TURBOPACK__default__export__ = AuthContext;
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/layout/ClientLayout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ClientLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$I18nProvider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/i18n/I18nProvider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__ConfigProvider$3e$__ = __turbopack_context__.i("[project]/node_modules/antd/es/config-provider/index.js [app-client] (ecmascript) <locals> <export default as ConfigProvider>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$locale$2f$zh_CN$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/locale/zh_CN.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$locale$2f$en_US$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/antd/locale/en_US.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$i18next$2f$dist$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/react-i18next/dist/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$i18next$2f$dist$2f$es$2f$useTranslation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-i18next/dist/es/useTranslation.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
function ClientLayout({ children }) {
    _s();
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const { i18n } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$i18next$2f$dist$2f$es$2f$useTranslation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslation"])();
    // 使用 useMemo 缓存 Ant Design 语言包，避免不必要的重新渲染
    const antdLocale = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useMemo({
        "ClientLayout.useMemo[antdLocale]": ()=>{
            return i18n.language === 'en' ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$locale$2f$en_US$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$locale$2f$zh_CN$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
        }
    }["ClientLayout.useMemo[antdLocale]"], [
        i18n.language
    ]);
    // 解决 hydration 不匹配问题
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ClientLayout.useEffect": ()=>{
            setMounted(true);
        }
    }["ClientLayout.useEffect"], []);
    if (!mounted) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$I18nProvider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$antd$2f$es$2f$config$2d$provider$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__$3c$export__default__as__ConfigProvider$3e$__["ConfigProvider"], {
            locale: antdLocale,
            theme: {
                token: {
                    colorPrimary: '#1677ff',
                    // 减少动画效果，提高响应速度
                    motion: {
                        easeInOut: 'cubic-bezier(0.645, 0.045, 0.355, 1)',
                        easeOut: 'cubic-bezier(0.215, 0.61, 0.355, 1)',
                        easeIn: 'cubic-bezier(0.55, 0.055, 0.675, 0.19)'
                    },
                    // 减少过渡时间
                    motionDurationMid: '0.1s',
                    motionDurationSlow: '0.2s'
                },
                components: {
                    Table: {
                        // 禁用表格动画，提高性能
                        motion: false
                    },
                    Menu: {
                        // 减少菜单动画时间
                        motionDurationSlow: '0.1s'
                    },
                    Layout: {
                        // 减少布局动画时间
                        motionDurationMid: '0.1s'
                    }
                }
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthProvider"], {
                children: mounted ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    id: "app-container",
                    children: children
                }, void 0, false, {
                    fileName: "[project]/src/components/layout/ClientLayout.tsx",
                    lineNumber: 69,
                    columnNumber: 11
                }, this) : null
            }, void 0, false, {
                fileName: "[project]/src/components/layout/ClientLayout.tsx",
                lineNumber: 66,
                columnNumber: 7
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/layout/ClientLayout.tsx",
            lineNumber: 35,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/layout/ClientLayout.tsx",
        lineNumber: 34,
        columnNumber: 5
    }, this);
}
_s(ClientLayout, "WMN0AFWP8HBs5JFUn1PFL/WSgXE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$i18next$2f$dist$2f$es$2f$useTranslation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslation"]
    ];
});
_c = ClientLayout;
var _c;
__turbopack_context__.k.register(_c, "ClientLayout");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_6dd66e48._.js.map