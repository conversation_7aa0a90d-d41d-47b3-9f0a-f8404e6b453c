import java.io.*;
import java.util.*;

public class ExcelSplitterDemo {
    
    public static void main(String[] args) {
        System.out.println("=== Excel MCCMNC数据分裂工具 ===");
        System.out.println("作者: Augment Agent");
        System.out.println("功能: 将MCCMNC列中包含'/'的数据分裂成多行");
        System.out.println("=====================================");
        
        try {
            // 检查输入文件
            String inputFile = "doc/test.xlsx";
            File excelFile = new File(inputFile);
            
            if (!excelFile.exists()) {
                System.err.println("错误: 找不到文件 " + inputFile);
                System.out.println("请确保文件存在于 doc 目录下");
                return;
            }
            
            System.out.println("✓ 找到Excel文件: " + inputFile);
            System.out.println("✓ 文件大小: " + excelFile.length() + " 字节");
            
            // 由于没有外部库，我们需要先将Excel转换为CSV
            String csvFile = "doc/test.csv";
            String outputCsvFile = "doc/test1.csv";
            
            // 检查CSV文件是否存在
            File csvInputFile = new File(csvFile);
            if (!csvInputFile.exists()) {
                System.out.println("\n⚠ 需要先将Excel转换为CSV格式");
                System.out.println("请按以下步骤操作：");
                System.out.println("1. 打开Excel文件: " + inputFile);
                System.out.println("2. 点击 文件 -> 另存为");
                System.out.println("3. 选择文件类型为 'CSV UTF-8 (逗号分隔)(*.csv)'");
                System.out.println("4. 保存为: " + csvFile);
                System.out.println("5. 重新运行此程序");
                System.out.println("\n等待CSV文件创建完成...");
                return;
            }
            
            // 处理CSV文件
            System.out.println("\n✓ 找到CSV文件，开始处理...");
            boolean success = processCsvFile(csvFile, outputCsvFile);
            
            if (success) {
                System.out.println("\n🎉 处理完成！");
                System.out.println("📁 结果文件: " + outputCsvFile);
                System.out.println("\n📋 后续步骤:");
                System.out.println("1. 打开Excel");
                System.out.println("2. 导入 " + outputCsvFile + " 文件");
                System.out.println("3. 另存为 doc/test1.xlsx");
                
                // 创建一个简单的批处理文件来帮助用户
                createBatchFile();
            }
            
        } catch (Exception e) {
            System.err.println("❌ 处理过程中出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public static boolean processCsvFile(String inputFile, String outputFile) {
        try {
            List<String[]> allRows = new ArrayList<>();
            int mccmncColumnIndex = -1;
            int originalRowCount = 0;
            int splitRowCount = 0;
            
            System.out.println("📖 正在读取CSV文件...");
            
            // 读取CSV文件
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(new FileInputStream(inputFile), "UTF-8"))) {
                
                String line;
                boolean isFirstLine = true;
                
                while ((line = reader.readLine()) != null) {
                    originalRowCount++;
                    String[] columns = parseCSVLine(line);
                    
                    if (isFirstLine) {
                        // 处理表头
                        System.out.println("📋 表头信息: " + Arrays.toString(columns));
                        
                        for (int i = 0; i < columns.length; i++) {
                            if ("MCCMNC".equalsIgnoreCase(columns[i].trim())) {
                                mccmncColumnIndex = i;
                                break;
                            }
                        }
                        
                        if (mccmncColumnIndex == -1) {
                            System.err.println("❌ 错误: 未找到MCCMNC列");
                            System.out.println("可用的列: " + Arrays.toString(columns));
                            return false;
                        }
                        
                        System.out.println("✓ 找到MCCMNC列，位置: 第" + (mccmncColumnIndex + 1) + "列");
                        allRows.add(columns);
                        isFirstLine = false;
                        continue;
                    }
                    
                    // 处理数据行
                    if (mccmncColumnIndex < columns.length) {
                        String mccmncValue = columns[mccmncColumnIndex];
                        
                        if (mccmncValue != null && mccmncValue.contains("/")) {
                            // 分割包含'/'的数据
                            String[] parts = mccmncValue.split("/");
                            splitRowCount++;
                            
                            System.out.println("🔄 行 " + originalRowCount + ": '" + 
                                             mccmncValue + "' → 分裂为 " + parts.length + " 行");
                            
                            // 为每个分割后的值创建新行
                            for (String part : parts) {
                                String[] newRow = columns.clone();
                                newRow[mccmncColumnIndex] = part.trim();
                                allRows.add(newRow);
                            }
                        } else {
                            // 不包含'/'的行直接添加
                            allRows.add(columns);
                        }
                    } else {
                        // 如果该行的列数不够，直接添加
                        allRows.add(columns);
                    }
                }
            }
            
            System.out.println("💾 正在保存结果文件...");
            
            // 写入结果文件
            try (PrintWriter writer = new PrintWriter(
                    new OutputStreamWriter(new FileOutputStream(outputFile), "UTF-8"))) {
                
                for (String[] row : allRows) {
                    writer.println(formatCSVLine(row));
                }
            }
            
            System.out.println("\n📊 === 处理结果统计 ===");
            System.out.println("📥 原始数据行数: " + originalRowCount + " 行");
            System.out.println("🔄 分裂的数据行: " + splitRowCount + " 行");
            System.out.println("📤 处理后行数: " + allRows.size() + " 行");
            System.out.println("📁 输出文件: " + outputFile);
            
            return true;
            
        } catch (IOException e) {
            System.err.println("❌ 文件处理错误: " + e.getMessage());
            return false;
        }
    }
    
    // 解析CSV行（处理引号和逗号）
    private static String[] parseCSVLine(String line) {
        List<String> fields = new ArrayList<>();
        StringBuilder currentField = new StringBuilder();
        boolean inQuotes = false;
        
        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);
            
            if (c == '"') {
                if (inQuotes && i + 1 < line.length() && line.charAt(i + 1) == '"') {
                    // 转义的引号
                    currentField.append('"');
                    i++; // 跳过下一个引号
                } else {
                    inQuotes = !inQuotes;
                }
            } else if (c == ',' && !inQuotes) {
                fields.add(currentField.toString());
                currentField = new StringBuilder();
            } else {
                currentField.append(c);
            }
        }
        
        fields.add(currentField.toString());
        return fields.toArray(new String[0]);
    }
    
    // 格式化CSV行（添加必要的引号）
    private static String formatCSVLine(String[] fields) {
        StringBuilder line = new StringBuilder();
        
        for (int i = 0; i < fields.length; i++) {
            if (i > 0) {
                line.append(",");
            }
            
            String field = fields[i];
            if (field.contains(",") || field.contains("\"") || field.contains("\n") || field.contains("\r")) {
                // 需要引号包围
                line.append("\"").append(field.replace("\"", "\"\"")).append("\"");
            } else {
                line.append(field);
            }
        }
        
        return line.toString();
    }
    
    // 创建批处理文件帮助用户
    private static void createBatchFile() {
        try {
            PrintWriter writer = new PrintWriter(new FileWriter("run_excel_splitter.bat"));
            writer.println("@echo off");
            writer.println("echo Excel数据分裂工具");
            writer.println("echo ==================");
            writer.println("java ExcelSplitterDemo");
            writer.println("pause");
            writer.close();
            System.out.println("✓ 已创建批处理文件: run_excel_splitter.bat");
        } catch (IOException e) {
            System.out.println("⚠ 无法创建批处理文件: " + e.getMessage());
        }
    }
}
