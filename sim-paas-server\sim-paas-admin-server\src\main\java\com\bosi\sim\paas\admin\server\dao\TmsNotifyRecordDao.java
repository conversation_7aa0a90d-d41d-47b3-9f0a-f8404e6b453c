package com.bosi.sim.paas.admin.server.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.dao.model.tds.TdsTenant;
import com.bosi.sim.paas.dao.model.tds.TdsNotifyRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 后台角色管理自定义Dao
 */
public interface TmsNotifyRecordDao {

    Page<TdsNotifyRecord> page(Page<TdsNotifyRecord> iPage, @Param("params") TdsNotifyRecord tmsNotifyRecord);

    Page<TdsTenant> pageDistributorForNotify(Page<TdsTenant> page, @Param("params") TdsTenant distributor);

    int insertList(@Param("params") List<TdsNotifyRecord> tmsNotifyRecordList);

}
