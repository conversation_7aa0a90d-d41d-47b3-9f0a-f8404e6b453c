package com.bosi.sim.paas.open.server.domain.vo;

import com.bosi.sim.paas.dao.model.sds.SdsMemberReceiveAddress;
import com.bosi.sim.paas.dao.model.sds.SmsCouponHistory;
import com.bosi.sim.paas.open.server.domain.dto.CartPromotionItemDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * 确认单信息封装
 */
@Data
public class ConfirmOrderVo {
    @ApiModelProperty("包含优惠信息的购物车信息")
    private List<CartPromotionItemDto> cartPromotionItemList;
    @ApiModelProperty("用户收货地址列表")
    private List<SdsMemberReceiveAddress> memberReceiveAddressList;
    @ApiModelProperty("用户可用优惠券列表")
    private List<SmsCouponHistory> couponHistoryList;
    @ApiModelProperty("计算的金额")
    private CalcAmount calcAmount;

    @Getter
    @Setter
    public static class CalcAmount {
        @ApiModelProperty("订单商品总金额")
        private BigDecimal totalAmount;
        @ApiModelProperty("活动优惠")
        private BigDecimal promotionAmount;
        @ApiModelProperty("应付金额")
        private BigDecimal payAmount;
    }
}
