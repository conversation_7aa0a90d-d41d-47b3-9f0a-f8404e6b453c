package com.bosi.esim.mall.dao.model.pms;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.esim.mall.dao.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@TableName("pms_attribute")
public class PmsAttribute extends BaseEntity {

    private String name;

    @ApiModelProperty(value = "规格数量")
    private Integer specCount;

    @ApiModelProperty(value = "参数数量")
    private Integer paramCount;

    @TableField(exist = false)
    private List<PmsAttributeType> attributeTypeList;

}
