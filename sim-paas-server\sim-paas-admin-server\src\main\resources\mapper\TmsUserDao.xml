<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bosi.sim.paas.admin.server.dao.TmsUserDao">

    <select id="selectUserList" resultType="com.bosi.sim.paas.dao.model.tds.TdsUser">
		select
		    u.*, d.distributor_name
		from tms_user u
		left join tms_distributor d on u.distributor_id = d.id
		where u.whether_delete = false
		and d.whether_delete = false
		<if test="params.id != null">
			AND u.id = #{params.id}
		</if>
		<if test="params.userName != null and params.userName != ''">
			AND u.user_name like concat('%', #{params.userName}, '%')
		</if>
		<if test="params.whetherEnable != null">
			AND u.whether_enable = #{params.whetherEnable}
		</if>
		<if test="params.phone != null and params.phone != ''">
			AND u.phone like concat('%', #{params.phone}, '%')
		</if>
		<if test="params.queryBeginTime != null and params.queryBeginTime != ''"><!-- 开始时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.queryBeginTime},'%y%m%d')
		</if>
		<if test="params.queryEndTime != null and params.queryEndTime != ''"><!-- 结束时间检索 -->
			AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.queryEndTime},'%y%m%d')
		</if>
		<if test="params.distributorId != null">
			AND u.distributor_id = #{params.distributorId}
		</if>
	</select>

	<select id="selectUserById" resultType="com.bosi.sim.paas.dao.model.tds.TdsUser">
		select
			u.*, d.distributor_name
		from tms_user u
				 left join tms_distributor d on u.distributor_id = d.id
		where u.id = #{userId} and u.whether_delete = false and d.whether_delete = false
	</select>


	<select id="selectUserByUserName" parameterType="String" resultType="com.bosi.sim.paas.dao.model.tds.TdsUser">
		select
			u.*, d.distributor_name
		from tms_user u
				 left join tms_distributor d on u.distributor_id = d.id
		where u.user_name = #{userName} and u.whether_delete = false and d.whether_delete = false
	</select>

	<select id="checkUserNameUnique" parameterType="String" resultType="com.bosi.sim.paas.dao.model.tds.TdsUser">
		select id, user_name from tms_user where user_name = #{userName} and whether_delete = false limit 1
	</select>

	<select id="checkPhoneUnique" parameterType="String" resultType="com.bosi.sim.paas.dao.model.tds.TdsUser">
		select id, phone from tms_user where phone = #{phone} and whether_delete = false limit 1
	</select>

	<select id="checkEmailUnique" parameterType="String" resultType="com.bosi.sim.paas.dao.model.tds.TdsUser">
		select id, email from tms_user where email = #{email} and whether_delete = false limit 1
	</select>

	<update id="updateUserAvatar">
 		update tms_user set avatar = #{avatar} where user_name = #{userName} and whether_delete = false
	</update>

	<update id="resetUserPwd">
 		update tms_user set password = #{password} where user_name = #{userName} and whether_delete = false
	</update>

</mapper>
