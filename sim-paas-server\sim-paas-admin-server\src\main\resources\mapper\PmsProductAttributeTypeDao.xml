<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bosi.sim.paas.admin.server.dao.PmsProductAttributeTypeDao">

    <insert id="insertList" useGeneratedKeys="true" keyProperty="id">
        <foreach collection="list" item="item" separator=";">
            insert into sds_product_attribute_type
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">id,</if>
                <if test="item.productId != null">product_id,</if>
                <if test="item.attributeTypeId != null">attribute_type_id,</if>
                <if test="item.value != null">value,</if>
            </trim>
            values
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">#{item.id},</if>
                <if test="item.productId != null">#{item.productId},</if>
                <if test="item.attributeTypeId != null">#{item.attributeTypeId},</if>
                <if test="item.value != null">#{item.value},</if>
            </trim>
        </foreach>
    </insert>

</mapper>
