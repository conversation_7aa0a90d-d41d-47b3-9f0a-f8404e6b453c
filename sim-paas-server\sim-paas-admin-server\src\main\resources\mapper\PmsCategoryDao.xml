<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bosi.sim.paas.admin.server.dao.PmsCategoryDao">
    <resultMap id="listWithChildrenMap" type="com.bosi.sim.paas.dao.model.sds.PmsCategory" >
        <id     property="id"       	column="id"           />
        <result property="name"       column="name"      />
        <collection  property="children"  columnPrefix="child_" resultMap="ChildrenMap"/>
    </resultMap>

    <resultMap id="ChildrenMap" type="com.bosi.sim.paas.dao.model.sds.PmsCategory" >
        <id     property="id"       	column="id"           />
        <result property="name"       column="name"      />
    </resultMap>

    <select id="listWithChildren" resultMap="listWithChildrenMap">
        select
            c1.id,
            c1.name,
            c2.id   child_id,
            c2.name child_name
        from
            sds_category c1
        left join
            sds_category c2 on c1.id = c2.parent_id
        where
            c1.parent_id = '0'
        and
            c1.whether_delete = false
    </select>
</mapper>
