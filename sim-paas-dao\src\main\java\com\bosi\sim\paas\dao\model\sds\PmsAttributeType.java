package com.bosi.esim.mall.dao.model.pms;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.esim.mall.dao.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("pms_attribute_type")
public class PmsAttributeType extends BaseEntity {
    private String attributeId;

    private String name;

    private String code;

    @ApiModelProperty(value = "属性选择类型：1->单选；2->多选")
    private Integer selectType;

    @ApiModelProperty(value = "可选值列表，以逗号隔开")
    private String inputList;

    @ApiModelProperty(value = "排序字段：最高的可以单独上传图片")
    private Integer sort;

    @ApiModelProperty(value = "属性的类型；1->规格；2->参数")
    private Integer classify;

}
