package com.bosi.sim.paas.admin.server.controller;

import com.bosi.sim.paas.common.core.api.BizCode;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.exception.CommonResult;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.PageUtil;
import com.bosi.sim.paas.common.optlog.annotation.OperateLog;
import com.bosi.sim.paas.common.security.annotation.RequiresPermissions;
import com.bosi.sim.paas.dao.model.tds.TdsRole;
import com.bosi.sim.paas.admin.server.service.TmsRoleService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色信息
 */
@RestController
@Api(tags = "TmsRoleController", value = "角色管理")
@RequestMapping("/tms/role")
public class TmsRoleController {
    @Autowired
    private TmsRoleService roleService;

    @RequiresPermissions("tms:role:page")
    @GetMapping("/page")
    public CommonResult page(@RequestParam(value = "pageNum") Integer pageNum,
                             @RequestParam(value = "pageSize") Integer pageSize, TdsRole role) {
        CommonPage<TdsRole> page = roleService.page(PageUtil.buildPage(pageNum, pageSize, "whether_admin", false), role);
        return CommonResult.success(page);
    }

    @RequiresPermissions("tms:role:list")
    @GetMapping("/list")
    public CommonResult list(TdsRole role) {
        List<TdsRole> list = roleService.list(role);
        List<TdsRole> result = list.stream().filter(r -> !r.getWhetherAdmin()).collect(Collectors.toList());
        return CommonResult.success(result);
    }

    /**
     * 根据角色编号获取详细信息
     */
    @RequiresPermissions("tms:role:query")
    @GetMapping("/{id}")
    public CommonResult getInfo(@PathVariable String id) {
        return CommonResult.success(roleService.selectRoleById(id));
    }

    /**
     * 新增角色
     */
    @RequiresPermissions("tms:role:add")
    @OperateLog("新增角色")
    @PostMapping
    public CommonResult add(@Validated @RequestBody TdsRole role) {
        if (!roleService.checkRoleNameUnique(role.getId(), role.getRoleName(), role.getDistributorId())) {
            throw BizException.build(BizCode.ROLE_NAME_REPEAT);
        }
        return CommonResult.success(roleService.insertRole(role));

    }

    /**
     * 修改保存角色
     */
    @RequiresPermissions("tms:role:edit")
    @OperateLog("修改角色")
    @PutMapping
    public CommonResult edit(@Validated @RequestBody TdsRole role) {
        if (!roleService.checkRoleNameUnique(role.getId(), role.getRoleName(), role.getDistributorId())) {
            throw BizException.build(BizCode.ROLE_NAME_REPEAT);
        }
        return CommonResult.success(roleService.updateRole(role));
    }

    /**
     * 状态修改
     */
    @RequiresPermissions("tms:role:edit")
    @OperateLog("修改角色状态")
    @PutMapping("/changeStatus")
    public CommonResult changeStatus(@RequestBody TdsRole role) {
        return CommonResult.success(roleService.updateRoleStatus(role));
    }

    /**
     * 删除角色
     */
    @RequiresPermissions("tms:role:remove")
    @OperateLog("删除角色")
    @DeleteMapping("/{ids}")
    public CommonResult remove(@PathVariable String[] ids) {
        return CommonResult.success(roleService.deleteRoleByIds(ids));
    }

}
