package com.bosi.sim.paas.open.server.dao;

import com.bosi.sim.paas.dao.model.sds.OmsOrder;
import com.bosi.sim.paas.dao.model.sds.OmsOrderItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 前台订单管理自定义Dao
 */
public interface OmsOrderDao {
    /**
     * 获取订单及下单商品详情
     */
    OmsOrder getDetailByOrderId(@Param("orderId") String orderId);

    OmsOrder getDetailByOrderSn(@Param("orderSn") String orderSn);

    /**
     * 修改 sds_product_sku表的锁定库存及真实库存
     */
    int updateSkuStock(@Param("itemList") List<OmsOrderItem> orderItemList);

    /**
     * 获取超时订单
     *
     * @param minute 超时时间（分）
     */
    List<OmsOrder> getTimeOutOrders(@Param("minute") Integer minute);

    /**
     * 解除取消订单的库存锁定
     */
    int releaseSkuStockLock(@Param("itemList") List<OmsOrderItem> orderItemList);

    /**
     * 根据商品的skuId来锁定库存
     */
    int lockStockBySkuId(@Param("productSkuId") String productSkuId, @Param("quantity") Integer quantity);

    /**
     * 根据商品的skuId扣减真实库存
     */
    int reduceSkuStock(@Param("productSkuId") String productSkuId, @Param("quantity") Integer quantity);

    /**
     * 根据商品的skuId释放库存
     */
    int releaseStockBySkuId(@Param("productSkuId") String productSkuId, @Param("quantity") Integer quantity);
}
