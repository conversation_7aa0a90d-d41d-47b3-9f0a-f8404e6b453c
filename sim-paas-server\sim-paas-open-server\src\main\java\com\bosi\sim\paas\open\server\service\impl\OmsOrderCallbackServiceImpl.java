package com.bosi.sim.paas.open.server.service.impl;

import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bosi.sim.paas.common.core.api.BizCode;
import com.bosi.sim.paas.common.core.api.SystemCode;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.dao.enums.sds.SdsOrderStatusEnum;
import com.bosi.sim.paas.dao.mapper.oms.OmsOrderMapper;
import com.bosi.sim.paas.dao.model.sds.OmsOrder;
import com.bosi.sim.paas.dao.model.sds.OmsOrderItem;
import com.bosi.sim.paas.dao.model.sds.jsonclz.ReceiverDetailJsonClass;
import com.bosi.sim.paas.open.server.config.odoo.OdooProperties;
import com.bosi.sim.paas.open.server.config.rest.RestTemplateService;
import com.bosi.sim.paas.open.server.dao.OmsOrderDao;
import com.bosi.sim.paas.open.server.service.OmsOrderCallbackService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;

/**
 * 前台订单管理Service
 */
@Slf4j
@Service
public class OmsOrderCallbackServiceImpl implements OmsOrderCallbackService {
    @Autowired
    private OmsOrderMapper orderMapper;

    @Autowired
    private OmsOrderDao omsOrderDao;

    @Autowired
    private RestTemplateService restTemplateService;

    @Autowired
    private OdooProperties odooProperties;

    @Override
    @Transactional
    public void paySuccess(String partnerOrderId) {
        LambdaQueryWrapper<OmsOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OmsOrder::getPartnerOrderId, partnerOrderId);
        OmsOrder orderInfo = orderMapper.selectOne(queryWrapper);
        if (orderInfo == null) {
            throw BizException.build(SystemCode.RESOURCE_NOTFOUND);
        }
        //修改订单支付状态
        OmsOrder order = new OmsOrder();
        order.setId(orderInfo.getId());
        order.setStatus(SdsOrderStatusEnum.WAITDELIVERY.getOrderStatus());
        order.setPaymentTime(new Date());

        LambdaQueryWrapper<OmsOrder> orderExample = new LambdaQueryWrapper<>();
        orderExample.eq(OmsOrder::getId, order.getId());
        orderExample.eq(OmsOrder::getStatus, SdsOrderStatusEnum.WAITPAY.getOrderStatus());

        //只修改未付款状态的订单
        int updateCount = orderMapper.update(order, orderExample);
        if (updateCount == 0) {
            throw BizException.build(BizCode.ORDER_STATUS_ERROR);
        }
        //恢复所有下单商品的锁定库存，扣减真实库存
        OmsOrder orderDetail = omsOrderDao.getDetailByOrderId(orderInfo.getId());
        for (OmsOrderItem orderItem : orderDetail.getOrderItemList()) {
            int count = omsOrderDao.reduceSkuStock(orderItem.getProductSkuId(), orderItem.getProductQuantity());
            if (count == 0) {
                throw BizException.build(BizCode.ORDER_SKU_NOT_ENOUGH);
            }
        }
        createOdooOrder(orderDetail);
    }

    public void createOdooOrder(OmsOrder omsOrder) {
        try {
            JSONObject body = new JSONObject();
            body.put("customer_order_ref", omsOrder.getOrderSn());
            body.put("shipping_address", buildAddress(JSON.parseObject(omsOrder.getReceiverDetailJson(), ReceiverDetailJsonClass.class)));
            body.put("payment_method", "prepaid");
            body.put("email", omsOrder.getMemberUsername());
            body.put("note", omsOrder.getNote());
            body.put("order_lines", buildOrderLines(omsOrder));

            String bodyStr = body.toJSONString();

            String digestStr = (odooProperties.getAuthSecret() + bodyStr + odooProperties.getChannelId()).replaceAll(" ", "");
            String sign_value = MD5.create().digestHex(digestStr, StandardCharsets.UTF_8);
            HttpHeaders headers = new HttpHeaders();
            headers.set("X-Channel-Id", odooProperties.getChannelId());
            headers.set("X-Channel-Language", odooProperties.getChannelLanguage());
            headers.set("X-Sign-Method", odooProperties.getSignMethod());
            headers.set("X-Sign-Value", sign_value);

            String url = odooProperties.getAddress() + odooProperties.getCreateOrderUri();
            RequestEntity<JSONObject> requestEntity = new RequestEntity<>(body, headers, HttpMethod.POST, URI.create(url));
            ResponseEntity<JSONObject> responseEntity = restTemplateService.exchange(requestEntity, JSONObject.class);
            if (!responseEntity.getStatusCode().is2xxSuccessful()) {
                String msg = String.format("create odoo order failed,error info:url=%s,headers=%s,requestBody=%s,responseStatus=%s", url, headers,
                        bodyStr, responseEntity.getStatusCode());
                log.error(msg);
                throw BizException.build(BizCode.ODOO_CREATE_ORDER_REQUEST_ERROR);
            }
            JSONObject responseBody = responseEntity.getBody();
            if (responseBody == null) {
                String msg = String.format("create odoo order failed,error info:url=%s,headers=%s,requestBody=%s", url, headers, bodyStr);
                log.error(msg);
                throw BizException.build(BizCode.ODOO_CREATE_ORDER_REQUEST_ERROR);
            }
            JSONObject result = responseBody.getJSONObject("result");
            if (!responseBody.getString("status").equals("ok") || result == null) {
                String msg = String.format("create odoo order failed,error info:url=%s,headers=%s,requestBody=%s,responseBody=%s", url, headers,
                        bodyStr, responseBody.toJSONString());
                log.error(msg);
                throw BizException.build(BizCode.ODOO_CREATE_ORDER_REQUEST_ERROR);
            }
        } catch (Exception e) {
            log.error("createOdooOrder error", e);
        }
    }

    private List<JSONObject> buildOrderLines(OmsOrder omsOrder) {
        List<JSONObject> orderLines = Lists.newArrayList();
        for (OmsOrderItem orderItem : omsOrder.getOrderItemList()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("customer_order_line_ref", orderItem.getOrderSn() + "-" + orderItem.getId());
            jsonObject.put("product_code", orderItem.getProductSkuCode());
            jsonObject.put("product_uom_qty", orderItem.getProductQuantity());
            if (StringUtils.isNotEmpty(orderItem.getDeviceUidJson())) {
                String card_uid = StringUtils.join(JSON.parseArray(orderItem.getDeviceUidJson(), String.class), ",");
                jsonObject.put("card_uid", card_uid);
            }
            orderLines.add(jsonObject);
        }
        return orderLines;
    }

    private JSONObject buildAddress(ReceiverDetailJsonClass receiverDetailJsonClass) {
        JSONObject shopping_address = new JSONObject();
        if (receiverDetailJsonClass == null) {
            return shopping_address;
        }
        if (StringUtils.isNotEmpty(receiverDetailJsonClass.getReceiverName())) {
            shopping_address.put("name", receiverDetailJsonClass.getReceiverName());
        }
        String address = "";
        if (StringUtils.isNotEmpty(receiverDetailJsonClass.getReceiverRegion())) {
            if (StringUtils.isEmpty(address)) {
                address = address + receiverDetailJsonClass.getReceiverRegion();
            } else {
                address = address + "-" + receiverDetailJsonClass.getReceiverRegion();
            }
        }
        if (StringUtils.isNotEmpty(receiverDetailJsonClass.getReceiverCountry())) {
            if (StringUtils.isEmpty(address)) {
                address = address + receiverDetailJsonClass.getReceiverCountry();
            } else {
                address = address + "-" + receiverDetailJsonClass.getReceiverCountry();
            }
        }
        if (StringUtils.isNotEmpty(receiverDetailJsonClass.getLevelAddresses())) {
            if (StringUtils.isEmpty(address)) {
                address = address + receiverDetailJsonClass.getLevelAddresses();
            } else {
                address = address + "-" + receiverDetailJsonClass.getLevelAddresses();
            }
        }
        if (StringUtils.isNotEmpty(receiverDetailJsonClass.getDetailAddress())) {
            if (StringUtils.isEmpty(address)) {
                address = address + receiverDetailJsonClass.getDetailAddress();
            } else {
                address = address + "-" + receiverDetailJsonClass.getDetailAddress();
            }
        }
        if (StringUtils.isNotEmpty(address)) {
            shopping_address.put("address", address);
        }
        if (StringUtils.isNotEmpty(receiverDetailJsonClass.getReceiverPhone())) {
            shopping_address.put("phone", receiverDetailJsonClass.getReceiverPhone());
        }
        return shopping_address;
    }

}

