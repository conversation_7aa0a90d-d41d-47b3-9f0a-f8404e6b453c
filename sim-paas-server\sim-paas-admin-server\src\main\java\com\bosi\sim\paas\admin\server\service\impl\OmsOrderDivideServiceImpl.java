package com.bosi.sim.paas.admin.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.BizCode;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.dao.enums.sds.SdsOrderStatusEnum;
import com.bosi.sim.paas.dao.enums.sds.SdsSettleSituationEnum;
import com.bosi.sim.paas.dao.enums.tds.TdsMovementTypeEnum;
import com.bosi.sim.paas.dao.enums.tds.TdsSettleModeEnum;
import com.bosi.sim.paas.dao.mapper.oms.OmsOrderDivideMapper;
import com.bosi.sim.paas.dao.mapper.oms.OmsOrderDivideSettleHistoryMapper;
import com.bosi.sim.paas.dao.mapper.oms.OmsOrderDivideSettleMapper;
import com.bosi.sim.paas.dao.mapper.oms.OmsOrderMapper;
import com.bosi.sim.paas.dao.mapper.tms.TmsUserAccountMapper;
import com.bosi.sim.paas.dao.model.sds.OmsOrder;
import com.bosi.sim.paas.dao.model.sds.OmsOrderDivide;
import com.bosi.sim.paas.dao.model.sds.OmsOrderDivideSettle;
import com.bosi.sim.paas.dao.model.sds.OmsOrderDivideSettleHistory;
import com.bosi.sim.paas.dao.model.tds.TdsTenantAccount;
import com.bosi.sim.paas.dao.pojo.dto.SdsOrderDivideInfoByAccountDto;
import com.bosi.sim.paas.dao.pojo.dto.SdsOrderDivideInfoByDistributorDto;
import com.bosi.sim.paas.dao.pojo.dto.SdsOrderDivideInfoDto;
import com.bosi.sim.paas.admin.server.config.rabbitmq.RabbitMqAccountMovementProducer;
import com.bosi.sim.paas.admin.server.dao.OmsOrderDivideDao;
import com.bosi.sim.paas.admin.server.dao.OmsOrderDivideSettleDao;
import com.bosi.sim.paas.admin.server.service.OmsOrderDivideService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 分销商管理 服务实现
 */
@Service
@Slf4j
public class OmsOrderDivideServiceImpl implements OmsOrderDivideService {

    @Autowired
    private OmsOrderMapper omsOrderMapper;

    @Autowired
    private OmsOrderDivideMapper omsOrderDivideMapper;

    @Autowired
    private OmsOrderDivideSettleHistoryMapper omsOrderDivideSettleHistoryMapper;

    @Autowired
    private OmsOrderDivideDao omsOrderDivideDao;

    @Autowired
    private OmsOrderDivideSettleMapper omsOrderDivideSettleMapper;

    @Autowired
    private OmsOrderDivideSettleDao omsOrderDivideSettleDao;

    @Autowired
    private TmsUserAccountMapper tmsUserAccountMapper;

    @Autowired
    private RabbitMqAccountMovementProducer rabbitMqAccountMovementProducer;

    @Override
    public CommonPage<OmsOrderDivide> page(Page<OmsOrderDivide> page, OmsOrderDivide omsOrderDivide) {
        Page<OmsOrderDivide> result = omsOrderDivideDao.page(page, omsOrderDivide);
        if (CollUtil.isNotEmpty(result.getRecords())) {
            List<String> divideIds = Lists.transform(result.getRecords(), OmsOrderDivide::getId);
            List<OmsOrderDivideSettle> list = omsOrderDivideSettleDao.listByDivideIds(divideIds);
            Map<String, List<OmsOrderDivideSettle>> map = Maps.newHashMap();
            list.forEach(omsOrderDivideSettle -> {
                List<OmsOrderDivideSettle> divideSettles = map.get(omsOrderDivideSettle.getOrderDivideId());
                if (CollUtil.isEmpty(divideSettles)) {
                    divideSettles = Lists.newArrayList();
                }
                divideSettles.add(omsOrderDivideSettle);
                map.put(omsOrderDivideSettle.getOrderDivideId(), divideSettles);
            });
            result.getRecords().forEach(divide -> {
                divide.setOmsOrderDivideSettleList(map.get(divide.getId()));
                divide.setTotalRealAmount(divide.getPerRealAmount().multiply(new BigDecimal(divide.getProductQuantity())));
            });
        }
        return CommonPage.restPage(result);
    }

    @Override
    public SdsOrderDivideInfoDto list(OmsOrderDivide omsOrderDivide) {
        List<OmsOrderDivide> list = omsOrderDivideDao.listNoSettleByParam(omsOrderDivide);
        list.forEach(divide -> divide.setTotalRealAmount(divide.getPerRealAmount().multiply(new BigDecimal(divide.getProductQuantity()))));
        BigDecimal totalAmount = new BigDecimal("0.00");
        long totalNumber = 0L;
        List<String> orderDivideSettleIds = Lists.newArrayList();
        Map<String, SdsOrderDivideInfoByDistributorDto> groupByDistributorMap = Maps.newHashMap();
        Map<String, SdsOrderDivideInfoByAccountDto> groupByAccountMap = Maps.newHashMap();
        for (OmsOrderDivide orderDivide : list) {
            SdsOrderDivideInfoByDistributorDto distributorDto = groupByDistributorMap.get(orderDivide.getDistributorId());
            if (distributorDto == null) {
                distributorDto = new SdsOrderDivideInfoByDistributorDto();
                distributorDto.setDistributorName(orderDivide.getDistributorName());
                distributorDto.setDistributorId(orderDivide.getDistributorId());
                distributorDto.setAmountCount(new BigDecimal("0.00"));
            }
            for (OmsOrderDivideSettle settle : orderDivide.getOmsOrderDivideSettleList()) {
                SdsOrderDivideInfoByAccountDto accountDto = groupByAccountMap.get(settle.getAccountId());
                if (accountDto == null) {
                    accountDto = new SdsOrderDivideInfoByAccountDto();
                    accountDto.setAccountUserName(settle.getAccountUserName());
                    accountDto.setAccountId(settle.getAccountId());
                    accountDto.setAmountCount(new BigDecimal("0.00"));
                }
                accountDto.setAmountCount(accountDto.getAmountCount().add(settle.getSettleAmount()));
                groupByAccountMap.put(settle.getAccountId(), accountDto);

                orderDivideSettleIds.add(settle.getId());
                distributorDto.setAmountCount(distributorDto.getAmountCount().add(settle.getSettleAmount()));
                totalAmount = totalAmount.add(settle.getSettleAmount());
                totalNumber = totalNumber + 1;
            }
            groupByDistributorMap.put(orderDivide.getDistributorId(), distributorDto);
        }
        List<SdsOrderDivideInfoByDistributorDto> groupByDistributor = Lists.newArrayList();
        List<SdsOrderDivideInfoByAccountDto> groupByAccount = Lists.newArrayList();
        for (String id : groupByDistributorMap.keySet()) {
            groupByDistributor.add(groupByDistributorMap.get(id));
        }
        for (String id : groupByAccountMap.keySet()) {
            groupByAccount.add(groupByAccountMap.get(id));
        }
        SdsOrderDivideInfoDto omsOrderDivideInfoDto = new SdsOrderDivideInfoDto();
        omsOrderDivideInfoDto.setOrderDivideList(list);
        omsOrderDivideInfoDto.setTotalAmount(totalAmount);
        omsOrderDivideInfoDto.setTotalNumber(totalNumber);
        omsOrderDivideInfoDto.setGroupByDistributor(groupByDistributor);
        omsOrderDivideInfoDto.setGroupByAccount(groupByAccount);
        omsOrderDivideInfoDto.setOrderDivideSettleIds(orderDivideSettleIds);
        return omsOrderDivideInfoDto;
    }

    @Override
    @Async
    @Transactional
    public void tosettle(String jobId, List<String> omsOrderDivideSettleIds) {
        LambdaQueryWrapper<OmsOrderDivideSettle> settleQuery = new LambdaQueryWrapper<>();
        settleQuery.in(OmsOrderDivideSettle::getId, omsOrderDivideSettleIds);
        List<OmsOrderDivideSettle> omsOrderDivideSettleList = omsOrderDivideSettleMapper.selectList(settleQuery);
        Map<String, OmsOrderDivideSettle> settleMap = Maps.newHashMap();
        Set<String> orderDivideIds = Sets.newHashSet();
        Set<String> accountIds = Sets.newHashSet();
        for (OmsOrderDivideSettle settle : omsOrderDivideSettleList) {
            settleMap.put(settle.getId(), settle);
            orderDivideIds.add(settle.getOrderDivideId());
            accountIds.add(settle.getAccountId());
        }

        LambdaQueryWrapper<TdsTenantAccount> accountQuery = new LambdaQueryWrapper<>();
        accountQuery.in(TdsTenantAccount::getId, accountIds);
        List<TdsTenantAccount> accountList = tmsUserAccountMapper.selectList(accountQuery);
        Map<String, TdsTenantAccount> accountMap = Maps.uniqueIndex(accountList, TdsTenantAccount::getId);

        LambdaQueryWrapper<OmsOrderDivide> divideQuery = new LambdaQueryWrapper<>();
        divideQuery.in(OmsOrderDivide::getId, orderDivideIds);
        List<OmsOrderDivide> omsOrderDivideList = omsOrderDivideMapper.selectList(divideQuery);
        Map<String, OmsOrderDivide> divideMap = Maps.newHashMap();
        Set<String> orderIds = Sets.newHashSet();
        for (OmsOrderDivide divide : omsOrderDivideList) {
            divideMap.put(divide.getId(), divide);
            orderIds.add(divide.getOrderId());
        }

        LambdaQueryWrapper<OmsOrder> orderQuery = new LambdaQueryWrapper<>();
        orderQuery.in(OmsOrder::getId, orderIds);
        List<OmsOrder> orderList = omsOrderMapper.selectList(orderQuery);
        Map<String, OmsOrder> orderMap = Maps.uniqueIndex(orderList, OmsOrder::getId);

        omsOrderDivideSettleIds.forEach(omsOrderDivideSettleId -> {
            OmsOrderDivideSettleHistory omsOrderDivideSettleHistory = new OmsOrderDivideSettleHistory();
            omsOrderDivideSettleHistory.setBatchNo(RandomUtil.randomNumbers(16));
            omsOrderDivideSettleHistory.setOrderDivideSettleId(omsOrderDivideSettleId);
            omsOrderDivideSettleHistory.setJobId(jobId);
            try {
                OmsOrderDivideSettle orderDivideSettle = settleMap.get(omsOrderDivideSettleId);
                if (orderDivideSettle == null) {
                    throw BizException.build(BizCode.SETTLE_RECORD_NOT_FOUND, omsOrderDivideSettleId);
                }
                omsOrderDivideSettleHistory.setSettleAmount(orderDivideSettle.getSettleAmount());
                if (orderDivideSettle.getWhetherSettle()) {
                    throw BizException.build(BizCode.SETTLE_RECORD_ALREADY_SETTLE, omsOrderDivideSettleId);
                }
                TdsTenantAccount account = accountMap.get(orderDivideSettle.getAccountId());
                omsOrderDivideSettleHistory.setSettleAccountId(orderDivideSettle.getAccountId());
                if (account == null) {
                    throw BizException.build(BizCode.SETTLE_ACCOUNT_NOT_FOUND, orderDivideSettle.getAccountId());
                }
                if (!account.getWhetherEnable()) {
                    throw BizException.build(BizCode.SETTLE_ACCOUNT_DISABLE, orderDivideSettle.getAccountId());
                }
                OmsOrderDivide orderDivide = divideMap.get(orderDivideSettle.getOrderDivideId());
                if (orderDivide == null) {
                    throw BizException.build(BizCode.SETTLE_DIVIDE_NOT_FOUND, orderDivideSettle.getOrderDivideId());
                }
                omsOrderDivideSettleHistory.setSettleMode(orderDivide.getSettleMode());
                if (orderDivide.getSettleSituation().equals(SdsSettleSituationEnum.ALL_SETTLED.getSettleSituation())) {
                    throw BizException.build(BizCode.SETTLE_DIVIDE_ALL_SETTLE, orderDivideSettle.getOrderDivideId());
                }
                OmsOrder order = orderMap.get(orderDivide.getOrderId());
                if (order == null) {
                    throw BizException.build(BizCode.SETTLE_ORDER_NOT_FOUND, orderDivide.getOrderId());
                }
                if (!order.getStatus().equals(SdsOrderStatusEnum.OVER.getOrderStatus())) {
                    throw BizException.build(BizCode.SETTLE_ORDER_STATUS_FORBID, orderDivide.getOrderId());
                }
                if (orderDivide.getSettleMode().equals(TdsSettleModeEnum.AUTO_LABEL.getSettleMode())
                        || orderDivide.getSettleMode().equals(TdsSettleModeEnum.MANUAL_LABEL.getSettleMode())) {
                    orderDivideSettle.setWhetherSettle(true);
                    int result = omsOrderDivideSettleMapper.updateById(orderDivideSettle);
                    if (result == 1) {
                        try {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("intransitBalanceChangeType", TdsMovementTypeEnum.OUTCOME.getMovementType());
                            jsonObject.put("intransitBalanceChangeValue", orderDivideSettle.getSettleAmount());
                            jsonObject.put("availableBalanceChangeType", TdsMovementTypeEnum.INCOME.getMovementType());
                            jsonObject.put("availableBalanceChangeValue", orderDivideSettle.getSettleAmount());
                            jsonObject.put("accountId", orderDivideSettle.getAccountId());
                            rabbitMqAccountMovementProducer.sendMessage(jsonObject);
                        } catch (Exception e) {
                            log.error("rabbitMqAccountMovementProducer send msg error", e);
                        }
                        omsOrderDivideSettleHistory.setWhetherSuccess(true);
                        LambdaQueryWrapper<OmsOrderDivideSettle> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.eq(OmsOrderDivideSettle::getOrderDivideId, orderDivideSettle.getOrderDivideId());
                        List<OmsOrderDivideSettle> settles = omsOrderDivideSettleMapper.selectList(queryWrapper);
                        if (CollectionUtil.isNotEmpty(settles)) {
                            long total = settles.size();
                            long settledTotal = settles.stream().filter(OmsOrderDivideSettle::getWhetherSettle).count();
                            if (total == settledTotal) {
                                orderDivide.setSettleSituation(SdsSettleSituationEnum.ALL_SETTLED.getSettleSituation());
                                omsOrderDivideMapper.updateById(orderDivide);
                            } else if (total > settledTotal) {
                                orderDivide.setSettleSituation(SdsSettleSituationEnum.PART_SETTLE.getSettleSituation());
                                omsOrderDivideMapper.updateById(orderDivide);
                            }
                        }
                    } else {
                        throw BizException.build(BizCode.SETTLE_UPDATE_RESULT_ERROR, orderDivideSettle.getId());
                    }
                } else if (orderDivide.getSettleMode().equals(TdsSettleModeEnum.AUTO_TRANSFER.getSettleMode())
                        || orderDivide.getSettleMode().equals(TdsSettleModeEnum.MANUAL_TRANSFER.getSettleMode())) {
                    throw BizException.build(BizCode.SETTLE_TRANSFER_COMING_SOON, orderDivideSettle.getId());
                }
            } catch (Exception e) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                omsOrderDivideSettleHistory.setWhetherSuccess(false);
                omsOrderDivideSettleHistory.setFailReason(e.getMessage());
            } finally {
                omsOrderDivideSettleHistoryMapper.insert(omsOrderDivideSettleHistory);
            }
        });
    }

    @Override
    public List<OmsOrderDivideSettleHistory> listSettleHistoryBySettleId(String settleId) {
        LambdaQueryWrapper<OmsOrderDivideSettleHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OmsOrderDivideSettleHistory::getOrderDivideSettleId, settleId);
        return omsOrderDivideSettleHistoryMapper.selectList(queryWrapper);
    }

}
