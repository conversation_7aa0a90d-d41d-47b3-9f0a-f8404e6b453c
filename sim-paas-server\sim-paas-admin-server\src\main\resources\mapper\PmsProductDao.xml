<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bosi.sim.paas.admin.server.dao.PmsProductDao">

    <select id="page" resultType="com.bosi.sim.paas.dao.model.sds.PmsProduct">
        select
            p.*,b.name as brand_name,pc.name as category_name,
            (select sum(sale) from sds_product_sku as ps where ps.product_id = p.id) as total_sale,
            (select sum(stock) from sds_product_sku as ps where ps.product_id = p.id) as total_stock
        from
            sds_product as p
        left join
            sds_brand as b on b.id = p.brand_id
        left join
            sds_category as pc on pc.id = p.category_id
        <where>
            <if test="true">
                and p.whether_delete = false
            </if>
            <if test="params.whetherPublish != null">
                and p.whether_publish = #{params.whetherPublish}
            </if>
            <if test="params.querySearchValue != null and params.querySearchValue != ''">
                and (p.product_name like concat('%', #{params.querySearchValue}, '%') or p.product_sn like concat('%', #{params.querySearchValue}, '%'))
            </if>
            <if test="params.whetherVirtually != null">
                and p.whether_virtually = #{params.whetherVirtually}
            </if>
            <if test="params.brandId != null">
                and p.brand_id = #{params.brandId}
            </if>
            <if test="params.categoryId != null">
                and p.category_id = #{params.categoryId}
            </if>
        </where>
    </select>


</mapper>
