package com.bosi.sim.paas.dao.model.sds;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.esim.mall.dao.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("oms_order_divide_settle")
public class OmsOrderDivideSettle extends BaseEntity {
    @ApiModelProperty(value = "订单id")
    private String orderDivideId;

    private String accountId;

    private Boolean whetherSettle;

    private String operateUser;

    private BigDecimal settleAmount;

    @TableField(exist = false)
    private String accountUserName;

}
