package com.bosi.sim.paas.dao.model.sds;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("sds_product_attribute_type")
public class SdsProductAttributeType extends BaseEntity {
    private String productId;

    private String attributeTypeId;

    @ApiModelProperty(value = "参数的值(多个时以逗号隔开)")
    private String value;

}
