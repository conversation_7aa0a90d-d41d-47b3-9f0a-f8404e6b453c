package com.bosi.sim.paas.tenant.server.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.dao.model.tds.TdsTenantAccount;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;


public interface TmsUserAccountDao {

    List<TdsTenantAccount> listByIds(@Param("ids") Set<String> ids);

    Page<TdsTenantAccount> page(Page<TdsTenantAccount> iPage, @Param("params") TdsTenantAccount tmsUserAccount);

}
