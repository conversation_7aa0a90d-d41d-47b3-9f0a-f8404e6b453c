package com.bosi.sim.paas.open.server.service;

import com.bosi.sim.paas.dao.model.sds.SdsMemberReceiveAddress;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 用户地址管理Service
 */
public interface MmsMemberReceiveAddressService {
    /**
     * 添加收货地址
     */
    int add(SdsMemberReceiveAddress address);

    /**
     * 删除收货地址
     * @param id 地址表的id
     */
    int delete(String id);

    /**
     * 修改收货地址
     * @param address 修改的收货地址信息
     */
    @Transactional
    int update(SdsMemberReceiveAddress address);

    /**
     * 返回当前用户的收货地址
     */
    List<SdsMemberReceiveAddress> list();

    /**
     * 获取地址详情
     * @param id 地址id
     */
    SdsMemberReceiveAddress getItem(String id);
}
