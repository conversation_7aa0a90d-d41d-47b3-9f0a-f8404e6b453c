# 重命名脚本 - 将 model 包中的类前缀按照包名前三个字母重命名

# 设置项目根目录
$MODEL_DIR = ".\sim-paas-dao\src\main\java\com\bosi\sim\paas\dao\model"

# 1. 重命名 sds 包中的类（从 Pms/Oms/Sms 改为 Sds）
Write-Host "重命名 sds 包中的类..."
Set-Location "$MODEL_DIR\sds"

# 重命名 Pms 前缀的文件
Get-ChildItem -Filter "Pms*.java" | ForEach-Object {
    $newname = "Sds" + $_.Name.Substring(3)
    Rename-Item -Path $_.Name -NewName $newname
    Write-Host "重命名: $($_.Name) -> $newname"
    
    # 修改文件内容
    (Get-Content $newname) -replace "class Pms", "class Sds" | Set-Content $newname
    (Get-Content $newname) -replace "extends Pms", "extends Sds" | Set-Content $newname
}

# 重命名 Oms 前缀的文件
Get-ChildItem -Filter "Oms*.java" | ForEach-Object {
    $newname = "Sds" + $_.Name.Substring(3)
    Rename-Item -Path $_.Name -NewName $newname
    Write-Host "重命名: $($_.Name) -> $newname"
    
    # 修改文件内容
    (Get-Content $newname) -replace "class Oms", "class Sds" | Set-Content $newname
    (Get-Content $newname) -replace "extends Oms", "extends Sds" | Set-Content $newname
}

# 重命名 Sms 前缀的文件
Get-ChildItem -Filter "Sms*.java" | ForEach-Object {
    $newname = "Sds" + $_.Name.Substring(3)
    Rename-Item -Path $_.Name -NewName $newname
    Write-Host "重命名: $($_.Name) -> $newname"
    
    # 修改文件内容
    (Get-Content $newname) -replace "class Sms", "class Sds" | Set-Content $newname
    (Get-Content $newname) -replace "extends Sms", "extends Sds" | Set-Content $newname
}

# 2. 重命名 tms 包中的类（从 Tds 改为 Tms）
Write-Host "重命名 tms 包中的类..."
Set-Location "$MODEL_DIR\tms"

# 重命名 Tds 前缀的文件
Get-ChildItem -Filter "Tds*.java" | ForEach-Object {
    $newname = "Tms" + $_.Name.Substring(3)
    Rename-Item -Path $_.Name -NewName $newname
    Write-Host "重命名: $($_.Name) -> $newname"
    
    # 修改文件内容
    (Get-Content $newname) -replace "class Tds", "class Tms" | Set-Content $newname
    (Get-Content $newname) -replace "extends Tds", "extends Tms" | Set-Content $newname
}

Write-Host "model 层类重命名完成！"