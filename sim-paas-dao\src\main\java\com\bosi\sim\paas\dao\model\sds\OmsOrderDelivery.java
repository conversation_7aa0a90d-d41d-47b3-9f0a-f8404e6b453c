package com.bosi.sim.paas.dao.model.sds;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.esim.mall.dao.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("oms_order_delivery")
public class OmsOrderDelivery extends BaseEntity {
    @ApiModelProperty(value = "订单id")
    private String orderId;

    @ApiModelProperty(value = "订单Item")
    private String orderItemId;

    @ApiModelProperty(value = "商品编号")
    private String productId;

    @ApiModelProperty(value = "商品SKU编号")
    private String productSkuId;

    private Integer deliveryNumber;

    private String deliverySn;

    private Integer deliveryStatus;

    @TableField(exist = false)
    private String orderSn;

    @TableField(exist = false)
    private String productSkuName;

    @TableField(exist = false)
    private String productSkuCode;
}
