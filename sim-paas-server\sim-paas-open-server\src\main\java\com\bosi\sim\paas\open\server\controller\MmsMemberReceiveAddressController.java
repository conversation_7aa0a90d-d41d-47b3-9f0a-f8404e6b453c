package com.bosi.sim.paas.open.server.controller;

import com.bosi.sim.paas.common.core.exception.CommonResult;
import com.bosi.sim.paas.dao.model.sds.SdsMemberReceiveAddress;
import com.bosi.sim.paas.open.server.service.MmsMemberReceiveAddressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 会员收货地址管理Controller
 */
@RestController
@Api(tags = "MmsMemberReceiveAddressController", value = "会员收货地址管理")
@RequestMapping("/mms/member/receive/address")
public class MmsMemberReceiveAddressController {
    @Autowired
    private MmsMemberReceiveAddressService memberReceiveAddressService;

    @ApiOperation("添加收货地址")
    @PostMapping("/add")
    public CommonResult add(@RequestBody SdsMemberReceiveAddress address) {
        memberReceiveAddressService.add(address);
        return CommonResult.success();
    }

    @ApiOperation("删除收货地址")
    @DeleteMapping("/delete")
    public CommonResult delete(@RequestParam String id) {
        memberReceiveAddressService.delete(id);
        return CommonResult.success();
    }

    @ApiOperation("修改收货地址")
    @PostMapping("/update")
    public CommonResult update(@RequestBody SdsMemberReceiveAddress address) {
        memberReceiveAddressService.update(address);
        return CommonResult.success();
    }

    @ApiOperation("获取所有收货地址")
    @GetMapping("/listAll")
    public CommonResult<List<SdsMemberReceiveAddress>> listAll() {
        List<SdsMemberReceiveAddress> addressList = memberReceiveAddressService.list();
        return CommonResult.success(addressList);
    }

    @ApiOperation("获取收货地址详情")
    @GetMapping("/get")
    public CommonResult<SdsMemberReceiveAddress> getItem(@RequestParam String id) {
        SdsMemberReceiveAddress address = memberReceiveAddressService.getItem(id);
        return CommonResult.success(address);
    }
}
