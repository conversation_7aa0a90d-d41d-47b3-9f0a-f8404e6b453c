package com.bosi.sim.paas.dao.pojo.dto;

import com.bosi.sim.paas.dao.model.sds.OmsOrderDivide;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 根据条件查询订单分润详情
 */
@Data
public class SdsOrderDivideInfoDto {

    private BigDecimal totalAmount;

    private Long totalNumber;

    private List<SdsOrderDivideInfoByDistributorDto> groupByDistributor;

    private List<SdsOrderDivideInfoByAccountDto> groupByAccount;

    private List<OmsOrderDivide> orderDivideList;

    private List<String> orderDivideSettleIds;
}
