package com.bosi.sim.paas.dao.enums.sds;


public enum SdsProductOperateTypeEnum {

    PUBLISH(1, "上架商品"),
    UNPUBLISH(2, "下架商品"),
    EDITBASE(3, "修改商品基础信息"),
    EDITMARKET(4, "修改商品营销信息"),
    EDITSTOCK(5, "修改商品库存信息"),
    DELETE(6, "删除商品");

    private Integer operateType;

    private String desc;

    SdsProductOperateTypeEnum(Integer operateType, String desc) {
        this.operateType = operateType;
        this.desc = desc;
    }

    public Integer getOperateType() {
        return operateType;
    }

    public String getDesc() {
        return desc;
    }
}
