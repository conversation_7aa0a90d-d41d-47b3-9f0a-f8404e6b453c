{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/components/layout/CachedViews.tsx"], "sourcesContent": ["'use client';\n\nimport { usePathname } from 'next/navigation';\nimport { ReactNode, useEffect, useRef, useState } from 'react';\n\ninterface CachedViewsProps {\n  children: ReactNode;\n  refreshFlag?: number; // 用于触发刷新\n}\n\ninterface CachedView {\n  key: string;\n  component: ReactNode;\n}\n\n/**\n * 页面缓存组件\n * 参考若依框架实现，缓存已访问的页面，避免重复渲染\n */\nexport default function CachedViews({ children, refreshFlag = 0 }: CachedViewsProps) {\n  const pathname = usePathname();\n  const [cachedViews, setCachedViews] = useState<CachedView[]>([]);\n  const [activeKey, setActiveKey] = useState(pathname);\n  const initialRenderRef = useRef(true);\n\n  // 初始化缓存\n  useEffect(() => {\n    if (initialRenderRef.current) {\n      setCachedViews([{ key: pathname, component: children }]);\n      initialRenderRef.current = false;\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [pathname]);\n\n  // 当路径变化时更新缓存\n  useEffect(() => {\n    if (initialRenderRef.current) return; // 跳过初始渲染\n\n    // 更新当前活动的视图\n    setActiveKey(pathname);\n\n    // 检查是否已缓存\n    setCachedViews(prev => {\n      const existingViewIndex = prev.findIndex(view => view.key === pathname);\n\n      if (existingViewIndex === -1) {\n        // 如果没有缓存，添加到缓存\n        return [...prev, { key: pathname, component: children }];\n      } else if (refreshFlag > 0) {\n        // 如果需要刷新，更新缓存\n        const newViews = [...prev];\n        newViews[existingViewIndex] = { key: pathname, component: children };\n        return newViews;\n      }\n\n      // 否则返回原来的缓存\n      return prev;\n    });\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [pathname, refreshFlag]);\n\n  // 如果没有缓存视图，直接显示当前内容\n  if (cachedViews.length === 0) {\n    return <div className=\"cached-views-container\">{children}</div>;\n  }\n\n  return (\n    <div className=\"cached-views-container\">\n      {cachedViews.map(view => (\n        <div\n          key={view.key}\n          className=\"cached-view\"\n          style={{\n            display: view.key === activeKey ? 'block' : 'none',\n            animation: view.key === activeKey ? 'fadeIn 0.1s ease-in-out' : 'none',\n          }}\n        >\n          {view.component}\n        </div>\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAmBe,SAAS,YAAY,EAAE,QAAQ,EAAE,cAAc,CAAC,EAAoB;;IACjF,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEhC,QAAQ;IACR,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,eAAe;oBAAC;wBAAE,KAAK;wBAAU,WAAW;oBAAS;iBAAE;gBACvD,iBAAiB,OAAO,GAAG;YAC7B;QACF,uDAAuD;QACvD;gCAAG;QAAC;KAAS;IAEb,aAAa;IACb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,iBAAiB,OAAO,EAAE,QAAQ,SAAS;YAE/C,YAAY;YACZ,aAAa;YAEb,UAAU;YACV;yCAAe,CAAA;oBACb,MAAM,oBAAoB,KAAK,SAAS;mEAAC,CAAA,OAAQ,KAAK,GAAG,KAAK;;oBAE9D,IAAI,sBAAsB,CAAC,GAAG;wBAC5B,eAAe;wBACf,OAAO;+BAAI;4BAAM;gCAAE,KAAK;gCAAU,WAAW;4BAAS;yBAAE;oBAC1D,OAAO,IAAI,cAAc,GAAG;wBAC1B,cAAc;wBACd,MAAM,WAAW;+BAAI;yBAAK;wBAC1B,QAAQ,CAAC,kBAAkB,GAAG;4BAAE,KAAK;4BAAU,WAAW;wBAAS;wBACnE,OAAO;oBACT;oBAEA,YAAY;oBACZ,OAAO;gBACT;;QACF,uDAAuD;QACvD;gCAAG;QAAC;QAAU;KAAY;IAE1B,oBAAoB;IACpB,IAAI,YAAY,MAAM,KAAK,GAAG;QAC5B,qBAAO,6LAAC;YAAI,WAAU;sBAA0B;;;;;;IAClD;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,YAAY,GAAG,CAAC,CAAA,qBACf,6LAAC;gBAEC,WAAU;gBACV,OAAO;oBACL,SAAS,KAAK,GAAG,KAAK,YAAY,UAAU;oBAC5C,WAAW,KAAK,GAAG,KAAK,YAAY,4BAA4B;gBAClE;0BAEC,KAAK,SAAS;eAPV,KAAK,GAAG;;;;;;;;;;AAYvB;GA/DwB;;QACL,qIAAA,CAAA,cAAW;;;KADN", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/components/layout/TabsNav.tsx"], "sourcesContent": ["'use client';\n\nimport { Menu } from '@/types';\nimport { CloseOutlined, ReloadOutlined } from '@ant-design/icons';\nimport { Dropdown, Tabs, message } from 'antd';\nimport { usePathname, useRouter } from 'next/navigation';\nimport { useEffect, useRef, useState } from 'react';\nimport { useTranslation } from 'react-i18next';\n\ninterface TabsNavProps {\n  menus: Menu[];\n  refreshCurrentPage: () => void;\n}\n\ninterface TabItem {\n  key: string;\n  label: string;\n  closable: boolean;\n}\n\n// 本地存储键\nconst TABS_STORAGE_KEY = 'iot-admin-tabs';\n\n// 查找菜单项\nconst findMenuByPath = (menus: Menu[], path: string): Menu | null => {\n  for (const menu of menus) {\n    if (menu.path === path) {\n      return menu;\n    }\n    if (menu.children) {\n      const found = findMenuByPath(menu.children, path);\n      if (found) return found;\n    }\n  }\n  return null;\n};\n\n// 从本地存储加载标签页\nconst loadTabsFromStorage = (): TabItem[] => {\n  if (typeof window === 'undefined') {\n    return [{ key: '/dashboard', label: '首页', closable: false }];\n  }\n\n  try {\n    const savedTabs = localStorage.getItem(TABS_STORAGE_KEY);\n    if (savedTabs) {\n      const parsedTabs = JSON.parse(savedTabs);\n      // 确保首页标签页始终存在\n      if (!parsedTabs.some((tab: TabItem) => tab.key === '/dashboard')) {\n        parsedTabs.unshift({ key: '/dashboard', label: '首页', closable: false });\n      }\n      return parsedTabs;\n    }\n  } catch (error) {\n    console.error('Failed to parse saved tabs:', error);\n  }\n\n  return [{ key: '/dashboard', label: '首页', closable: false }];\n};\n\n// 保存标签页到本地存储\nconst saveTabsToStorage = (tabs: TabItem[]): void => {\n  if (typeof window === 'undefined') return;\n\n  try {\n    localStorage.setItem(TABS_STORAGE_KEY, JSON.stringify(tabs));\n  } catch (error) {\n    console.error('Failed to save tabs:', error);\n  }\n};\n\n// 清除本地存储中的标签页数据\nconst clearTabsStorage = (): void => {\n  if (typeof window === 'undefined') return;\n\n  try {\n    localStorage.removeItem(TABS_STORAGE_KEY);\n  } catch (error) {\n    console.error('Failed to clear tabs storage:', error);\n  }\n};\n\nexport default function TabsNav({ menus, refreshCurrentPage }: TabsNavProps) {\n  const { t } = useTranslation();\n  const router = useRouter();\n  const pathname = usePathname();\n\n  // 清除本地存储中的标签页数据，确保从干净的状态开始\n  // 注意：这只在开发环境中使用，生产环境中应该注释掉\n  // 已禁用，以提高性能\n  /*\n  useEffect(() => {\n    if (process.env.NODE_ENV === 'development') {\n      clearTabsStorage();\n    }\n  }, []);\n  */\n\n  const [activeKey, setActiveKey] = useState(pathname);\n  const [tabs, setTabs] = useState<TabItem[]>(() => {\n    // 使用本地存储的标签页，提高性能\n    return loadTabsFromStorage();\n  });\n  const initializedRef = useRef(false);\n\n  // 添加标签页\n  const addTab = (path: string) => {\n    const menuItem = findMenuByPath(menus, path);\n    if (!menuItem) return;\n\n    setTabs(prev => {\n      // 检查标签页是否已存在\n      if (prev.some(tab => tab.key === path)) {\n        return prev;\n      }\n\n      // 添加新标签页\n      const newTabs = [...prev, { key: path, label: menuItem.name, closable: true }];\n\n      // 保存到本地存储\n      saveTabsToStorage(newTabs);\n\n      return newTabs;\n    });\n  };\n\n  // 初始化 - 只运行一次\n  useEffect(() => {\n    if (initializedRef.current) return;\n    initializedRef.current = true;\n\n    // 确保当前路径的标签页存在\n    if (pathname !== '/dashboard' && !tabs.some(tab => tab.key === pathname)) {\n      const menuItem = findMenuByPath(menus, pathname);\n      if (menuItem) {\n        setTabs(prev => {\n          const newTabs = [...prev, { key: pathname, label: menuItem.name, closable: true }];\n          saveTabsToStorage(newTabs);\n          return newTabs;\n        });\n      }\n    }\n\n    // 设置当前活动标签页\n    setActiveKey(pathname);\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  // 路由变化时更新活动标签页\n  useEffect(() => {\n    // 设置当前活动标签页\n    setActiveKey(pathname);\n\n    // 如果当前路径的标签页不存在，添加它\n    if (!tabs.some(tab => tab.key === pathname)) {\n      addTab(pathname);\n    }\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [pathname]);\n\n  // 关闭标签页\n  const removeTab = (targetKey: string) => {\n    // 不允许关闭首页\n    if (targetKey === '/dashboard') return;\n\n    // 找到要关闭的标签页的索引\n    const targetIndex = tabs.findIndex(tab => tab.key === targetKey);\n\n    // 更新标签页列表\n    const newTabs = tabs.filter(tab => tab.key !== targetKey);\n    setTabs(newTabs);\n\n    // 保存到本地存储\n    saveTabsToStorage(newTabs);\n\n    // 如果关闭的是当前活动的标签页，需要激活其他标签页\n    if (targetKey === activeKey) {\n      // 默认激活左侧标签页\n      const newActiveKey = newTabs[targetIndex - 1]?.key || '/dashboard';\n      setActiveKey(newActiveKey);\n      router.push(newActiveKey);\n    }\n  };\n\n  // 处理标签页切换\n  const handleTabChange = (key: string) => {\n    setActiveKey(key);\n    router.push(key);\n  };\n\n  // 处理标签页编辑（关闭）\n  const handleTabEdit = (targetKey: string, action: 'add' | 'remove') => {\n    if (action === 'remove') {\n      removeTab(targetKey);\n    }\n  };\n\n  // 刷新当前页面\n  const handleRefresh = () => {\n    refreshCurrentPage();\n    message.success(t('tabs.refreshSuccess'));\n  };\n\n  // 关闭所有标签页\n  const closeAllTabs = () => {\n    const defaultTabs = [{ key: '/dashboard', label: t('menu.home'), closable: false }];\n    setTabs(defaultTabs);\n    saveTabsToStorage(defaultTabs);\n    setActiveKey('/dashboard');\n    router.push('/dashboard');\n  };\n\n  // 关闭其他标签页\n  const closeOtherTabs = () => {\n    const currentTab = tabs.find(tab => tab.key === activeKey);\n    if (!currentTab) return;\n\n    const newTabs = [\n      { key: '/dashboard', label: t('menu.home'), closable: false },\n      ...(activeKey !== '/dashboard' ? [currentTab] : [])\n    ];\n\n    setTabs(newTabs);\n    saveTabsToStorage(newTabs);\n  };\n\n  // 右键菜单项\n  const getDropdownItems = (tabKey: string) => [\n    {\n      key: 'refresh',\n      label: t('tabs.refresh'),\n      icon: <ReloadOutlined />,\n      onClick: () => {\n        if (tabKey === activeKey) {\n          handleRefresh();\n        } else {\n          setActiveKey(tabKey);\n          router.push(tabKey);\n          setTimeout(handleRefresh, 300);\n        }\n      },\n    },\n    {\n      key: 'close',\n      label: t('tabs.close'),\n      icon: <CloseOutlined />,\n      disabled: tabKey === '/dashboard',\n      onClick: () => removeTab(tabKey),\n    },\n    {\n      key: 'closeOthers',\n      label: t('tabs.closeOthers'),\n      onClick: closeOtherTabs,\n    },\n    {\n      key: 'closeAll',\n      label: t('tabs.closeAll'),\n      onClick: closeAllTabs,\n    },\n  ];\n\n  // 自定义标签页\n  const renderTabLabel = (tab: TabItem) => {\n    return (\n      <Dropdown\n        menu={{ items: getDropdownItems(tab.key) }}\n        trigger={['contextMenu']}\n      >\n        <span>{tab.label}</span>\n      </Dropdown>\n    );\n  };\n\n  return (\n    <Tabs\n      type=\"editable-card\"\n      activeKey={activeKey}\n      onChange={handleTabChange}\n      onEdit={handleTabEdit}\n      items={tabs.map(tab => ({\n        key: tab.key,\n        label: renderTabLabel(tab),\n        closable: tab.closable,\n      }))}\n      hideAdd\n      tabBarStyle={{ margin: 0, backgroundColor: '#fff', padding: '0 16px' }}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAAA;;;AAPA;;;;;;AAoBA,QAAQ;AACR,MAAM,mBAAmB;AAEzB,QAAQ;AACR,MAAM,iBAAiB,CAAC,OAAe;IACrC,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,KAAK,IAAI,KAAK,MAAM;YACtB,OAAO;QACT;QACA,IAAI,KAAK,QAAQ,EAAE;YACjB,MAAM,QAAQ,eAAe,KAAK,QAAQ,EAAE;YAC5C,IAAI,OAAO,OAAO;QACpB;IACF;IACA,OAAO;AACT;AAEA,aAAa;AACb,MAAM,sBAAsB;IAC1B,uCAAmC;;IAEnC;IAEA,IAAI;QACF,MAAM,YAAY,aAAa,OAAO,CAAC;QACvC,IAAI,WAAW;YACb,MAAM,aAAa,KAAK,KAAK,CAAC;YAC9B,cAAc;YACd,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC,MAAiB,IAAI,GAAG,KAAK,eAAe;gBAChE,WAAW,OAAO,CAAC;oBAAE,KAAK;oBAAc,OAAO;oBAAM,UAAU;gBAAM;YACvE;YACA,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;IAC/C;IAEA,OAAO;QAAC;YAAE,KAAK;YAAc,OAAO;YAAM,UAAU;QAAM;KAAE;AAC9D;AAEA,aAAa;AACb,MAAM,oBAAoB,CAAC;IACzB,uCAAmC;;IAAM;IAEzC,IAAI;QACF,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;IACxD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;IACxC;AACF;AAEA,gBAAgB;AAChB,MAAM,mBAAmB;IACvB,uCAAmC;;IAAM;IAEzC,IAAI;QACF,aAAa,UAAU,CAAC;IAC1B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;IACjD;AACF;AAEe,SAAS,QAAQ,EAAE,KAAK,EAAE,kBAAkB,EAAgB;;IACzE,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,2BAA2B;IAC3B,2BAA2B;IAC3B,YAAY;IACZ;;;;;;EAMA,GAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;4BAAa;YAC1C,kBAAkB;YAClB,OAAO;QACT;;IACA,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE9B,QAAQ;IACR,MAAM,SAAS,CAAC;QACd,MAAM,WAAW,eAAe,OAAO;QACvC,IAAI,CAAC,UAAU;QAEf,QAAQ,CAAA;YACN,aAAa;YACb,IAAI,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK,OAAO;gBACtC,OAAO;YACT;YAEA,SAAS;YACT,MAAM,UAAU;mBAAI;gBAAM;oBAAE,KAAK;oBAAM,OAAO,SAAS,IAAI;oBAAE,UAAU;gBAAK;aAAE;YAE9E,UAAU;YACV,kBAAkB;YAElB,OAAO;QACT;IACF;IAEA,cAAc;IACd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,eAAe,OAAO,EAAE;YAC5B,eAAe,OAAO,GAAG;YAEzB,eAAe;YACf,IAAI,aAAa,gBAAgB,CAAC,KAAK,IAAI;qCAAC,CAAA,MAAO,IAAI,GAAG,KAAK;qCAAW;gBACxE,MAAM,WAAW,eAAe,OAAO;gBACvC,IAAI,UAAU;oBACZ;6CAAQ,CAAA;4BACN,MAAM,UAAU;mCAAI;gCAAM;oCAAE,KAAK;oCAAU,OAAO,SAAS,IAAI;oCAAE,UAAU;gCAAK;6BAAE;4BAClF,kBAAkB;4BAClB,OAAO;wBACT;;gBACF;YACF;YAEA,YAAY;YACZ,aAAa;QACf,uDAAuD;QACvD;4BAAG,EAAE;IAEL,eAAe;IACf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,YAAY;YACZ,aAAa;YAEb,oBAAoB;YACpB,IAAI,CAAC,KAAK,IAAI;qCAAC,CAAA,MAAO,IAAI,GAAG,KAAK;qCAAW;gBAC3C,OAAO;YACT;QACF,uDAAuD;QACvD;4BAAG;QAAC;KAAS;IAEb,QAAQ;IACR,MAAM,YAAY,CAAC;QACjB,UAAU;QACV,IAAI,cAAc,cAAc;QAEhC,eAAe;QACf,MAAM,cAAc,KAAK,SAAS,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK;QAEtD,UAAU;QACV,MAAM,UAAU,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK;QAC/C,QAAQ;QAER,UAAU;QACV,kBAAkB;QAElB,2BAA2B;QAC3B,IAAI,cAAc,WAAW;YAC3B,YAAY;YACZ,MAAM,eAAe,OAAO,CAAC,cAAc,EAAE,EAAE,OAAO;YACtD,aAAa;YACb,OAAO,IAAI,CAAC;QACd;IACF;IAEA,UAAU;IACV,MAAM,kBAAkB,CAAC;QACvB,aAAa;QACb,OAAO,IAAI,CAAC;IACd;IAEA,cAAc;IACd,MAAM,gBAAgB,CAAC,WAAmB;QACxC,IAAI,WAAW,UAAU;YACvB,UAAU;QACZ;IACF;IAEA,SAAS;IACT,MAAM,gBAAgB;QACpB;QACA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC,EAAE;IACpB;IAEA,UAAU;IACV,MAAM,eAAe;QACnB,MAAM,cAAc;YAAC;gBAAE,KAAK;gBAAc,OAAO,EAAE;gBAAc,UAAU;YAAM;SAAE;QACnF,QAAQ;QACR,kBAAkB;QAClB,aAAa;QACb,OAAO,IAAI,CAAC;IACd;IAEA,UAAU;IACV,MAAM,iBAAiB;QACrB,MAAM,aAAa,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK;QAChD,IAAI,CAAC,YAAY;QAEjB,MAAM,UAAU;YACd;gBAAE,KAAK;gBAAc,OAAO,EAAE;gBAAc,UAAU;YAAM;eACxD,cAAc,eAAe;gBAAC;aAAW,GAAG,EAAE;SACnD;QAED,QAAQ;QACR,kBAAkB;IACpB;IAEA,QAAQ;IACR,MAAM,mBAAmB,CAAC,SAAmB;YAC3C;gBACE,KAAK;gBACL,OAAO,EAAE;gBACT,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;gBACrB,SAAS;oBACP,IAAI,WAAW,WAAW;wBACxB;oBACF,OAAO;wBACL,aAAa;wBACb,OAAO,IAAI,CAAC;wBACZ,WAAW,eAAe;oBAC5B;gBACF;YACF;YACA;gBACE,KAAK;gBACL,OAAO,EAAE;gBACT,oBAAM,6LAAC,uNAAA,CAAA,gBAAa;;;;;gBACpB,UAAU,WAAW;gBACrB,SAAS,IAAM,UAAU;YAC3B;YACA;gBACE,KAAK;gBACL,OAAO,EAAE;gBACT,SAAS;YACX;YACA;gBACE,KAAK;gBACL,OAAO,EAAE;gBACT,SAAS;YACX;SACD;IAED,SAAS;IACT,MAAM,iBAAiB,CAAC;QACtB,qBACE,6LAAC,yLAAA,CAAA,WAAQ;YACP,MAAM;gBAAE,OAAO,iBAAiB,IAAI,GAAG;YAAE;YACzC,SAAS;gBAAC;aAAc;sBAExB,cAAA,6LAAC;0BAAM,IAAI,KAAK;;;;;;;;;;;IAGtB;IAEA,qBACE,6LAAC,iLAAA,CAAA,OAAI;QACH,MAAK;QACL,WAAW;QACX,UAAU;QACV,QAAQ;QACR,OAAO,KAAK,GAAG,CAAC,CAAA,MAAO,CAAC;gBACtB,KAAK,IAAI,GAAG;gBACZ,OAAO,eAAe;gBACtB,UAAU,IAAI,QAAQ;YACxB,CAAC;QACD,OAAO;QACP,aAAa;YAAE,QAAQ;YAAG,iBAAiB;YAAQ,SAAS;QAAS;;;;;;AAG3E;GA9MwB;;QACR,mKAAA,CAAA,iBAAc;QACb,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAHN", "debugId": null}}, {"offset": {"line": 483, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/components/layout/AdminLayout.tsx"], "sourcesContent": ["'use client';\n\nimport CachedViews from '@/components/layout/CachedViews';\nimport TabsNav from '@/components/layout/TabsNav';\nimport LanguageSwitcher from '@/components/common/LanguageSwitcher';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { menuService } from '@/services';\nimport { Menu } from '@/types';\nimport { useTranslation } from 'react-i18next';\nimport {\n  DashboardOutlined,\n  LogoutOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  SettingOutlined,\n  TeamOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport { Avatar, Breadcrumb, Dropdown, Layout, Menu as AntMenu, Spin, theme } from 'antd';\nimport Link from 'next/link';\nimport { usePathname, useRouter } from 'next/navigation';\nimport { useEffect, useState } from 'react';\n\nconst { Header, Sider, Content } = Layout;\n\n// 图标映射\nconst iconMap: Record<string, React.ReactNode> = {\n  dashboard: <DashboardOutlined />,\n  setting: <SettingOutlined />,\n  user: <UserOutlined />,\n  team: <TeamOutlined />,\n  menu: <MenuOutlined />,\n};\n\n// 自定义 MenuOutlined 组件\nfunction MenuOutlined() {\n  return <span className=\"anticon\">\n    <svg viewBox=\"64 64 896 896\" focusable=\"false\" data-icon=\"menu\" width=\"1em\" height=\"1em\" fill=\"currentColor\" aria-hidden=\"true\">\n      <path d=\"M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z\"></path>\n    </svg>\n  </span>;\n}\n\n// 将菜单数据转换为 Ant Design Menu 组件所需的格式\nconst convertMenuItems = (menus: Menu[]) => {\n  return menus.map(menu => {\n    const item = {\n      key: menu.path,\n      icon: menu.icon ? iconMap[menu.icon] || <MenuOutlined /> : null,\n      label: menu.children && menu.children.length > 0\n        ? menu.name\n        : <Link href={menu.path} style={{ color: 'inherit', textDecoration: 'none' }}>{menu.name}</Link>,\n      children: menu.children && menu.children.length > 0 ? convertMenuItems(menu.children) : undefined,\n    };\n    return item;\n  });\n};\n\n// 获取当前路径的面包屑\nconst getBreadcrumbItems = (pathname: string, menus: Menu[]) => {\n  const paths = pathname.split('/').filter(Boolean);\n  const { t } = useTranslation();\n  const breadcrumbItems = [{\n    title: <Link href=\"/dashboard\" style={{ color: 'inherit', textDecoration: 'none' }}>{t('menu.home')}</Link>,\n  }];\n\n  let currentPath = '';\n  let currentMenus = menus;\n\n  for (const path of paths) {\n    currentPath += `/${path}`;\n\n    // 在当前菜单层级中查找匹配的菜单\n    const findMenu = (menus: Menu[], path: string): Menu | undefined => {\n      for (const menu of menus) {\n        if (menu.path === path) {\n          return menu;\n        }\n        if (menu.children) {\n          const found = findMenu(menu.children, path);\n          if (found) {\n            return found;\n          }\n        }\n      }\n      return undefined;\n    };\n\n    const menu = findMenu(currentMenus, currentPath);\n    if (menu) {\n      breadcrumbItems.push({\n        title: <Link href={menu.path} style={{ color: 'inherit', textDecoration: 'none' }}>{menu.name}</Link>,\n      });\n      if (menu.children) {\n        currentMenus = menu.children;\n      }\n    }\n  }\n\n  return breadcrumbItems;\n};\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n}\n\nconst AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {\n  const { t } = useTranslation();\n  const { user, logout } = useAuth();\n  const router = useRouter();\n  const pathname = usePathname();\n  const [collapsed, setCollapsed] = useState(false);\n  const [menuItems, setMenuItems] = useState<any[]>([]);\n  const [menuTree, setMenuTree] = useState<Menu[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [refreshFlag, setRefreshFlag] = useState(0); // 用于触发页面刷新\n\n  const {\n    token: { colorBgContainer, borderRadiusLG },\n  } = theme.useToken();\n\n  // 获取菜单数据\n  useEffect(() => {\n    // 使用缓存的菜单数据，避免重复请求\n    const cachedMenus = localStorage.getItem('iot-admin-menus');\n\n    const fetchMenus = async () => {\n      try {\n        setLoading(true);\n\n        // 如果有缓存，先使用缓存数据快速渲染\n        if (cachedMenus) {\n          try {\n            const parsedMenus = JSON.parse(cachedMenus);\n            setMenuTree(parsedMenus);\n            setMenuItems(convertMenuItems(parsedMenus));\n            setLoading(false);\n          } catch (e) {\n            console.error('解析缓存菜单失败:', e);\n          }\n        }\n\n        // 然后再从服务器获取最新数据\n        const menuTree = await menuService.getMenuTree();\n        setMenuTree(menuTree);\n        setMenuItems(convertMenuItems(menuTree));\n\n        // 缓存菜单数据\n        localStorage.setItem('iot-admin-menus', JSON.stringify(menuTree));\n      } catch (error) {\n        console.error('获取菜单失败:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchMenus();\n  }, []);\n\n  // 处理菜单点击 - 参考若依框架实现\n  const handleMenuClick = ({ key }: { key: string }) => {\n    // 如果点击的是当前路径，刷新当前页面\n    if (pathname === key) {\n      refreshCurrentPage();\n      return;\n    }\n\n    // 使用 router.push 进行客户端导航\n    // 注意：Next.js 的 router.push 已经是客户端导航，不会刷新整个页面\n    router.push(key);\n  };\n\n  // 处理登出\n  const handleLogout = async () => {\n    await logout();\n    router.push('/login');\n  };\n\n  // 刷新当前页面\n  const refreshCurrentPage = () => {\n    setRefreshFlag(prev => prev + 1);\n  };\n\n  // 用户下拉菜单\n  const userMenuItems = [\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: t('common.logout'),\n      onClick: handleLogout,\n    },\n  ];\n\n  // 如果正在加载，显示加载中\n  if (loading) {\n    return (\n      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>\n        <Spin size=\"large\" tip={t('common.loading')} />\n      </div>\n    );\n  }\n\n  return (\n    <Layout style={{ minHeight: '100vh' }}>\n      <Sider trigger={null} collapsible collapsed={collapsed} theme=\"light\">\n        <div style={{ height: 64, display: 'flex', alignItems: 'center', justifyContent: 'center', borderBottom: '1px solid #f0f0f0' }}>\n          <h1 style={{ margin: 0, fontSize: collapsed ? 16 : 20, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>\n            {collapsed ? 'IOT' : 'IOT Admin'}\n          </h1>\n        </div>\n        <AntMenu\n          mode=\"inline\"\n          selectedKeys={[pathname]}\n          defaultOpenKeys={['/system']}\n          style={{ height: '100%', borderRight: 0 }}\n          items={menuItems}\n          onClick={handleMenuClick}\n        />\n      </Sider>\n      <Layout>\n        <Header style={{ padding: 0, background: colorBgContainer, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n          <div style={{ paddingLeft: 16 }}>\n            {collapsed ? (\n              <MenuUnfoldOutlined onClick={() => setCollapsed(false)} style={{ fontSize: 18 }} />\n            ) : (\n              <MenuFoldOutlined onClick={() => setCollapsed(true)} style={{ fontSize: 18 }} />\n            )}\n          </div>\n          <div style={{ paddingRight: 24 }}>\n            <Dropdown menu={{ items: userMenuItems }} placement=\"bottomRight\">\n              <div style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}>\n                <Avatar src={user?.avatar} style={{ marginRight: 8 }} />\n                <span>{user?.name}</span>\n              </div>\n            </Dropdown>\n          </div>\n        </Header>\n        <Content style={{ margin: '0', display: 'flex', flexDirection: 'column', height: 'calc(100vh - 64px)' }}>\n          {/* 标签页导航 */}\n          <div className=\"tabs-nav-container\" style={{ background: '#fff', borderBottom: '1px solid #f0f0f0' }}>\n            <TabsNav menus={menuTree} refreshCurrentPage={refreshCurrentPage} />\n          </div>\n\n          {/* 内容区域 */}\n          <div\n            style={{\n              padding: 16,\n              flex: 1,\n              overflow: 'auto',\n              background: '#f0f2f5',\n            }}\n          >\n            <div\n              style={{\n                padding: 24,\n                minHeight: 360,\n                background: colorBgContainer,\n                borderRadius: borderRadiusLG,\n              }}\n            >\n              <CachedViews refreshFlag={refreshFlag}>\n                {children}\n              </CachedViews>\n            </div>\n          </div>\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\nexport default AdminLayout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AArBA;;;;;;;;;;;AAuBA,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,qLAAA,CAAA,SAAM;AAEzC,OAAO;AACP,MAAM,UAA2C;IAC/C,yBAAW,6LAAC,+NAAA,CAAA,oBAAiB;;;;;IAC7B,uBAAS,6LAAC,2NAAA,CAAA,kBAAe;;;;;IACzB,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;IACnB,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;IACnB,oBAAM,6LAAC;;;;;AACT;AAEA,sBAAsB;AACtB,SAAS;IACP,qBAAO,6LAAC;QAAK,WAAU;kBACrB,cAAA,6LAAC;YAAI,SAAQ;YAAgB,WAAU;YAAQ,aAAU;YAAO,OAAM;YAAM,QAAO;YAAM,MAAK;YAAe,eAAY;sBACvH,cAAA,6LAAC;gBAAK,GAAE;;;;;;;;;;;;;;;;AAGd;KANS;AAQT,mCAAmC;AACnC,MAAM,mBAAmB,CAAC;IACxB,OAAO,MAAM,GAAG,CAAC,CAAA;QACf,MAAM,OAAO;YACX,KAAK,KAAK,IAAI;YACd,MAAM,KAAK,IAAI,GAAG,OAAO,CAAC,KAAK,IAAI,CAAC,kBAAI,6LAAC;;;;uBAAkB;YAC3D,OAAO,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,IAC3C,KAAK,IAAI,iBACT,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAM,KAAK,IAAI;gBAAE,OAAO;oBAAE,OAAO;oBAAW,gBAAgB;gBAAO;0BAAI,KAAK,IAAI;;;;;;YAC1F,UAAU,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,IAAI,iBAAiB,KAAK,QAAQ,IAAI;QAC1F;QACA,OAAO;IACT;AACF;AAEA,aAAa;AACb,MAAM,qBAAqB,CAAC,UAAkB;;IAC5C,MAAM,QAAQ,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;IACzC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,kBAAkB;QAAC;YACvB,qBAAO,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAK;gBAAa,OAAO;oBAAE,OAAO;oBAAW,gBAAgB;gBAAO;0BAAI,EAAE;;;;;;QACzF;KAAE;IAEF,IAAI,cAAc;IAClB,IAAI,eAAe;IAEnB,KAAK,MAAM,QAAQ,MAAO;QACxB,eAAe,CAAC,CAAC,EAAE,MAAM;QAEzB,kBAAkB;QAClB,MAAM,WAAW,CAAC,OAAe;YAC/B,KAAK,MAAM,QAAQ,MAAO;gBACxB,IAAI,KAAK,IAAI,KAAK,MAAM;oBACtB,OAAO;gBACT;gBACA,IAAI,KAAK,QAAQ,EAAE;oBACjB,MAAM,QAAQ,SAAS,KAAK,QAAQ,EAAE;oBACtC,IAAI,OAAO;wBACT,OAAO;oBACT;gBACF;YACF;YACA,OAAO;QACT;QAEA,MAAM,OAAO,SAAS,cAAc;QACpC,IAAI,MAAM;YACR,gBAAgB,IAAI,CAAC;gBACnB,qBAAO,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAM,KAAK,IAAI;oBAAE,OAAO;wBAAE,OAAO;wBAAW,gBAAgB;oBAAO;8BAAI,KAAK,IAAI;;;;;;YAC/F;YACA,IAAI,KAAK,QAAQ,EAAE;gBACjB,eAAe,KAAK,QAAQ;YAC9B;QACF;IACF;IAEA,OAAO;AACT;GAzCM;;QAEU,mKAAA,CAAA,iBAAc;;;AA6C9B,MAAM,cAA0C,CAAC,EAAE,QAAQ,EAAE;;IAC3D,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,iBAAc,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACpD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,WAAW;IAE9D,MAAM,EACJ,OAAO,EAAE,gBAAgB,EAAE,cAAc,EAAE,EAC5C,GAAG,mLAAA,CAAA,QAAK,CAAC,QAAQ;IAElB,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,mBAAmB;YACnB,MAAM,cAAc,aAAa,OAAO,CAAC;YAEzC,MAAM;oDAAa;oBACjB,IAAI;wBACF,WAAW;wBAEX,oBAAoB;wBACpB,IAAI,aAAa;4BACf,IAAI;gCACF,MAAM,cAAc,KAAK,KAAK,CAAC;gCAC/B,YAAY;gCACZ,aAAa,iBAAiB;gCAC9B,WAAW;4BACb,EAAE,OAAO,GAAG;gCACV,QAAQ,KAAK,CAAC,aAAa;4BAC7B;wBACF;wBAEA,gBAAgB;wBAChB,MAAM,WAAW,MAAM,qIAAA,CAAA,cAAW,CAAC,WAAW;wBAC9C,YAAY;wBACZ,aAAa,iBAAiB;wBAE9B,SAAS;wBACT,aAAa,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC;oBACzD,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;oBAC3B,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;gCAAG,EAAE;IAEL,oBAAoB;IACpB,MAAM,kBAAkB,CAAC,EAAE,GAAG,EAAmB;QAC/C,oBAAoB;QACpB,IAAI,aAAa,KAAK;YACpB;YACA;QACF;QAEA,yBAAyB;QACzB,6CAA6C;QAC7C,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;IACP,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,SAAS;IACT,MAAM,qBAAqB;QACzB,eAAe,CAAA,OAAQ,OAAO;IAChC;IAEA,SAAS;IACT,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,OAAO,EAAE;YACT,SAAS;QACX;KACD;IAED,eAAe;IACf,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,OAAO;gBAAE,SAAS;gBAAQ,gBAAgB;gBAAU,YAAY;gBAAU,QAAQ;YAAQ;sBAC7F,cAAA,6LAAC,iLAAA,CAAA,OAAI;gBAAC,MAAK;gBAAQ,KAAK,EAAE;;;;;;;;;;;IAGhC;IAEA,qBACE,6LAAC,qLAAA,CAAA,SAAM;QAAC,OAAO;YAAE,WAAW;QAAQ;;0BAClC,6LAAC;gBAAM,SAAS;gBAAM,WAAW;gBAAC,WAAW;gBAAW,OAAM;;kCAC5D,6LAAC;wBAAI,OAAO;4BAAE,QAAQ;4BAAI,SAAS;4BAAQ,YAAY;4BAAU,gBAAgB;4BAAU,cAAc;wBAAoB;kCAC3H,cAAA,6LAAC;4BAAG,OAAO;gCAAE,QAAQ;gCAAG,UAAU,YAAY,KAAK;gCAAI,YAAY;gCAAU,UAAU;gCAAU,cAAc;4BAAW;sCACvH,YAAY,QAAQ;;;;;;;;;;;kCAGzB,6LAAC,iLAAA,CAAA,OAAO;wBACN,MAAK;wBACL,cAAc;4BAAC;yBAAS;wBACxB,iBAAiB;4BAAC;yBAAU;wBAC5B,OAAO;4BAAE,QAAQ;4BAAQ,aAAa;wBAAE;wBACxC,OAAO;wBACP,SAAS;;;;;;;;;;;;0BAGb,6LAAC,qLAAA,CAAA,SAAM;;kCACL,6LAAC;wBAAO,OAAO;4BAAE,SAAS;4BAAG,YAAY;4BAAkB,SAAS;4BAAQ,YAAY;4BAAU,gBAAgB;wBAAgB;;0CAChI,6LAAC;gCAAI,OAAO;oCAAE,aAAa;gCAAG;0CAC3B,0BACC,6LAAC,iOAAA,CAAA,qBAAkB;oCAAC,SAAS,IAAM,aAAa;oCAAQ,OAAO;wCAAE,UAAU;oCAAG;;;;;yDAE9E,6LAAC,6NAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,aAAa;oCAAO,OAAO;wCAAE,UAAU;oCAAG;;;;;;;;;;;0CAG/E,6LAAC;gCAAI,OAAO;oCAAE,cAAc;gCAAG;0CAC7B,cAAA,6LAAC,yLAAA,CAAA,WAAQ;oCAAC,MAAM;wCAAE,OAAO;oCAAc;oCAAG,WAAU;8CAClD,cAAA,6LAAC;wCAAI,OAAO;4CAAE,QAAQ;4CAAW,SAAS;4CAAQ,YAAY;wCAAS;;0DACrE,6LAAC,qLAAA,CAAA,SAAM;gDAAC,KAAK,MAAM;gDAAQ,OAAO;oDAAE,aAAa;gDAAE;;;;;;0DACnD,6LAAC;0DAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAKrB,6LAAC;wBAAQ,OAAO;4BAAE,QAAQ;4BAAK,SAAS;4BAAQ,eAAe;4BAAU,QAAQ;wBAAqB;;0CAEpG,6LAAC;gCAAI,WAAU;gCAAqB,OAAO;oCAAE,YAAY;oCAAQ,cAAc;gCAAoB;0CACjG,cAAA,6LAAC,0IAAA,CAAA,UAAO;oCAAC,OAAO;oCAAU,oBAAoB;;;;;;;;;;;0CAIhD,6LAAC;gCACC,OAAO;oCACL,SAAS;oCACT,MAAM;oCACN,UAAU;oCACV,YAAY;gCACd;0CAEA,cAAA,6LAAC;oCACC,OAAO;wCACL,SAAS;wCACT,WAAW;wCACX,YAAY;wCACZ,cAAc;oCAChB;8CAEA,cAAA,6LAAC,8IAAA,CAAA,UAAW;wCAAC,aAAa;kDACvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB;IAnKM;;QACU,mKAAA,CAAA,iBAAc;QACH,kIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QASxB,mLAAA,CAAA,QAAK,CAAC;;;MAbN;uCAqKS", "debugId": null}}, {"offset": {"line": 1040, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/app/%28admin%29/layout.tsx"], "sourcesContent": ["'use client';\n\nimport AdminLayout from '@/components/layout/AdminLayout';\n\nexport default function AdminPageLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return <AdminLayout>{children}</AdminLayout>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS,gBAAgB,EACtC,QAAQ,EAGT;IACC,qBAAO,6LAAC,8IAAA,CAAA,UAAW;kBAAE;;;;;;AACvB;KANwB", "debugId": null}}]}