package com.bosi.sim.paas.tenant.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.common.optlog.dto.OperateLogDTO;
import com.bosi.sim.paas.common.security.auth.CurrentAuthorization;
import com.bosi.sim.paas.dao.mapper.tms.TmsOperateLogMapper;
import com.bosi.sim.paas.dao.model.tds.TdsOperateLog;
import com.bosi.sim.paas.tenant.server.enums.DistributorTokenExtendKeyEnum;
import com.bosi.sim.paas.tenant.server.service.TmsOperateLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;

/**
 * 操作日志 服务层处理
 */
@Service
public class TmsOperateLogServiceImpl implements TmsOperateLogService {
    @Autowired
    private TmsOperateLogMapper operLogMapper;

    /**
     * 新增操作日志
     *
     * @param operLog 操作日志对象
     * @return 结果
     */
    @Override
    public void saveLog(OperateLogDTO operLog) {
        TdsOperateLog operateLog = BeanUtil.copyProperties(operLog, TdsOperateLog.class);
        operateLog.setDistributorId(CurrentAuthorization.getStringFromExtendJson(DistributorTokenExtendKeyEnum.DISTRIBUTORID_KEY.getKey()));
        operLogMapper.insert(operateLog);
    }

    /**
     * 查询系统操作日志集合
     *
     * @param operLog 操作日志对象
     * @return 操作日志集合
     */
    @Override
    public CommonPage<TdsOperateLog> page(Page<TdsOperateLog> ipage, TdsOperateLog operLog) {
        LambdaQueryWrapper<TdsOperateLog> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(operLog.getOperIp())) {
            queryWrapper.like(TdsOperateLog::getOperIp, operLog.getOperIp());
        }
        if (StringUtils.isNotEmpty(operLog.getTitle())) {
            queryWrapper.like(TdsOperateLog::getTitle, operLog.getTitle());
        }
        if (StringUtils.isNotNull(operLog.getWhetherSuccess())) {
            queryWrapper.eq(TdsOperateLog::getWhetherSuccess, operLog.getWhetherSuccess());
        }
        if (StringUtils.isNotEmpty(operLog.getOperName())) {
            queryWrapper.like(TdsOperateLog::getOperName, operLog.getOperName());
        }
        if (StringUtils.isNotEmpty(operLog.getQueryBeginTime())) {
            queryWrapper.ge(TdsOperateLog::getOperTime, operLog.getQueryBeginTime());
        }
        if (StringUtils.isNotEmpty(operLog.getQueryEndTime())) {
            queryWrapper.le(TdsOperateLog::getOperTime, operLog.getQueryEndTime());
        }
        if (StringUtils.isNotNull(operLog.getDistributorId())) {
            queryWrapper.eq(TdsOperateLog::getDistributorId, operLog.getDistributorId());
        }
        IPage<TdsOperateLog> page = operLogMapper.selectPage(ipage, queryWrapper);
        return CommonPage.restPage(page);
    }

    /**
     * 批量删除系统操作日志
     *
     * @param operIds 需要删除的操作日志ID
     * @return 结果
     */
    @Override
    public int deleteOperLogByIds(String[] operIds) {
        return operLogMapper.deleteBatchIds(Arrays.asList(operIds));
    }

    /**
     * 查询操作日志详细
     *
     * @param operId 操作ID
     * @return 操作日志对象
     */
    @Override
    public TdsOperateLog selectOperLogById(String operId) {
        return operLogMapper.selectById(operId);
    }

}
