/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-ease: initial;
    }
  }
}

@layer theme {
  :root, :host {
    --ease-in-out: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-geist-sans);
    --default-mono-font-family: var(--font-geist-mono);
  }
}

@layer base {
  *, :after, :before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}

@layer components;

@layer utilities {
  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .block {
    display: block;
  }

  .flex {
    display: flex;
  }

  .hidden {
    display: none;
  }

  .inline {
    display: inline;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .items-center {
    align-items: center;
  }

  .justify-center {
    justify-content: center;
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .ease-in-out {
    --tw-ease: var(--ease-in-out);
    transition-timing-function: var(--ease-in-out);
  }
}

:root {
  --background: #fff;
  --foreground: #171717;
  --font-size-base: 14px;
  --font-size-sm: 12px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-xxl: 24px;
  --line-height-base: 1.5;
  --border-radius-base: 4px;
  --border-radius-sm: 2px;
  --border-radius-lg: 8px;
  --transition-duration: .2s;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
}

.page-enter {
  opacity: 0;
}

.page-enter-active {
  opacity: 1;
  transition: opacity .3s;
}

.page-exit {
  opacity: 1;
}

.page-exit-active {
  opacity: 0;
  transition: opacity .3s;
}

html {
  scroll-behavior: smooth;
}

.ant-table, .ant-form, .ant-card, .ant-modal {
  transition: opacity .2s ease-in-out;
}

.tabs-nav-container .ant-tabs-nav {
  margin-bottom: 0;
}

.tabs-nav-container .ant-tabs-tab {
  padding: 8px 16px;
  transition: all .3s;
}

.tabs-nav-container .ant-tabs-tab-active {
  background-color: #f0f2f5;
  border-bottom-color: #0000 !important;
}

.tabs-nav-container .ant-tabs-tab-btn {
  transition: color .3s;
}

.cached-views-container {
  width: 100%;
  min-height: 300px;
  position: relative;
}

.cached-view {
  width: 100%;
  height: 100%;
}

@keyframes fadeIn {
  from {
    opacity: .9;
  }

  to {
    opacity: 1;
  }
}

.ant-layout-content {
  transition: none !important;
}

.ant-layout-content .ant-tabs-content {
  height: 100%;
  margin: 0;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.ant-table {
  font-size: var(--font-size-base);
}

.ant-table-small {
  font-size: var(--font-size-sm);
}

.ant-form-item-label > label {
  font-size: var(--font-size-base);
}

.ant-form-item-explain, .ant-form-item-extra {
  font-size: var(--font-size-sm);
}

.ant-btn {
  font-size: var(--font-size-base);
}

.ant-btn-sm {
  font-size: var(--font-size-sm);
}

.ant-btn-lg {
  font-size: var(--font-size-lg);
}

.ant-menu-item {
  font-size: var(--font-size-base);
}

h1 {
  font-size: var(--font-size-xxl);
  margin-bottom: 16px;
  font-weight: 500;
}

h2 {
  font-size: var(--font-size-xl);
  margin-bottom: 14px;
  font-weight: 500;
}

h3 {
  font-size: var(--font-size-lg);
  margin-bottom: 12px;
  font-weight: 500;
}

.ant-card-head-title {
  font-size: var(--font-size-lg);
}

.ant-tabs-tab, .ant-dropdown-menu-item {
  font-size: var(--font-size-base);
}

.ant-modal-title {
  font-size: var(--font-size-lg);
}

.ant-statistic-title {
  font-size: var(--font-size-base);
}

.ant-statistic-content {
  font-size: var(--font-size-xxl);
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

/*# sourceMappingURL=src_app_globals_css_f9ee138c._.single.css.map*/