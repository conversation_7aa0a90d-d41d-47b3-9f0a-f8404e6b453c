package com.bosi.sim.paas.admin.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.api.SystemCode;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.dao.mapper.sds.SdsMemberGrowthHistoryMapper;
import com.bosi.sim.paas.dao.mapper.sds.SdsMemberMapper;
import com.bosi.sim.paas.dao.mapper.sds.MmsMemberPointHistoryMapper;
import com.bosi.sim.paas.dao.mapper.sds.MmsMemberReceiveAddressMapper;
import com.bosi.sim.paas.dao.mapper.sms.SmsCouponHistoryMapper;
import com.bosi.sim.paas.dao.model.sds.SdsMember;
import com.bosi.sim.paas.dao.model.sds.SdsMemberGrowthHistory;
import com.bosi.sim.paas.dao.model.sds.SdsMemberPointHistory;
import com.bosi.sim.paas.dao.model.sds.SdsMemberReceiveAddress;
import com.bosi.sim.paas.dao.model.sds.SmsCouponHistory;
import com.bosi.sim.paas.admin.server.service.MmsMemberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 会员管理Service实现类
 */
@Service
public class MmsMemberServiceImpl implements MmsMemberService {

    @Autowired
    private SdsMemberMapper mmsMemberMapper;

    @Autowired
    private SdsMemberGrowthHistoryMapper mmsMemberGrowthHistoryMapper;

    @Autowired
    private MmsMemberPointHistoryMapper memberPointHistoryMapper;

    @Autowired
    private SmsCouponHistoryMapper couponHistoryMapper;

    @Autowired
    private MmsMemberReceiveAddressMapper memberReceiveAddressMapper;

    @Override
    public CommonPage<SdsMember> page(Page<SdsMember> ipage, SdsMember member) {
        LambdaQueryWrapper<SdsMember> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(member.getUsername())) {
            queryWrapper.like(SdsMember::getUsername, member.getUsername());
        }
        if (StringUtils.isNotNull(member.getWhetherEnable())) {
            queryWrapper.eq(SdsMember::getWhetherEnable, member.getWhetherEnable());
        }
        IPage<SdsMember> page = mmsMemberMapper.selectPage(ipage, queryWrapper);
        return CommonPage.restPage(page);
    }

    @Override
    public CommonPage<SdsMemberGrowthHistory> pageGrowth(Page<SdsMemberGrowthHistory> ipage, SdsMemberGrowthHistory memberGrowthHistory) {
        LambdaQueryWrapper<SdsMemberGrowthHistory> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(memberGrowthHistory.getMemberId())) {
            queryWrapper.like(SdsMemberGrowthHistory::getMemberId, memberGrowthHistory.getMemberId());
        }
        IPage<SdsMemberGrowthHistory> page = mmsMemberGrowthHistoryMapper.selectPage(ipage, queryWrapper);
        return CommonPage.restPage(page);
    }

    @Override
    public CommonPage<SdsMemberPointHistory> pagePoint(Page<SdsMemberPointHistory> ipage, SdsMemberPointHistory memberPointHistory) {
        LambdaQueryWrapper<SdsMemberPointHistory> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(memberPointHistory.getMemberId())) {
            queryWrapper.like(SdsMemberPointHistory::getMemberId, memberPointHistory.getMemberId());
        }
        IPage<SdsMemberPointHistory> page = memberPointHistoryMapper.selectPage(ipage, queryWrapper);
        return CommonPage.restPage(page);
    }

    @Override
    public CommonPage<SmsCouponHistory> pageCoupon(Page<SmsCouponHistory> ipage, SmsCouponHistory couponHistory) {
        LambdaQueryWrapper<SmsCouponHistory> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(couponHistory.getMemberId())) {
            queryWrapper.like(SmsCouponHistory::getMemberId, couponHistory.getMemberId());
        }
        IPage<SmsCouponHistory> page = couponHistoryMapper.selectPage(ipage, queryWrapper);
        return CommonPage.restPage(page);
    }

    @Override
    public List<SdsMemberReceiveAddress> listAddressByMemberId(String memberId) {
        LambdaQueryWrapper<SdsMemberReceiveAddress> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SdsMemberReceiveAddress::getMemberId, memberId);
        return memberReceiveAddressMapper.selectList(queryWrapper);
    }

    @Override
    public SdsMember detail(String id) {
        SdsMember member = mmsMemberMapper.selectById(id);
        if (member == null) {
            throw BizException.build(SystemCode.RESOURCE_NOTFOUND);
        }
        return member;
    }

    @Override
    public int updateStatus(SdsMember member) {
        SdsMember update = new SdsMember();
        update.setId(member.getId());
        update.setWhetherEnable(member.getWhetherEnable());
        return mmsMemberMapper.updateById(update);
    }
}
