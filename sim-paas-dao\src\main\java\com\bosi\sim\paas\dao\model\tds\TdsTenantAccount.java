package com.bosi.sim.paas.dao.model.tms;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("tms_tenant_account")
public class TdsTenantAccount extends BaseEntity {

    private Boolean whetherEnable;

    private String tenantId;

    private BigDecimal availableBalance;

    private BigDecimal intransitBalance;

    @TableField(exist = false)
    private String tenantName;

}
