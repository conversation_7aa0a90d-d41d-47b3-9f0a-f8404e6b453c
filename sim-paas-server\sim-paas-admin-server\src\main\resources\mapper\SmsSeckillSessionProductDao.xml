<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bosi.sim.paas.admin.server.dao.SmsSeckillSessionProductDao">

    <select id="getList" resultType="com.bosi.sim.paas.dao.model.sds.SmsSeckillSessionProduct">
        SELECT
            r.*,
            p.sku_name,p.sku_code,p.price
        FROM
            sms_seckill_session_product r
            LEFT JOIN sds_product_sku p ON r.product_sku_id = p.id
        WHERE
            r.whether_delete = false
            AND r.seckill_id = #{seckillId}
            AND r.seckill_session_id = #{seckillSessionId}
        ORDER BY r.product_sort DESC
    </select>

    <select id="getById" resultType="com.bosi.sim.paas.dao.model.sds.SmsSeckillSessionProduct">
        SELECT
            r.*,
            p.sku_name,p.sku_code,p.price
        FROM
            sms_seckill_session_product r
        LEFT JOIN sds_product_sku p ON r.product_sku_id = p.id
        WHERE
            r.whether_delete = false
        AND
            r.id = #{id}
        ORDER BY r.product_sort DESC
    </select>

    <insert id="insertList" useGeneratedKeys="true" keyProperty="id">
        <foreach collection="list" item="item" separator=";">
            insert into sms_seckill_session_product
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">id,</if>
                <if test="item.seckillId != null">seckill_id,</if>
                <if test="item.seckillSessionId != null">seckill_session_id,</if>
                <if test="item.seckillPrice != null">seckill_price,</if>
                <if test="item.seckillCount != null">seckill_count,</if>
                <if test="item.seckillLimit != null">seckill_limit,</if>
                <if test="item.productSkuId != null">product_sku_id,</if>
                <if test="item.productSort != null">product_sort,</if>
            </trim>
            values
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">#{item.id},</if>
                <if test="item.seckillSessionId != null">#{item.seckillSessionId},</if>
                <if test="item.seckillPrice != null">#{item.seckillPrice},</if>
                <if test="item.seckillCount != null">#{item.seckillCount},</if>
                <if test="item.seckillLimit != null">#{item.seckillLimit},</if>
                <if test="item.productSkuId != null">#{item.productSkuId},</if>
                <if test="item.productSort != null">#{item.productSort},</if>
            </trim>
        </foreach>
    </insert>
</mapper>
