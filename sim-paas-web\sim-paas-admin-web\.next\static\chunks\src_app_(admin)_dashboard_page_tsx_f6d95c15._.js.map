{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/app/%28admin%29/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { Card, Col, Row, Statistic } from 'antd';\nimport { DashboardOutlined, TeamOutlined, UserOutlined } from '@ant-design/icons';\n\nexport default function DashboardPage() {\n  return (\n    <>\n      <h1>仪表盘</h1>\n      \n      <Row gutter={16}>\n        <Col span={8}>\n          <Card>\n            <Statistic\n              title=\"用户总数\"\n              value={3}\n              prefix={<UserOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={8}>\n          <Card>\n            <Statistic\n              title=\"角色总数\"\n              value={3}\n              prefix={<TeamOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={8}>\n          <Card>\n            <Statistic\n              title=\"菜单总数\"\n              value={5}\n              prefix={<DashboardOutlined />}\n            />\n          </Card>\n        </Col>\n      </Row>\n      \n      <div style={{ marginTop: 24 }}>\n        <Card title=\"系统信息\">\n          <p><strong>系统名称：</strong> IOT Admin 后台管理系统</p>\n          <p><strong>当前版本：</strong> v1.0.0</p>\n          <p><strong>前端框架：</strong> Next.js + Ant Design</p>\n          <p><strong>后端技术：</strong> Java</p>\n        </Card>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,qBACE;;0BACE,6LAAC;0BAAG;;;;;;0BAEJ,6LAAC,+KAA<PERSON>,CAAA,MAAG;gBAAC,QAAQ;;kCACX,6LAAC,+KAA<PERSON>,CAAA,MAAG;wBAAC,MAAM;kCACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,2LAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO;gCACP,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;kCAI3B,6LAAC,+KAAA,CAAA,MAAG;wBAAC,MAAM;kCACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,2LAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO;gCACP,sBAAQ,6LAAC,qNAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;kCAI3B,6LAAC,+KAA<PERSON>,CAAA,MAAG;wBAAC,MAAM;kCACT,cAAA,6LAAC,iLAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,2LAAA,CAAA,YAAS;gCACR,OAAM;gCACN,OAAO;gCACP,sBAAQ,6LAAC,+NAAA,CAAA,oBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMlC,6LAAC;gBAAI,OAAO;oBAAE,WAAW;gBAAG;0BAC1B,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBAAC,OAAM;;sCACV,6LAAC;;8CAAE,6LAAC;8CAAO;;;;;;gCAAc;;;;;;;sCACzB,6LAAC;;8CAAE,6LAAC;8CAAO;;;;;;gCAAc;;;;;;;sCACzB,6LAAC;;8CAAE,6LAAC;8CAAO;;;;;;gCAAc;;;;;;;sCACzB,6LAAC;;8CAAE,6LAAC;8CAAO;;;;;;gCAAc;;;;;;;;;;;;;;;;;;;;AAKnC;KA7CwB", "debugId": null}}]}