package com.bosi.sim.paas.dao.model.tms;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import lombok.Data;

/**
 * 分销商表 ads_dept
 */
@Data
@TableName("tms_tenant")
public class TdsTenant extends BaseEntity {
    /**
     * 租户名称
     */
    private String tenantName;

    private String tenantIdsChain;

    private String parentTenantId;

    /**
     * 负责人
     */
    private String leader;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 分销商状态:0正常,1停用
     */
    private Boolean whetherEnable;

    private Boolean whetherMain;

    @TableField(exist = false)
    private Long notifyCount;

    @TableField(exist = false)
    private String notifyId;

}
