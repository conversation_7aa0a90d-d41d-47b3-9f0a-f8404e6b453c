import java.io.*;
import java.util.*;

public class ExcelSplitter {
    
    public static void main(String[] args) {
        try {
            // 首先尝试读取Excel文件，如果失败则提示转换为CSV
            String inputFile = "doc/test.xlsx";
            String csvFile = "doc/test.csv";
            String outputFile = "doc/test1.csv";
            
            System.out.println("Excel数据分裂工具");
            System.out.println("==================");
            
            // 检查输入文件是否存在
            File excelFile = new File(inputFile);
            if (!excelFile.exists()) {
                System.err.println("错误: 找不到文件 " + inputFile);
                return;
            }
            
            System.out.println("找到Excel文件: " + inputFile);
            System.out.println("文件大小: " + excelFile.length() + " 字节");
            
            // 提示用户手动转换为CSV
            System.out.println("\n由于Java处理Excel需要外部库，请按以下步骤操作：");
            System.out.println("1. 打开 " + inputFile);
            System.out.println("2. 另存为 CSV 格式，保存为 " + csvFile);
            System.out.println("3. 重新运行此程序");
            
            // 检查CSV文件是否存在
            File csvInputFile = new File(csvFile);
            if (!csvInputFile.exists()) {
                System.out.println("\n等待CSV文件创建...");
                return;
            }
            
            // 处理CSV文件
            processCsvFile(csvFile, outputFile);
            
        } catch (Exception e) {
            System.err.println("处理过程中出现错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public static void processCsvFile(String inputFile, String outputFile) throws IOException {
        System.out.println("\n开始处理CSV文件: " + inputFile);
        
        List<String[]> allRows = new ArrayList<>();
        String[] headers = null;
        int mccmncColumnIndex = -1;
        int totalRows = 0;
        int splitRows = 0;
        
        // 读取CSV文件
        try (BufferedReader br = new BufferedReader(new FileReader(inputFile, java.nio.charset.StandardCharsets.UTF_8))) {
            String line;
            boolean isFirstLine = true;
            
            while ((line = br.readLine()) != null) {
                totalRows++;
                String[] columns = parseCsvLine(line);
                
                if (isFirstLine) {
                    headers = columns;
                    // 查找MCCMNC列
                    for (int i = 0; i < headers.length; i++) {
                        if ("MCCMNC".equalsIgnoreCase(headers[i].trim())) {
                            mccmncColumnIndex = i;
                            break;
                        }
                    }
                    
                    if (mccmncColumnIndex == -1) {
                        System.err.println("错误: 未找到MCCMNC列");
                        System.out.println("可用的列: " + Arrays.toString(headers));
                        return;
                    }
                    
                    System.out.println("找到MCCMNC列，索引: " + mccmncColumnIndex);
                    allRows.add(headers);
                    isFirstLine = false;
                    continue;
                }
                
                // 处理数据行
                if (mccmncColumnIndex < columns.length) {
                    String mccmncValue = columns[mccmncColumnIndex];
                    
                    if (mccmncValue != null && mccmncValue.contains("/")) {
                        // 分割包含'/'的数据
                        String[] parts = mccmncValue.split("/");
                        splitRows++;
                        
                        System.out.println("行 " + totalRows + ": '" + mccmncValue + "' 分裂为 " + parts.length + " 行");
                        
                        // 为每个分割后的值创建新行
                        for (String part : parts) {
                            String[] newRow = columns.clone();
                            newRow[mccmncColumnIndex] = part.trim();
                            allRows.add(newRow);
                        }
                    } else {
                        // 不包含'/'的行直接添加
                        allRows.add(columns);
                    }
                } else {
                    // 如果该行的列数不够，直接添加
                    allRows.add(columns);
                }
            }
        }
        
        // 写入结果文件
        try (PrintWriter pw = new PrintWriter(new FileWriter(outputFile, java.nio.charset.StandardCharsets.UTF_8))) {
            for (String[] row : allRows) {
                pw.println(String.join(",", escapeCSVFields(row)));
            }
        }
        
        System.out.println("\n处理完成!");
        System.out.println("原始数据行数: " + totalRows);
        System.out.println("分裂了 " + splitRows + " 行包含'/'的数据");
        System.out.println("处理后数据行数: " + allRows.size());
        System.out.println("结果已保存到: " + outputFile);
        System.out.println("\n请将 " + outputFile + " 重新导入Excel并另存为 doc/test1.xlsx");
    }
    
    // 简单的CSV解析（处理逗号分隔）
    private static String[] parseCsvLine(String line) {
        List<String> fields = new ArrayList<>();
        StringBuilder currentField = new StringBuilder();
        boolean inQuotes = false;
        
        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);
            
            if (c == '"') {
                inQuotes = !inQuotes;
            } else if (c == ',' && !inQuotes) {
                fields.add(currentField.toString());
                currentField = new StringBuilder();
            } else {
                currentField.append(c);
            }
        }
        
        fields.add(currentField.toString());
        return fields.toArray(new String[0]);
    }
    
    // 转义CSV字段
    private static String[] escapeCSVFields(String[] fields) {
        String[] escaped = new String[fields.length];
        for (int i = 0; i < fields.length; i++) {
            String field = fields[i];
            if (field.contains(",") || field.contains("\"") || field.contains("\n")) {
                field = "\"" + field.replace("\"", "\"\"") + "\"";
            }
            escaped[i] = field;
        }
        return escaped;
    }
}
