import java.io.*;
import java.util.*;

public class ExcelSplitter {

    public static void main(String[] args) {
        System.out.println("=== Excel MCCMNC Data Splitter ===");
        System.out.println("Author: Augment Agent");
        System.out.println("Function: Split MCCMNC column data containing '/' into multiple rows");
        System.out.println("=========================================");

        try {
            // Check input file
            String inputFile = "doc/test.xlsx";
            File excelFile = new File(inputFile);

            if (!excelFile.exists()) {
                System.err.println("Error: Cannot find file " + inputFile);
                System.out.println("Please ensure the file exists in the doc directory");
                return;
            }

            System.out.println("Found Excel file: " + inputFile);
            System.out.println("File size: " + excelFile.length() + " bytes");

            // Since we don't have external libraries, we need to convert Excel to CSV first
            String csvFile = "doc/test.csv";
            String outputCsvFile = "doc/test1.csv";

            // Check if CSV file exists
            File csvInputFile = new File(csvFile);
            if (!csvInputFile.exists()) {
                System.out.println("\nNeed to convert Excel to CSV format first");
                System.out.println("Please follow these steps:");
                System.out.println("1. Open Excel file: " + inputFile);
                System.out.println("2. Click File -> Save As");
                System.out.println("3. Select file type: 'CSV UTF-8 (Comma delimited)(*.csv)'");
                System.out.println("4. Save as: " + csvFile);
                System.out.println("5. Run this program again");
                System.out.println("\nWaiting for CSV file creation...");
                return;
            }

            // Process CSV file
            System.out.println("\nFound CSV file, starting processing...");
            boolean success = processCsvFile(csvFile, outputCsvFile);

            if (success) {
                System.out.println("\nProcessing completed successfully!");
                System.out.println("Result file: " + outputCsvFile);
                System.out.println("\nNext steps:");
                System.out.println("1. Open Excel");
                System.out.println("2. Import " + outputCsvFile + " file");
                System.out.println("3. Save as doc/test1.xlsx");

                // Create a simple batch file to help users
                createBatchFile();
            }

        } catch (Exception e) {
            System.err.println("Error occurred during processing: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public static void processCsvFile(String inputFile, String outputFile) throws IOException {
        System.out.println("\n开始处理CSV文件: " + inputFile);
        
        List<String[]> allRows = new ArrayList<>();
        String[] headers = null;
        int mccmncColumnIndex = -1;
        int totalRows = 0;
        int splitRows = 0;
        
        // 读取CSV文件
        try (BufferedReader br = new BufferedReader(new FileReader(inputFile, java.nio.charset.StandardCharsets.UTF_8))) {
            String line;
            boolean isFirstLine = true;
            
            while ((line = br.readLine()) != null) {
                totalRows++;
                String[] columns = parseCsvLine(line);
                
                if (isFirstLine) {
                    headers = columns;
                    // 查找MCCMNC列
                    for (int i = 0; i < headers.length; i++) {
                        if ("MCCMNC".equalsIgnoreCase(headers[i].trim())) {
                            mccmncColumnIndex = i;
                            break;
                        }
                    }
                    
                    if (mccmncColumnIndex == -1) {
                        System.err.println("错误: 未找到MCCMNC列");
                        System.out.println("可用的列: " + Arrays.toString(headers));
                        return;
                    }
                    
                    System.out.println("找到MCCMNC列，索引: " + mccmncColumnIndex);
                    allRows.add(headers);
                    isFirstLine = false;
                    continue;
                }
                
                // 处理数据行
                if (mccmncColumnIndex < columns.length) {
                    String mccmncValue = columns[mccmncColumnIndex];
                    
                    if (mccmncValue != null && mccmncValue.contains("/")) {
                        // 分割包含'/'的数据
                        String[] parts = mccmncValue.split("/");
                        splitRows++;
                        
                        System.out.println("行 " + totalRows + ": '" + mccmncValue + "' 分裂为 " + parts.length + " 行");
                        
                        // 为每个分割后的值创建新行
                        for (String part : parts) {
                            String[] newRow = columns.clone();
                            newRow[mccmncColumnIndex] = part.trim();
                            allRows.add(newRow);
                        }
                    } else {
                        // 不包含'/'的行直接添加
                        allRows.add(columns);
                    }
                } else {
                    // 如果该行的列数不够，直接添加
                    allRows.add(columns);
                }
            }
        }
        
        // 写入结果文件
        try (PrintWriter pw = new PrintWriter(new FileWriter(outputFile, java.nio.charset.StandardCharsets.UTF_8))) {
            for (String[] row : allRows) {
                pw.println(String.join(",", escapeCSVFields(row)));
            }
        }
        
        System.out.println("\n处理完成!");
        System.out.println("原始数据行数: " + totalRows);
        System.out.println("分裂了 " + splitRows + " 行包含'/'的数据");
        System.out.println("处理后数据行数: " + allRows.size());
        System.out.println("结果已保存到: " + outputFile);
        System.out.println("\n请将 " + outputFile + " 重新导入Excel并另存为 doc/test1.xlsx");
    }
    
    // 简单的CSV解析（处理逗号分隔）
    private static String[] parseCsvLine(String line) {
        List<String> fields = new ArrayList<>();
        StringBuilder currentField = new StringBuilder();
        boolean inQuotes = false;
        
        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);
            
            if (c == '"') {
                inQuotes = !inQuotes;
            } else if (c == ',' && !inQuotes) {
                fields.add(currentField.toString());
                currentField = new StringBuilder();
            } else {
                currentField.append(c);
            }
        }
        
        fields.add(currentField.toString());
        return fields.toArray(new String[0]);
    }
    
    // 转义CSV字段
    private static String[] escapeCSVFields(String[] fields) {
        String[] escaped = new String[fields.length];
        for (int i = 0; i < fields.length; i++) {
            String field = fields[i];
            if (field.contains(",") || field.contains("\"") || field.contains("\n")) {
                field = "\"" + field.replace("\"", "\"\"") + "\"";
            }
            escaped[i] = field;
        }
        return escaped;
    }
}
