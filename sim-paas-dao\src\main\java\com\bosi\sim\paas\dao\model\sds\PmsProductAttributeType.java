package com.bosi.esim.mall.dao.model.pms;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.esim.mall.dao.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("pms_product_attribute_type")
public class PmsProductAttributeType extends BaseEntity {
    private String productId;

    private String attributeTypeId;

    @ApiModelProperty(value = "参数的值(多个时以逗号隔开)")
    private String value;

}