package com.bosi.sim.paas.open.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.BizCode;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.api.SystemCode;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.IdUtils;
import com.bosi.sim.paas.common.core.utils.PageUtil;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.common.security.auth.CurrentAuthorization;
import com.bosi.sim.paas.dao.enums.ConfigKeyEnum;
import com.bosi.sim.paas.dao.enums.oms.OmsOrderStatusEnum;
import com.bosi.sim.paas.dao.enums.sms.SmsCouponUseStatusEnum;
import com.bosi.sim.paas.dao.enums.sms.SmsDivideModeEnum;
import com.bosi.sim.paas.dao.mapper.cms.CmsConfigMapper;
import com.bosi.sim.paas.dao.mapper.mms.MmsMemberMapper;
import com.bosi.sim.paas.dao.mapper.mms.MmsMemberReceiveAddressMapper;
import com.bosi.sim.paas.dao.mapper.oms.OmsCartItemMapper;
import com.bosi.sim.paas.dao.mapper.oms.OmsOrderItemMapper;
import com.bosi.sim.paas.dao.mapper.oms.OmsOrderMapper;
import com.bosi.sim.paas.dao.mapper.pms.PmsProductSkuMapper;
import com.bosi.sim.paas.dao.mapper.sms.SmsCouponHistoryMapper;
import com.bosi.sim.paas.dao.mapper.tms.TmsDistributorMapper;
import com.bosi.sim.paas.dao.model.ads.AdsConfig;
import com.bosi.sim.paas.dao.model.sds.SdsMember;
import com.bosi.sim.paas.dao.model.sds.SdsMemberReceiveAddress;
import com.bosi.sim.paas.dao.model.sds.OmsCartItem;
import com.bosi.sim.paas.dao.model.sds.OmsOrder;
import com.bosi.sim.paas.dao.model.sds.OmsOrderItem;
import com.bosi.sim.paas.dao.model.sds.jsonclz.DivideDetailJsonClass;
import com.bosi.sim.paas.dao.model.sds.jsonclz.ReceiverDetailJsonClass;
import com.bosi.sim.paas.dao.model.sds.PmsProductSku;
import com.bosi.sim.paas.dao.model.sds.SmsCoupon;
import com.bosi.sim.paas.dao.model.sds.SmsCouponHistory;
import com.bosi.sim.paas.dao.model.sds.SmsReferralCodeProduct;
import com.bosi.sim.paas.dao.model.sds.jsonclz.DivideAccountJsonarrayClass;
import com.bosi.sim.paas.dao.model.tds.TdsTenant;
import com.bosi.sim.paas.open.server.config.device.AppProductParam;
import com.bosi.sim.paas.open.server.config.device.DeviceMgtClient;
import com.bosi.sim.paas.open.server.config.rabbitmq.RabbitMqOrderCancelProducer;
import com.bosi.sim.paas.open.server.config.rabbitmq.RabbitMqOrderConfirmProducer;
import com.bosi.sim.paas.open.server.config.stripe.StripeClient;
import com.bosi.sim.paas.open.server.dao.OmsOrderDao;
import com.bosi.sim.paas.open.server.dao.OmsOrderItemDao;
import com.bosi.sim.paas.open.server.dao.SmsReferralCodeProductDao;
import com.bosi.sim.paas.open.server.domain.dto.CartPromotionItemDto;
import com.bosi.sim.paas.open.server.domain.param.CartItemCreateParam;
import com.bosi.sim.paas.open.server.domain.param.OrderCartParam;
import com.bosi.sim.paas.open.server.domain.param.OrderDirectParam;
import com.bosi.sim.paas.open.server.domain.vo.ConfirmOrderVo;
import com.bosi.sim.paas.open.server.handler.OrderHandler;
import com.bosi.sim.paas.open.server.service.OmsOrderService;
import com.google.common.collect.Lists;
import com.stripe.model.PaymentIntent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 前台订单管理Service
 */
@Slf4j
@Service
public class OmsOrderServiceImpl implements OmsOrderService {

    @Autowired
    private DeviceMgtClient deviceMgtClient;

    @Autowired
    private OrderHandler orderHandler;

    @Autowired
    private OmsOrderDao omsOrderDao;

    @Autowired
    private OmsOrderMapper orderMapper;

    @Autowired
    private OmsOrderItemMapper omsOrderItemMapper;

    @Autowired
    private OmsOrderItemDao omsOrderItemDao;

    @Autowired
    private OmsCartItemMapper cartItemMapper;

    @Autowired
    private MmsMemberMapper memberMapper;

    @Autowired
    private MmsMemberReceiveAddressMapper memberReceiveAddressMapper;

    @Autowired
    private PmsProductSkuMapper productSkuMapper;

    @Autowired
    private SmsCouponHistoryMapper couponHistoryMapper;

    @Autowired
    private TmsDistributorMapper distributorMapper;

    @Autowired
    private SmsReferralCodeProductDao referralCodeProductDao;

    @Autowired
    private CmsConfigMapper cmsConfigMapper;

    @Autowired
    private RabbitMqOrderCancelProducer rabbitMqOrderCancelProducer;

    @Autowired
    private RabbitMqOrderConfirmProducer rabbitMqOrderConfirmProducer;

    @Autowired
    private StripeClient stripeClient;

    @Override
    public ConfirmOrderVo generateConfirmOrder(List<String> cartIds) {
        List<CartItemCreateParam> createParamList = orderHandler.getCartItemCreateParamListByCartIds(CurrentAuthorization.getUserId(), cartIds);
        return confirmOrder(createParamList);
    }

    @Override
    public ConfirmOrderVo directGenerateConfirmOrder(CartItemCreateParam cartItemCreateParam) {
        List<CartItemCreateParam> createParamList = Lists.newArrayList(cartItemCreateParam);
        return confirmOrder(createParamList);
    }

    private ConfirmOrderVo confirmOrder(List<CartItemCreateParam> createParamList) {
        ConfirmOrderVo result = new ConfirmOrderVo();
        List<CartPromotionItemDto> cartPromotionItemList = orderHandler.calcCartPromotion(CurrentAuthorization.getUserId(), createParamList);
        result.setCartPromotionItemList(cartPromotionItemList);
        //获取用户收货地址列表
        List<SdsMemberReceiveAddress> memberReceiveAddressList = orderHandler.listMemberReceiveAddressByMemberId(CurrentAuthorization.getUserId());
        result.setMemberReceiveAddressList(memberReceiveAddressList);
        //获取用户可用优惠券列表
        List<SmsCouponHistory> couponHistoryDetailList = orderHandler.listCartCoupon(cartPromotionItemList, 1, CurrentAuthorization.getUserId());
        result.setCouponHistoryList(couponHistoryDetailList);
        //计算总金额、活动优惠、应付金额
        ConfirmOrderVo.CalcAmount calcAmount = calcCartAmount(cartPromotionItemList);
        result.setCalcAmount(calcAmount);
        return result;
    }

    @Transactional
    @Override
    public Map<String, Object> generateOrder(OrderCartParam orderParam) {
        LambdaQueryWrapper<SdsMemberReceiveAddress> example = new LambdaQueryWrapper<>();
        example.eq(SdsMemberReceiveAddress::getMemberId, CurrentAuthorization.getUserId());
        example.eq(SdsMemberReceiveAddress::getId, orderParam.getMemberReceiveAddressId());
        SdsMemberReceiveAddress address = memberReceiveAddressMapper.selectOne(example);
        if (address == null) {
            throw BizException.build(BizCode.ADDRESS_NOT_FOUND);
        }
        ReceiverDetailJsonClass receiverDetailJsonClass = new ReceiverDetailJsonClass();
        receiverDetailJsonClass.setId(address.getId());
        receiverDetailJsonClass.setReceiverName(address.getName());
        receiverDetailJsonClass.setReceiverPhone(address.getPhoneNumber());
        receiverDetailJsonClass.setReceiverRegion(address.getRegion());
        receiverDetailJsonClass.setReceiverCountry(address.getCountry());
        receiverDetailJsonClass.setLevelAddresses(address.getLevelAddresses());
        receiverDetailJsonClass.setDetailAddress(address.getDetailAddress());

        List<CartItemCreateParam> createParamList = orderHandler.getCartItemCreateParamListByCartIds(CurrentAuthorization.getUserId(),
                orderParam.getCartIds());
        //获取购物车及优惠信息
        List<CartPromotionItemDto> cartPromotionItemList = orderHandler.calcCartPromotion(CurrentAuthorization.getUserId(), createParamList);
        return createOrder(cartPromotionItemList, receiverDetailJsonClass, orderParam.getCouponId(),
                orderParam.getPayType(), orderParam.getSourceType());
    }

    @Transactional
    @Override
    public Map<String, Object> directGenerateOrder(OrderDirectParam orderParam) {
        LambdaQueryWrapper<SdsMemberReceiveAddress> example = new LambdaQueryWrapper<>();
        example.eq(SdsMemberReceiveAddress::getMemberId, CurrentAuthorization.getUserId());
        example.eq(SdsMemberReceiveAddress::getId, orderParam.getMemberReceiveAddressId());
        SdsMemberReceiveAddress address = memberReceiveAddressMapper.selectOne(example);
        if (address == null) {
            throw BizException.build(BizCode.ADDRESS_NOT_FOUND);
        }
        ReceiverDetailJsonClass receiverDetailJsonClass = new ReceiverDetailJsonClass();
        receiverDetailJsonClass.setId(address.getId());
        receiverDetailJsonClass.setReceiverName(address.getName());
        receiverDetailJsonClass.setReceiverPhone(address.getPhoneNumber());
        receiverDetailJsonClass.setReceiverRegion(address.getRegion());
        receiverDetailJsonClass.setReceiverCountry(address.getCountry());
        receiverDetailJsonClass.setLevelAddresses(address.getLevelAddresses());
        receiverDetailJsonClass.setDetailAddress(address.getDetailAddress());
        //获取购物车及优惠信息
        List<CartPromotionItemDto> cartPromotionItemList = orderHandler.calcCartPromotion(CurrentAuthorization.getUserId(),
                Lists.newArrayList(orderParam.getCartItemCreateParam()));
        return createOrder(cartPromotionItemList, receiverDetailJsonClass, orderParam.getCouponId(),
                orderParam.getPayType(), orderParam.getSourceType());
    }

    private Map<String, Object> createOrder(List<CartPromotionItemDto> cartPromotionItemList, ReceiverDetailJsonClass receiverDetailJsonClass,
                                            String couponId, Integer payType, Integer sourceType) {
        List<OmsOrderItem> orderItemList = new ArrayList<>();
        SdsMember currentMember = memberMapper.selectById(CurrentAuthorization.getUserId());
        for (CartPromotionItemDto cartPromotionItem : cartPromotionItemList) {
            //生成下单商品信息
            OmsOrderItem orderItem = new OmsOrderItem();
            orderItem.setId(IdUtils.fastSimpleUUID());
            orderItem.setProductId(cartPromotionItem.getProductId());
            orderItem.setProductName(cartPromotionItem.getProductName());
            orderItem.setProductSkuName(cartPromotionItem.getProductSkuName());
            orderItem.setProductPic(cartPromotionItem.getPic());
            orderItem.setProductAttr(cartPromotionItem.getProductAttr());
            orderItem.setProductBrandName(cartPromotionItem.getProductBrandName());
            orderItem.setProductSn(cartPromotionItem.getProductSn());
            orderItem.setProductQuantity(cartPromotionItem.getQuantity());
            orderItem.setProductSkuId(cartPromotionItem.getProductSkuId());
            orderItem.setProductSkuCode(cartPromotionItem.getProductSkuCode());
            orderItem.setProductCategoryName(cartPromotionItem.getProductCategoryName());
            orderItem.setPromotionName(cartPromotionItem.getPromotionMessage());
            orderItem.setPerProductPrice(cartPromotionItem.getPerPrice());
            orderItem.setPerPromotionAmount(cartPromotionItem.getPerReduceAmount());
            orderItem.setPerGiftPoint(cartPromotionItem.getPerGiftPoint());
            orderItem.setPerGiftGrowth(cartPromotionItem.getPerGiftGrowth());
            orderItem.setWhetherVirtually(cartPromotionItem.getWhetherVirtually());
            if (cartPromotionItem.getWhetherVirtually()) {
                orderItem.setDeviceUidJson(JSON.toJSONString(cartPromotionItem.getCustomerDeviceUidList()));
            }
            if (StringUtils.isNotEmpty(cartPromotionItem.getReferralCode())) {
                SmsReferralCodeProduct referralCodeProduct = referralCodeProductDao.getReferralCode(cartPromotionItem.getReferralCode(),
                        cartPromotionItem.getProductId(), cartPromotionItem.getProductSkuId());
                if (referralCodeProduct != null && referralCodeProduct.getWhetherEnable()) {
                    TdsTenant distributor = distributorMapper.selectById(referralCodeProduct.getDistributorId());
                    if (distributor != null) {
                        List<DivideAccountJsonarrayClass> divideAccountList = JSON.parseArray(referralCodeProduct.getDivideAccountJsonarray(),
                                DivideAccountJsonarrayClass.class);
                        BigDecimal totalDivideAmount = new BigDecimal("0");
                        BigDecimal productTotalPrice = cartPromotionItem.getPerPrice().multiply(new BigDecimal(cartPromotionItem.getQuantity()));
                        for (DivideAccountJsonarrayClass divideAccount : divideAccountList) {
                            BigDecimal divideAmount;
                            if (referralCodeProduct.getDivideMode().equals(SmsDivideModeEnum.FIX_AMOUNT.getDivideMode())) {
                                divideAmount = divideAccount.getDivideValue();
                            } else {
                                divideAmount = divideAccount.getDivideValue()
                                        .multiply(productTotalPrice)
                                        .divide(new BigDecimal(100), 2, RoundingMode.HALF_EVEN);
                            }
                            divideAccount.setDivideAmount(divideAmount);
                            totalDivideAmount = totalDivideAmount.add(divideAmount);
                        }
                        if (totalDivideAmount.compareTo(productTotalPrice) > 0) {
                            throw BizException.build(BizCode.ORDER_DIVIDE_AMOUNT_ERROR);
                        }
                        DivideDetailJsonClass divideJson = new DivideDetailJsonClass();
                        divideJson.setReferralCode(cartPromotionItem.getReferralCode());
                        divideJson.setDistributorId(distributor.getId());
                        divideJson.setDivideMode(referralCodeProduct.getDivideMode());
                        divideJson.setSettleMode(distributor.getSettleMode());
                        divideJson.setTotalDivideAmount(totalDivideAmount);
                        divideJson.setDivideAccountJsonarrayClassList(divideAccountList);
                        orderItem.setDivideReferralCode(cartPromotionItem.getReferralCode());
                        orderItem.setDivideDetailJson(JSON.toJSONString(divideJson));
                        orderItem.setDivideTotalAmount(totalDivideAmount);
                    }
                }
            }
            orderItemList.add(orderItem);
        }
        //判断购物车中商品是否都有库存
        if (!hasStock(cartPromotionItemList)) {
            throw BizException.build(BizCode.ORDER_SKU_NOT_ENOUGH);
        }
        //判断使用使用了优惠券
        if (couponId == null) {
            //不用优惠券
            for (OmsOrderItem orderItem : orderItemList) {
                orderItem.setPerCouponAmount(new BigDecimal(0));
            }
        } else {
            //使用优惠券
            SmsCouponHistory couponHistoryDetail = getUseCoupon(cartPromotionItemList, couponId, CurrentAuthorization.getUserId());
            if (couponHistoryDetail == null) {
                throw BizException.build(BizCode.COUPON_CANNOT_USE);
            }
            //对下单商品的优惠券进行处理
            calcPerCouponAmount(orderItemList, couponHistoryDetail.getCoupon());
        }
        //计算order_item的实付金额
        handleRealAmount(orderItemList);
        //进行库存锁定
        lockStock(cartPromotionItemList);
        //根据商品合计、运费、活动优惠、优惠券、积分计算应付金额
        OmsOrder order = new OmsOrder();
        order.setTotalAmount(calcTotalAmount(orderItemList));
        order.setTotalPromotionAmount(calcPromotionAmount(orderItemList));
        order.setPromotionInfo(getOrderPromotionInfo(orderItemList));
        if (couponId == null) {
            order.setTotalCouponAmount(new BigDecimal(0));
        } else {
            order.setCouponId(couponId);
            order.setTotalCouponAmount(calcCouponAmount(orderItemList));
        }
        BigDecimal totalPayAmount = calcPayAmount(order);
        order.setTotalPayAmount(totalPayAmount);
        //转化为订单信息并插入数据库
        order.setMemberId(currentMember.getId());
        order.setMemberUsername(currentMember.getUsername());
        //支付方式：1->支付宝；2->微信
        order.setPayType(payType);
        //订单来源：0->PC订单；1->app订单
        order.setSourceType(sourceType);
        //订单状态：0->待付款；1->待发货；2->已发货；3->已完成；4->已关闭；5->无效订单
        order.setStatus(OmsOrderStatusEnum.WAITPAY.getOrderStatus());
        //收货人信息：姓名、电话、邮编、地址
        order.setReceiverDetailJson(JSON.toJSONString(receiverDetailJsonClass));
        order.setWhetherConfirm(false);
        //计算赠送积分
        order.setTotalGiftPoint(calcGiftPoint(orderItemList));
        //计算赠送成长值
        order.setTotalGiftGrowth(calcGiftGrowth(orderItemList));
        //生成订单号
        order.setOrderSn(generateOrderSn());
        //订单构造完成后创建stripe订单

        Map<String, Object> parterOrderInfo = new HashMap<>();
        try {
            PaymentIntent paymentIntent = stripeClient.createPaymentIntent(totalPayAmount.multiply(new BigDecimal(100)).longValue(), "usd");
            parterOrderInfo.put("id", paymentIntent.getId());
            parterOrderInfo.put("amount", paymentIntent.getAmount());
            parterOrderInfo.put("currency", paymentIntent.getCurrency());
            parterOrderInfo.put("client_secret", paymentIntent.getClientSecret());
        } catch (Exception e) {
            log.error("createPaymentIntent error", e);
            throw BizException.build(BizCode.PARTER_STRIPE_CREATE_ORDER_ERROR);
        }
        order.setPartnerOrderId(parterOrderInfo.get("id").toString());
        //插入order表和order_item表
        orderMapper.insert(order);
        for (int i = 0; i < orderItemList.size(); i++) {
            OmsOrderItem orderItem = orderItemList.get(i);
            orderItem.setOrderId(order.getId());
            orderItem.setOrderSn(order.getOrderSn() + i);
        }
        omsOrderItemDao.insertList(orderItemList);
        //如使用优惠券更新优惠券使用状态
        if (couponId != null) {
            updateCouponStatus(couponId, currentMember.getId(), SmsCouponUseStatusEnum.USED.getUseStatus());
        }
        //删除购物车中的下单商品
        deleteCartItemList(cartPromotionItemList, CurrentAuthorization.getUserId());
        try {
            LambdaQueryWrapper<AdsConfig> cmsConfigLambdaQueryWrapper = new LambdaQueryWrapper<>();
            cmsConfigLambdaQueryWrapper.eq(AdsConfig::getConfigKey, ConfigKeyEnum.ORDER_TIMEOUT_KEY.getKey());
            AdsConfig config = cmsConfigMapper.selectOne(cmsConfigLambdaQueryWrapper);
            int defaultSecond = 1800;
            if (config != null) {
                defaultSecond = Integer.parseInt(config.getConfigValue()) * 60;
            }
            JSONObject orderJson = new JSONObject();
            orderJson.put("orderId", order.getId());
            rabbitMqOrderCancelProducer.sendMessage(orderJson, defaultSecond);
        } catch (Exception e) {
            log.error("rabbitMqOrderCancelProducer send msg error", e);
        }
        Map<String, Object> result = new HashMap<>();
        result.put("order", order);
        result.put("orderItemList", orderItemList);
        result.put("partnerOrderInfo", parterOrderInfo);
        return result;
    }

    @Override
    @Transactional
    public void cancelOrder(String orderId, boolean authExpire) {
        //查询未付款的取消订单
        LambdaQueryWrapper<OmsOrder> example = new LambdaQueryWrapper<>();
        example.eq(OmsOrder::getId, orderId);
        example.eq(OmsOrder::getStatus, OmsOrderStatusEnum.WAITPAY.getOrderStatus());

        OmsOrder cancelOrder = orderMapper.selectOne(example);
        if (cancelOrder != null) {
            //修改订单状态为取消
            cancelOrder.setStatus(OmsOrderStatusEnum.CLOSED.getOrderStatus());
            cancelOrder.setNote(authExpire ? "自动过期" : "用户取消");
            int result = orderMapper.update(cancelOrder, example);
            if (result == 0) {
                throw BizException.build(BizCode.ORDER_STATUS_ERROR);
            }
            LambdaQueryWrapper<OmsOrderItem> orderItemExample = new LambdaQueryWrapper<>();
            orderItemExample.eq(OmsOrderItem::getOrderId, orderId);
            List<OmsOrderItem> orderItemList = omsOrderItemMapper.selectList(orderItemExample);
            //解除订单商品库存锁定
            if (!CollectionUtils.isEmpty(orderItemList)) {
                for (OmsOrderItem orderItem : orderItemList) {
                    int count = omsOrderDao.releaseStockBySkuId(orderItem.getProductSkuId(), orderItem.getProductQuantity());
                    if (count == 0) {
                        throw BizException.build(BizCode.ORDER_SKU_NOT_ENOUGH);
                    }
                }
            }
            //修改优惠券使用状态
            updateCouponStatus(cancelOrder.getCouponId(), cancelOrder.getMemberId(), SmsCouponUseStatusEnum.UNUSE.getUseStatus());
        }
    }

    @Override
    public CommonPage<OmsOrder> list(Integer status, Integer pageNum, Integer pageSize) {
        LambdaQueryWrapper<OmsOrder> orderExample = new LambdaQueryWrapper<>();
        orderExample.eq(OmsOrder::getMemberId, CurrentAuthorization.getUserId());
        if (StringUtils.isNotNull(status)) {
            orderExample.eq(OmsOrder::getStatus, status);
        }
        Page<OmsOrder> page = orderMapper.selectPage(PageUtil.buildPage(pageNum, pageSize), orderExample);
        if (CollUtil.isEmpty(page.getRecords())) {
            return CommonPage.restPage(page);
        }
        //设置数据信息
        List<String> orderIds = Lists.transform(page.getRecords(), OmsOrder::getId);

        LambdaQueryWrapper<OmsOrderItem> orderItemExample = new LambdaQueryWrapper<>();
        orderItemExample.in(OmsOrderItem::getOrderId, orderIds);
        List<OmsOrderItem> orderItemList = omsOrderItemMapper.selectList(orderItemExample);
        for (OmsOrder omsOrder : page.getRecords()) {
            List<OmsOrderItem> relatedItemList =
                    orderItemList.stream().filter(item -> item.getOrderId().equals(omsOrder.getId())).collect(Collectors.toList());
            omsOrder.setOrderItemList(relatedItemList);
        }
        return CommonPage.restPage(page);
    }

    @Override
    public OmsOrder detail(String orderId) {
        OmsOrder omsOrder = orderMapper.selectById(orderId);
        omsOrder.setReceiverDetailJsonClass(JSON.parseObject(omsOrder.getReceiverDetailJson(), ReceiverDetailJsonClass.class));
        LambdaQueryWrapper<OmsOrderItem> orderItemExample = new LambdaQueryWrapper<>();
        orderItemExample.eq(OmsOrderItem::getOrderId, orderId);
        List<OmsOrderItem> orderItemList = omsOrderItemMapper.selectList(orderItemExample);
        List<AppProductParam> orderDeviceAppProduct = Lists.newArrayList();
        orderItemList.forEach(omsOrderItem -> {
            if (StringUtils.isNotEmpty(omsOrderItem.getDeviceUidJson())) {
                List<String> uids = JSON.parseArray(omsOrderItem.getDeviceUidJson(), String.class);
                List<AppProductParam> orderItemDeviceAppProduct = Lists.newArrayList();
                uids.forEach(uid -> {
                    AppProductParam appProductParam = new AppProductParam();
                    appProductParam.setProductSkuName(omsOrderItem.getProductSkuName());
                    appProductParam.setUid(uid);
                    orderItemDeviceAppProduct.add(appProductParam);
                    orderDeviceAppProduct.add(appProductParam);
                });
                omsOrderItem.setDeviceProfileUrl(deviceMgtClient.generateUrl(orderItemDeviceAppProduct));
            }
        });
        omsOrder.setDeviceProfileUrl(deviceMgtClient.generateUrl(orderDeviceAppProduct));
        omsOrder.setOrderItemList(orderItemList);
        return omsOrder;
    }

    @Override
    public void deleteOrder(String orderId) {
        OmsOrder order = orderMapper.selectById(orderId);
        if (!CurrentAuthorization.getUserId().equals(order.getMemberId())) {
            throw BizException.build(SystemCode.ILLEGAL_OPERATION);
        }
        if (order.getStatus().equals(OmsOrderStatusEnum.CLOSED.getOrderStatus()) || order.getStatus().equals(OmsOrderStatusEnum.REFOUNDED.getOrderStatus())) {
            orderMapper.deleteById(orderId);
        } else {
            throw BizException.build(BizCode.ORDER_STATUS_ERROR);
        }
    }

    /**
     * 生成22位订单编号:14位日期+2位平台号码+6位
     */
    private String generateOrderSn() {
        StringBuilder sb = new StringBuilder();
        String date = new SimpleDateFormat("yyyyMMddhhmmss").format(new Date());
        sb.append(date);
        sb.append(RandomUtil.randomNumbers(6));
        return sb.toString();
    }

    /**
     * 删除下单商品的购物车信息
     */
    private void deleteCartItemList(List<CartPromotionItemDto> cartPromotionItemList, String memberId) {
        List<String> ids = Lists.transform(cartPromotionItemList, CartPromotionItemDto::getId);
        LambdaQueryWrapper<OmsCartItem> example = new LambdaQueryWrapper<>();
        example.in(OmsCartItem::getId, ids);
        example.eq(OmsCartItem::getMemberId, memberId);
        cartItemMapper.delete(example);
    }

    /**
     * 计算该订单赠送的成长值
     */
    private Integer calcGiftGrowth(List<OmsOrderItem> orderItemList) {
        int sum = 0;
        for (OmsOrderItem orderItem : orderItemList) {
            sum = sum + orderItem.getPerGiftGrowth() * orderItem.getProductQuantity();
        }
        return sum;
    }

    /**
     * 计算该订单赠送的积分
     */
    private Integer calcGiftPoint(List<OmsOrderItem> orderItemList) {
        int sum = 0;
        for (OmsOrderItem orderItem : orderItemList) {
            sum = sum + orderItem.getPerGiftPoint() * orderItem.getProductQuantity();
        }
        return sum;
    }

    /**
     * 将优惠券信息更改为指定状态
     *
     * @param couponId  优惠券id
     * @param memberId  会员id
     * @param useStatus 0->未使用；1->已使用
     */
    private void updateCouponStatus(String couponId, String memberId, Integer useStatus) {
        if (couponId == null) {
            return;
        }
        //查询第一张优惠券
        LambdaQueryWrapper<SmsCouponHistory> example = new LambdaQueryWrapper<>();
        example.eq(SmsCouponHistory::getMemberId, memberId);
        example.eq(SmsCouponHistory::getCouponId, couponId);
        example.eq(SmsCouponHistory::getUseStatus, useStatus.equals(SmsCouponUseStatusEnum.UNUSE.getUseStatus()) ?
                SmsCouponUseStatusEnum.USED.getUseStatus() :
                SmsCouponUseStatusEnum.UNUSE.getUseStatus());
        List<SmsCouponHistory> couponHistoryList = couponHistoryMapper.selectList(example);
        if (!CollectionUtils.isEmpty(couponHistoryList)) {
            SmsCouponHistory couponHistory = couponHistoryList.get(0);
            couponHistory.setUseTime(new Date());
            couponHistory.setUseStatus(useStatus);
            couponHistoryMapper.updateById(couponHistory);
        }
    }

    private void handleRealAmount(List<OmsOrderItem> orderItemList) {
        for (OmsOrderItem orderItem : orderItemList) {
            //原价-促销优惠-优惠券抵扣-积分抵扣
            BigDecimal realAmount = orderItem.getPerProductPrice()
                    .subtract(orderItem.getPerPromotionAmount())
                    .subtract(orderItem.getPerCouponAmount());
            orderItem.setPerRealAmount(realAmount);
        }
    }

    /**
     * 获取订单促销信息
     */
    private String getOrderPromotionInfo(List<OmsOrderItem> orderItemList) {
        StringBuilder sb = new StringBuilder();
        for (OmsOrderItem orderItem : orderItemList) {
            sb.append(orderItem.getPromotionName());
            sb.append(";");
        }
        String result = sb.toString();
        if (result.endsWith(";")) {
            result = result.substring(0, result.length() - 1);
        }
        return result;
    }

    /**
     * 计算订单应付金额
     */
    private BigDecimal calcPayAmount(OmsOrder order) {
        //总金额+运费-促销优惠-优惠券优惠-积分抵扣
        return order.getTotalAmount()
                .subtract(order.getTotalPromotionAmount())
                .subtract(order.getTotalCouponAmount());
    }

    /**
     * 计算订单优惠券金额
     */
    private BigDecimal calcCouponAmount(List<OmsOrderItem> orderItemList) {
        BigDecimal couponAmount = new BigDecimal(0);
        for (OmsOrderItem orderItem : orderItemList) {
            if (orderItem.getPerCouponAmount() != null) {
                couponAmount = couponAmount.add(orderItem.getPerCouponAmount().multiply(new BigDecimal(orderItem.getProductQuantity())));
            }
        }
        return couponAmount;
    }

    /**
     * 计算订单活动优惠
     */
    private BigDecimal calcPromotionAmount(List<OmsOrderItem> orderItemList) {
        BigDecimal promotionAmount = new BigDecimal(0);
        for (OmsOrderItem orderItem : orderItemList) {
            if (orderItem.getPerPromotionAmount() != null) {
                promotionAmount = promotionAmount.add(orderItem.getPerPromotionAmount().multiply(new BigDecimal(orderItem.getProductQuantity())));
            }
        }
        return promotionAmount;
    }

    /**
     * 对每个下单商品进行优惠券金额分摊的计算
     *
     * @param orderItemList 可用优惠券的下单商品商品
     */
    private void calcPerCouponAmount(List<OmsOrderItem> orderItemList, SmsCoupon coupon) {
        BigDecimal totalAmount = calcTotalAmount(orderItemList);
        for (OmsOrderItem orderItem : orderItemList) {
            //(商品价格/可用商品总价)*优惠券面额
            BigDecimal couponAmount = orderItem.getPerProductPrice().divide(totalAmount, 3, RoundingMode.HALF_EVEN).multiply(coupon.getAmount());
            orderItem.setPerCouponAmount(couponAmount);
        }
    }

    /**
     * 获取该用户可以使用的优惠券
     *
     * @param cartPromotionItemList 购物车优惠列表
     * @param couponId              使用优惠券id
     */

    private SmsCouponHistory getUseCoupon(List<CartPromotionItemDto> cartPromotionItemList, String couponId, String memberId) {
        List<SmsCouponHistory> couponHistoryDetailList = orderHandler.listCartCoupon(cartPromotionItemList, 1, memberId);
        for (SmsCouponHistory couponHistoryDetail : couponHistoryDetailList) {
            if (couponHistoryDetail.getCoupon().getId().equals(couponId)) {
                return couponHistoryDetail;
            }
        }
        return null;
    }

    /**
     * 计算总金额
     */
    private BigDecimal calcTotalAmount(List<OmsOrderItem> orderItemList) {
        BigDecimal totalAmount = new BigDecimal("0");
        for (OmsOrderItem item : orderItemList) {
            totalAmount = totalAmount.add(item.getPerProductPrice().multiply(new BigDecimal(item.getProductQuantity())));
        }
        return totalAmount;
    }

    /**
     * 锁定下单商品的所有库存
     */
    private void lockStock(List<CartPromotionItemDto> cartPromotionItemList) {
        for (CartPromotionItemDto cartPromotionItem : cartPromotionItemList) {
            PmsProductSku skuStock = productSkuMapper.selectById(cartPromotionItem.getProductSkuId());
            skuStock.setLockStock(skuStock.getLockStock() + cartPromotionItem.getQuantity());
            int count = omsOrderDao.lockStockBySkuId(cartPromotionItem.getProductSkuId(), cartPromotionItem.getQuantity());
            if (count == 0) {
                throw BizException.build(BizCode.ORDER_SKU_NOT_ENOUGH);
            }
        }
    }

    /**
     * 判断下单商品是否都有库存
     */
    private boolean hasStock(List<CartPromotionItemDto> cartPromotionItemList) {
        for (CartPromotionItemDto cartPromotionItem : cartPromotionItemList) {
            //判断真实库存是否为空 判断真实库存是否小于0 判断真实库存是否小于下单的数量
            if (cartPromotionItem.getRealStock() == null
                    || cartPromotionItem.getRealStock() <= 0
                    || cartPromotionItem.getRealStock() < cartPromotionItem.getQuantity()) {
                return false;
            }
        }
        return true;
    }

    /**
     * 计算购物车中商品的价格
     */
    private ConfirmOrderVo.CalcAmount calcCartAmount(List<CartPromotionItemDto> cartPromotionItemList) {
        ConfirmOrderVo.CalcAmount calcAmount = new ConfirmOrderVo.CalcAmount();
        BigDecimal totalAmount = new BigDecimal("0");
        BigDecimal promotionAmount = new BigDecimal("0");
        for (CartPromotionItemDto cartPromotionItem : cartPromotionItemList) {
            totalAmount = totalAmount.add(cartPromotionItem.getPerPrice().multiply(new BigDecimal(cartPromotionItem.getQuantity())));
            promotionAmount = promotionAmount.add(cartPromotionItem.getPerReduceAmount().multiply(new BigDecimal(cartPromotionItem.getQuantity())));
        }
        calcAmount.setTotalAmount(totalAmount);
        calcAmount.setPromotionAmount(promotionAmount);
        calcAmount.setPayAmount(totalAmount.subtract(promotionAmount));
        return calcAmount;
    }

    @Override
    @Transactional
    public int confirmDeliveryOrder(String orderId) {
        LambdaQueryWrapper<OmsOrder> updateWrapper = new LambdaQueryWrapper<>();
        updateWrapper.eq(OmsOrder::getStatus, OmsOrderStatusEnum.DELIVERYED.getOrderStatus());
        updateWrapper.eq(OmsOrder::getId, orderId);
        OmsOrder update = new OmsOrder();
        update.setStatus(OmsOrderStatusEnum.OVER.getOrderStatus());
        update.setConfirmTime(new Date());
        update.setWhetherConfirm(true);
        int result = orderMapper.update(update, updateWrapper);
        if (result == 0) {
            throw BizException.build(BizCode.ORDER_STATUS_ERROR);
        }
        try {
            JSONObject orderJson = new JSONObject();
            orderJson.put("orderId", orderId);
            rabbitMqOrderConfirmProducer.sendMessage(orderJson);
        } catch (Exception e) {
            log.error("rabbitMqOrderConfirmProducer send msg error", e);
        }
        return result;
    }

}
