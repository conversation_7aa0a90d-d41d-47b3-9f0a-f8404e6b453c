<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bosi.sim.paas.admin.server.dao.PmsProductFullReductionDao">

    <insert id="insertList" useGeneratedKeys="true" keyProperty="id">
        <foreach collection="list" item="item" separator=";">
            insert into sds_product_full_reduction
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">id,</if>
                <if test="item.productId != null">product_id,</if>
                <if test="item.fullPrice != null">full_price,</if>
                <if test="item.reducePrice != null">reduce_price,</if>
            </trim>
            values
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">#{item.id},</if>
                <if test="item.productId != null">#{item.productId},</if>
                <if test="item.fullPrice != null">#{item.fullPrice},</if>
                <if test="item.reducePrice != null">#{item.reducePrice},</if>
            </trim>
        </foreach>
    </insert>

</mapper>
