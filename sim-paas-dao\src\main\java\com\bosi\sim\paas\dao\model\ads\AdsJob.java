package com.bosi.sim.paas.dao.model.ads;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.enums.ads.AdsMisfirePolicyEnum;
import com.bosi.sim.paas.dao.model.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("ads_job")
public class AdsJob extends BaseEntity {
    /**
     * 任务名称
     */
    private String jobName;

    /**
     * 任务组名
     */
    private String jobGroup;

    /**
     * 调用目标字符串
     */
    private String invokeTarget;

    /**
     * cron执行表达式
     */
    private String cronExpression;

    /**
     * cron计划策略
     */
    private Integer misfirePolicy = AdsMisfirePolicyEnum.MISFIRE_DEFAULT.getMisfirePolicy();

    /**
     * 是否并发执行
     */
    private Boolean whetherConcurrent;

    /**
     * 是否启用
     */
    private Boolean whetherEnable;

}
