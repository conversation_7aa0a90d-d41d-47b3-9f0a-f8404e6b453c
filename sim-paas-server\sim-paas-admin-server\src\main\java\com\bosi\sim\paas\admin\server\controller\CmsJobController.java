package com.bosi.sim.paas.admin.server.controller;

import com.bosi.sim.paas.common.core.api.BizCode;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.api.SystemCode;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.exception.CommonResult;
import com.bosi.sim.paas.common.core.utils.PageUtil;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.common.optlog.annotation.OperateLog;
import com.bosi.sim.paas.common.security.annotation.RequiresPermissions;
import com.bosi.sim.paas.common.security.auth.CurrentAuthorization;
import com.bosi.sim.paas.dao.enums.LinkPreEnum;
import com.bosi.sim.paas.dao.enums.ads.AdsJobInvokeEnum;
import com.bosi.sim.paas.dao.model.ads.AdsJob;
import com.bosi.sim.paas.admin.server.service.CmsJobService;
import com.bosi.sim.paas.admin.server.util.AbstractQuartzJob;
import com.bosi.sim.paas.admin.server.util.CronUtils;
import io.swagger.annotations.Api;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;

/**
 * 调度任务信息操作处理
 */
@RestController
@Api(tags = "CmsJobController", value = "调度任务信息操作处理")
@RequestMapping("/cms/job")
public class CmsJobController {
    @Autowired
    private CmsJobService jobService;

    /**
     * 查询定时任务列表
     */
    @RequiresPermissions("cms:job:page")
    @GetMapping("/page")
    public CommonResult page(AdsJob sysJob,
                             @RequestParam(value = "pageSize") Integer pageSize,
                             @RequestParam(value = "pageNum") Integer pageNum) {
        CommonPage<AdsJob> page = jobService.page(PageUtil.buildPage(pageNum, pageSize), sysJob);
        return CommonResult.success(page);
    }

    /**
     * 获取定时任务详细信息
     */
    @RequiresPermissions("cms:job:query")
    @GetMapping(value = "/{id}")
    public CommonResult getInfo(@PathVariable String id) {
        return CommonResult.success(jobService.selectJobById(id));
    }

    /**
     * 新增定时任务
     */
    @RequiresPermissions("cms:job:add")
    @OperateLog("Add Job Task")
    @PostMapping
    public CommonResult add(@RequestBody AdsJob job) throws SchedulerException, BizException {
        if (!CronUtils.isValid(job.getCronExpression())) {
            throw BizException.build(BizCode.TASK_CRON_EXPRESSION_ERROR);
        } else if (StringUtils.containsIgnoreCase(job.getInvokeTarget(), AdsJobInvokeEnum.LOOKUP_RMI.getValue())) {
            throw BizException.build(BizCode.TASK_TARGET_NOT_ALLOW, "rmi");
        } else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), new String[]{AdsJobInvokeEnum.LOOKUP_LDAP.getValue(),
                AdsJobInvokeEnum.LOOKUP_LDAPS.getValue()})) {
            throw BizException.build(BizCode.TASK_TARGET_NOT_ALLOW, "ldap(s)");
        } else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), new String[]{LinkPreEnum.HTTP.getValue(),
                LinkPreEnum.HTTPS.getValue()})) {
            throw BizException.build(BizCode.TASK_TARGET_NOT_ALLOW, "http(s)");
        } else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), AbstractQuartzJob.JOB_ERROR_STR)) {
            throw BizException.build(BizCode.TASK_TARGET_VIOLATION);
        }
        job.setCreateBy(CurrentAuthorization.getUsername());
        return CommonResult.success(jobService.insertJob(job));
    }

    /**
     * 修改定时任务
     */
    @RequiresPermissions("cms:job:edit")
    @OperateLog("Update Job Task")
    @PutMapping
    public CommonResult edit(@RequestBody AdsJob job) throws SchedulerException, BizException {
        if (!CronUtils.isValid(job.getCronExpression())) {
            throw BizException.build(BizCode.TASK_CRON_EXPRESSION_ERROR);
        } else if (StringUtils.containsIgnoreCase(job.getInvokeTarget(), AdsJobInvokeEnum.LOOKUP_RMI.getValue())) {
            throw BizException.build(BizCode.TASK_TARGET_NOT_ALLOW, "rmi");
        } else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), new String[]{AdsJobInvokeEnum.LOOKUP_LDAP.getValue(),
                AdsJobInvokeEnum.LOOKUP_LDAPS.getValue()})) {
            throw BizException.build(BizCode.TASK_TARGET_NOT_ALLOW, "ldap(s)");
        } else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), new String[]{LinkPreEnum.HTTP.getValue(),
                LinkPreEnum.HTTPS.getValue()})) {
            throw BizException.build(BizCode.TASK_TARGET_NOT_ALLOW, "http(s)");
        } else if (StringUtils.containsAnyIgnoreCase(job.getInvokeTarget(), AbstractQuartzJob.JOB_ERROR_STR)) {
            throw BizException.build(BizCode.TASK_TARGET_VIOLATION);
        }
        job.setUpdateBy(CurrentAuthorization.getUsername());
        return CommonResult.success(jobService.updateJob(job));
    }

    /**
     * 定时任务状态修改
     */
    @RequiresPermissions("cms:job:changeStatus")
    @OperateLog("Update Job Task Status")
    @PutMapping("/changeStatus")
    public CommonResult changeStatus(@RequestBody AdsJob job) throws SchedulerException {
        AdsJob newJob = jobService.selectJobById(job.getId());
        newJob.setWhetherEnable(job.getWhetherEnable());
        return CommonResult.success(jobService.changeStatus(newJob));
    }

    /**
     * 定时任务立即执行一次
     */
    @RequiresPermissions("cms:job:changeStatus")
    @OperateLog("Run Job Task")
    @PutMapping("/run")
    public CommonResult run(@RequestBody AdsJob job) throws SchedulerException {
        boolean result = jobService.run(job);
        if (!result) {
            throw BizException.build(SystemCode.RESOURCE_NOTFOUND);
        }
        return CommonResult.success();
    }

    /**
     * 删除定时任务
     */
    @RequiresPermissions("cms:job:remove")
    @OperateLog("Delete Job Task")
    @DeleteMapping("/{ids}")
    public CommonResult remove(@PathVariable String[] ids) throws SchedulerException, BizException {
        jobService.deleteJobByIds(Arrays.asList(ids));
        return CommonResult.success();
    }
}
