package com.bosi.sim.paas.admin.server.config.rabbitmq;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.bosi.sim.paas.dao.enums.tds.TdsMovementSourceEnum;
import com.bosi.sim.paas.dao.enums.tds.TdsMovementTypeEnum;
import com.bosi.sim.paas.dao.mapper.tms.TmsUserAccountMovementMapper;
import com.bosi.sim.paas.dao.model.tds.TdsTenantAccountMovement;
import com.bosi.sim.paas.admin.server.dao.TmsUserAccountDao;
import lombok.extern.log4j.Log4j2;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

@Component
@Log4j2
public class RabbitMqAccountMovementConsumer {

    @Autowired
    private TmsUserAccountDao tmsUserAccountDao;

    @Autowired
    private TmsUserAccountMovementMapper tmsUserAccountMovementMapper;

    @RabbitListener(queues = {RabbitMqAccountMovementConfig.ACCOUNT_MOVEMENT_SEQ_QUEUE_NAME})
    @Transactional
    public void onMessage(Message message) {
        try {
            byte[] messageBody = message.getBody();
            String msg = new String(messageBody);
            log.debug("seq queue of {} receive msg:{}", RabbitMqAccountMovementConfig.ACCOUNT_MOVEMENT_SEQ_QUEUE_NAME, msg);
            JSONObject messageJson = JSON.parseObject(msg);
            String accountId = messageJson.getString("accountId");
            Integer availableBalanceChangeType = messageJson.getInteger("availableBalanceChangeType");
            BigDecimal availableBalanceChangeValue = messageJson.getBigDecimal("availableBalanceChangeValue");
            if (availableBalanceChangeType != null && availableBalanceChangeValue != null) {
                if (availableBalanceChangeType.equals(TdsMovementTypeEnum.INCOME.getMovementType())) {
                    int result = tmsUserAccountDao.increaseAvailableAmount(accountId, availableBalanceChangeValue);
                    if (result == 0) {
                        log.error("increaseAvailableAmount result fail:{}", msg);
                        return;
                    }
                    TdsTenantAccountMovement accountMovement = new TdsTenantAccountMovement();
                    accountMovement.setMovementSource(TdsMovementSourceEnum.SETTLE.getMovementSource());
                    accountMovement.setAccountId(accountId);
                    accountMovement.setMovementType(TdsMovementTypeEnum.INCOME.getMovementType());
                    accountMovement.setMovementAmount(availableBalanceChangeValue);
                    tmsUserAccountMovementMapper.insert(accountMovement);
                }
            }
            Integer intransitBalanceChangeType = messageJson.getInteger("intransitBalanceChangeType");
            BigDecimal intransitBalanceChangeValue = messageJson.getBigDecimal("intransitBalanceChangeValue");
            if (intransitBalanceChangeType != null && intransitBalanceChangeValue != null) {
                if (intransitBalanceChangeType.equals(TdsMovementTypeEnum.INCOME.getMovementType())) {
                    int result = tmsUserAccountDao.increaseIntransitBalance(accountId, availableBalanceChangeValue);
                    if (result == 0) {
                        log.error("increaseIntransitBalance result fail:{}", msg);
                        return;
                    }
                } else if (intransitBalanceChangeType.equals(TdsMovementTypeEnum.OUTCOME.getMovementType())) {
                    int result = tmsUserAccountDao.decreaseIntransitBalance(accountId, availableBalanceChangeValue);
                    if (result == 0) {
                        log.error("decreaseIntransitBalance result fail:{}", msg);
                        return;
                    }
                }
            }
            log.info("handle msg success");
        } catch (Exception e) {
            log.error("handle msg fail", e);
        }
    }

}
