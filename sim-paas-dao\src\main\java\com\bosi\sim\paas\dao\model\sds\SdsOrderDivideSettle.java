package com.bosi.sim.paas.dao.model.sds;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("sds_order_divide_settle")
public class SdsOrderDivideSettle extends BaseEntity {
    @ApiModelProperty(value = "订单id")
    private String orderDivideId;

    private String accountId;

    private Boolean whetherSettle;

    private String operateUser;

    private BigDecimal settleAmount;

    @TableField(exist = false)
    private String accountUserName;

}
