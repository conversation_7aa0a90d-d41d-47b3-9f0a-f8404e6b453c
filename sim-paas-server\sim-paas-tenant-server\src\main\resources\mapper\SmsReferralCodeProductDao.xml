<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bosi.sim.paas.tenant.server.dao.SmsReferralCodeProductDao">

    <select id="listByReferralCodeId" resultType="com.bosi.sim.paas.dao.model.sds.SmsReferralCodeProduct">
        select
            a.*,
            c.product_name,
            c.product_sn,
            b.sku_name,
            b.sku_code,
            b.price,
            b.original_price,
            b.stock,
            b.sp_data
        from
            sms_referral_code_product as a
        left join
            sds_product_sku as b on a.product_sku_id = b.id
        left join
            sds_product as c on a.product_id = c.id
        where
            a.whether_delete = false
        and
            a.referral_code_id = #{referralCodeId}
    </select>
</mapper>
