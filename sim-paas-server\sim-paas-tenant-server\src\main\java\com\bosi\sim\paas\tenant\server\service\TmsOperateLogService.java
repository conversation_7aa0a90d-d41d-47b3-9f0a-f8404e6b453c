package com.bosi.sim.paas.tenant.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.optlog.service.OptLogService;
import com.bosi.sim.paas.dao.model.tds.TdsOperateLog;

/**
 * 操作日志 服务层
 */
public interface TmsOperateLogService extends OptLogService {

    /**
     * 查询系统操作日志集合
     *
     * @param operLog 操作日志对象
     * @return 操作日志集合
     */
    CommonPage<TdsOperateLog> page(Page<TdsOperateLog> ipage, TdsOperateLog operLog);

    /**
     * 批量删除系统操作日志
     *
     * @param operIds 需要删除的操作日志ID
     * @return 结果
     */
    int deleteOperLogByIds(String[] operIds);

    /**
     * 查询操作日志详细
     *
     * @param operId 操作ID
     * @return 操作日志对象
     */
    TdsOperateLog selectOperLogById(String operId);
}
