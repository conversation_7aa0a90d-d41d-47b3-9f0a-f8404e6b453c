package com.bosi.sim.paas.dao.enums.sds;


public enum SdsOrderStatusEnum {

    WAITPAY(0, "待支付"),

    WAITDELIVERY(1, "待发货"),

    DELIVERYED(2, "已发货"),

    OVER(3, "已完成"),

    REFOUNDED(4, "已退款"),

    CLOSED(5, "已关闭");

    private Integer orderStatus;

    private String desc;

    SdsOrderStatusEnum(Integer orderStatus, String desc) {
        this.orderStatus = orderStatus;
        this.desc = desc;
    }

    public Integer getOrderStatus() {
        return orderStatus;
    }

    public String getDesc() {
        return desc;
    }
}
