package com.bosi.sim.paas.tenant.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.dao.mapper.tms.TmsUserAccountMovementMapper;
import com.bosi.sim.paas.dao.model.tds.TdsTenantAccountMovement;
import com.bosi.sim.paas.tenant.server.service.TmsUserAccountMovementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 分销商管理 服务实现
 */
@Service
public class TmsUserAccountMovementServiceImpl implements TmsUserAccountMovementService {

    @Autowired
    private TmsUserAccountMovementMapper userAccountMovementMapper;

    @Override
    public CommonPage<TdsTenantAccountMovement> page(Page<TdsTenantAccountMovement> page, TdsTenantAccountMovement userAccountMovement) {
        LambdaQueryWrapper<TdsTenantAccountMovement> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotNull(userAccountMovement.getAccountId())) {
            queryWrapper.eq(TdsTenantAccountMovement::getAccountId, userAccountMovement.getAccountId());
        }
        if (StringUtils.isNotNull(userAccountMovement.getMovementSource())) {
            queryWrapper.eq(TdsTenantAccountMovement::getMovementSource, userAccountMovement.getMovementSource());
        }
        if (StringUtils.isNotNull(userAccountMovement.getMovementType())) {
            queryWrapper.eq(TdsTenantAccountMovement::getMovementType, userAccountMovement.getMovementType());
        }
        if (StringUtils.isNotNull(userAccountMovement.getQueryBeginTime())) {
            queryWrapper.le(TdsTenantAccountMovement::getCreateTime, userAccountMovement.getQueryBeginTime());
        }
        if (StringUtils.isNotNull(userAccountMovement.getQueryEndTime())) {
            queryWrapper.ge(TdsTenantAccountMovement::getCreateTime, userAccountMovement.getQueryEndTime());
        }
        return CommonPage.restPage(userAccountMovementMapper.selectPage(page, queryWrapper));
    }
}
