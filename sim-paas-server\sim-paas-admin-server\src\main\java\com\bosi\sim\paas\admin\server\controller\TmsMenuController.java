package com.bosi.sim.paas.admin.server.controller;

import com.alibaba.fastjson2.JSONObject;
import com.bosi.sim.paas.common.core.api.BizCode;
import com.bosi.sim.paas.common.core.exception.CommonResult;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.common.optlog.annotation.OperateLog;
import com.bosi.sim.paas.common.security.annotation.RequiresPermissions;
import com.bosi.sim.paas.common.security.auth.CurrentAuthorization;
import com.bosi.sim.paas.dao.model.tds.TdsMenu;
import com.bosi.sim.paas.admin.server.service.TmsMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 菜单信息
 */
@RestController
@Api(tags = "TmsMenuController", value = "后台菜单管理")
@RequestMapping("/tms/menu")
public class TmsMenuController {
    @Autowired
    private TmsMenuService menuService;

    /**
     * 获取菜单列表
     */
    @ApiOperation("查看后台菜单")
    @RequiresPermissions("tms:menu:list")
    @GetMapping("/list")
    public CommonResult list(TdsMenu menu) {
        List<TdsMenu> menus = menuService.selectList(menu);
        return CommonResult.success(menus);
    }

    /**
     * 根据菜单编号获取详细信息
     */
    @RequiresPermissions("tms:menu:query")
    @GetMapping("/{id}")
    public CommonResult getInfo(@PathVariable String id) {
        return CommonResult.success(menuService.selectMenuById(id));
    }

    /**
     * 获取菜单下拉树列表
     */
    @GetMapping("/listMenuAll")
    public CommonResult listMenuAll() {
        List<TdsMenu> menus = menuService.selectAll();
        return CommonResult.success(menuService.buildMenuTreeSelect(menus));
    }

    /**
     * 加载对应角色菜单列表树
     */
    @GetMapping("/roleMenuTreeselect/{roleId}")
    public CommonResult roleMenuTreeselect(@PathVariable("roleId") String roleId) {
        List<TdsMenu> menus = menuService.selectAll();
        JSONObject ajax = new JSONObject();
        ajax.put("checkedKeys", menuService.selectMenuListByRoleId(roleId));
        ajax.put("menus", menuService.buildMenuTreeSelect(menus));
        return CommonResult.success(ajax);
    }

    /**
     * 新增菜单
     */
    @RequiresPermissions("tms:menu:add")
    @OperateLog("新增菜单")
    @PostMapping
    public CommonResult add(@Validated @RequestBody TdsMenu menu) {
        if (!menuService.checkMenuNameUnique(menu)) {
            throw BizException.build(BizCode.MENU_NAME_EXIST);
        } else if (menu.getWhetherFrame() && !StringUtils.ishttp(menu.getPath())) {
            throw BizException.build(BizCode.MENU_FRAME_MUST_START_HTTP);
        }
        return CommonResult.success(menuService.insertMenu(menu));
    }

    /**
     * 修改菜单
     */
    @RequiresPermissions("tms:menu:edit")
    @OperateLog("修改菜单")
    @PutMapping
    public CommonResult edit(@Validated @RequestBody TdsMenu menu) {
        if (!menuService.checkMenuNameUnique(menu)) {
            throw BizException.build(BizCode.MENU_NAME_EXIST);
        } else if (menu.getWhetherFrame() && !StringUtils.ishttp(menu.getPath())) {
            throw BizException.build(BizCode.MENU_FRAME_MUST_START_HTTP);
        } else if (menu.getId().equals(menu.getParentId())) {
            throw BizException.build(BizCode.MENU_PARENT_CANNOT_SELF);
        }
        return CommonResult.success(menuService.updateMenu(menu));
    }

    /**
     * 删除菜单
     */
    @RequiresPermissions("tms:menu:remove")
    @OperateLog("删除菜单")
    @DeleteMapping("/{id}")
    public CommonResult remove(@PathVariable String id) {
        if (menuService.hasChildByMenuId(id)) {
            throw BizException.build(BizCode.MENU_EXIST_CHILD_FORBID_DELETE);
        }
        if (menuService.checkMenuExistRole(id)) {
            throw BizException.build(BizCode.MENU_ALREADY_ALLOCATION);
        }
        return CommonResult.success(menuService.deleteMenuById(id));
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public CommonResult getRouters() {
        String userId = CurrentAuthorization.getUserId();
        List<TdsMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return CommonResult.success(menuService.buildMenus(menus));
    }
}
