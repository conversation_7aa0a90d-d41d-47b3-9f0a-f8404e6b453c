package com.bosi.sim.paas.dao.model.sds;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@TableName("sds_category")
public class SdsCategory extends BaseEntity {
    @ApiModelProperty(value = "上机分类的编号：0表示一级分类")
    private String parentId;

    private String name;

    @ApiModelProperty(value = "分类级别：0->1级；1->2级")
    private Integer level;

    private Integer sort;

    private String keywords;

    @ApiModelProperty(value = "描述")
    private String description;

    @TableField(exist = false)
    private List<SdsCategory> children;

}
