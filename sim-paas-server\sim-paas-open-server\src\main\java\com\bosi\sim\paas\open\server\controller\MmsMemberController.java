package com.bosi.sim.paas.open.server.controller;

import com.bosi.sim.paas.common.core.api.BizCode;
import com.bosi.sim.paas.common.core.api.SystemCode;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.exception.CommonResult;
import com.bosi.sim.paas.common.security.auth.CurrentAuthorization;
import com.bosi.sim.paas.dao.enums.sds.SdsLoginSourceTypeEnum;
import com.bosi.sim.paas.dao.model.sds.SdsMember;
import com.bosi.sim.paas.open.server.domain.dto.MemberParterAuthInfoDto;
import com.bosi.sim.paas.open.server.domain.param.MemberLoginParam;
import com.bosi.sim.paas.open.server.domain.param.MemberPartnerLoginCallbackParam;
import com.bosi.sim.paas.open.server.domain.param.MemberRegisterParam;
import com.bosi.sim.paas.open.server.domain.param.MemberUpdatePwdParam;
import com.bosi.sim.paas.open.server.service.MmsMemberPartnerAuthService;
import com.bosi.sim.paas.open.server.service.MmsMemberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 会员管理Controller
 */
@RestController
@Api(tags = "MmsMemberController", value = "会员登录注册管理")
@RequestMapping("/mms/member")
@Valid
public class MmsMemberController {

    @Autowired
    private MmsMemberService memberService;

    @Autowired
    private MmsMemberPartnerAuthService mmsMemberPartnerAuthService;

    @ApiOperation("会员注册")
    @PostMapping("/register")
    public CommonResult register(@RequestBody MemberRegisterParam memberRegisterParam) {
        memberService.register(memberRegisterParam);
        return CommonResult.success();
    }

    @ApiOperation("会员登录")
    @PostMapping("/login")
    public CommonResult login(@Valid @RequestBody MemberLoginParam memberLoginParam) {
        String token = memberService.login(memberLoginParam.getUsername(), memberLoginParam.getPassword());
        if (token == null) {
            throw BizException.build(BizCode.MEMBER_LOGIN_ACCOUNT_OR_PASSWORD_ERROR);
        }
        Map<String, String> tokenMap = new HashMap<>();
        tokenMap.put("token", token);
        return CommonResult.success(tokenMap);
    }

    @ApiOperation("获取会员信息")
    @GetMapping("/get")
    public CommonResult info() {
        SdsMember member = memberService.getById(CurrentAuthorization.getUserId());
        if (member == null) {
            throw BizException.build(SystemCode.RESOURCE_NOTFOUND);
        }
        member.setPassword(null);
        return CommonResult.success(member);
    }

    @ApiOperation("获取验证码")
    @GetMapping("/getAuthCode")
    public CommonResult getAuthCode(@RequestParam String email, @RequestParam String key) {
        memberService.generateAuthCode(key, email);
        return CommonResult.success();
    }

    @ApiOperation("会员修改密码")
    @PostMapping("/updatePassword")
    public CommonResult updatePassword(@RequestBody MemberUpdatePwdParam memberUpdatePwdParam) {
        memberService.updatePassword(CurrentAuthorization.getUsername(), memberUpdatePwdParam.getPassword(), memberUpdatePwdParam.getAuthCode());
        return CommonResult.success();
    }


    @ApiOperation("刷新token")
    @GetMapping("/refreshToken")
    public CommonResult refreshToken() {
        memberService.refreshToken();
        return CommonResult.success();
    }

    @PostMapping("/partner/login/callback")
    public CommonResult partnerLoginCallback(@RequestBody MemberPartnerLoginCallbackParam memberPartnerLoginCallbackParam) {
        MemberParterAuthInfoDto authInfoDto;
        if (memberPartnerLoginCallbackParam.getSourceType().equals(SdsLoginSourceTypeEnum.GOOGLE.getSourceType())) {
            authInfoDto = mmsMemberPartnerAuthService.googleAuth(memberPartnerLoginCallbackParam.getCode(),
                    memberPartnerLoginCallbackParam.getRedirectUri());
        } else if (memberPartnerLoginCallbackParam.getSourceType().equals(SdsLoginSourceTypeEnum.GITHUB.getSourceType())) {
            authInfoDto = mmsMemberPartnerAuthService.githubAuth(memberPartnerLoginCallbackParam.getCode());
        } else if (memberPartnerLoginCallbackParam.getSourceType().equals(SdsLoginSourceTypeEnum.APPLE.getSourceType())) {
            authInfoDto = mmsMemberPartnerAuthService.appleAuth(memberPartnerLoginCallbackParam.getCode());
        } else {
            throw BizException.build(SystemCode.VALIDATE_FAILED);
        }
        if (authInfoDto == null) {
            throw BizException.build(SystemCode.ILLEGAL_OPERATION);
        }
        return CommonResult.success(memberService.loginOrRegisterForPartner(authInfoDto));
    }

}
