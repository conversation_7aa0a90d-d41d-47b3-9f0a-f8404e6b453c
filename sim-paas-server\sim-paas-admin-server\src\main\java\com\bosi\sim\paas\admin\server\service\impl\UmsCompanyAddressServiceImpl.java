package com.bosi.sim.paas.admin.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bosi.sim.paas.dao.mapper.ads.AdsCompanyAddressMapper;
import com.bosi.sim.paas.dao.model.ads.AdsCompanyAddress;
import com.bosi.sim.paas.admin.server.service.UmsCompanyAddressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 收货地址管理Service实现类
 */
@Service
public class UmsCompanyAddressServiceImpl implements UmsCompanyAddressService {
    @Autowired
    private AdsCompanyAddressMapper companyAddressMapper;

    @Override
    public List<AdsCompanyAddress> listAll() {
        return companyAddressMapper.selectList(new LambdaQueryWrapper<>());
    }
}
