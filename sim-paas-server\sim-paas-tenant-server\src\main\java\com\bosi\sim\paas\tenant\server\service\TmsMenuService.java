package com.bosi.sim.paas.tenant.server.service;

import com.bosi.sim.paas.dao.model.tds.TdsMenu;
import com.bosi.sim.paas.dao.model.tds.TdsUser;
import com.bosi.sim.paas.dao.pojo.vo.RouterVo;
import com.bosi.sim.paas.dao.pojo.vo.TreeSelectVo;

import java.util.List;
import java.util.Set;

/**
 * 菜单 业务层
 */
public interface TmsMenuService {
    /**
     * 根据用户查询系统菜单列表
     *
     * @return 菜单列表
     */
    List<TdsMenu> selectAll();

    Set<String> getMenuPermission(TdsUser user);

    Set<String> selectMenuPermsByUserId(String userId);

    /**
     * 根据用户ID查询菜单树信息
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<TdsMenu> selectMenuTreeByUserId(String userId);

    /**
     * 根据角色ID查询菜单树信息
     *
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    List<TdsMenu> selectMenuListByRoleId(String roleId);

    /**
     * 构建前端路由所需要的菜单
     *
     * @param menus 菜单列表
     * @return 路由列表
     */
    List<RouterVo> buildMenus(List<TdsMenu> menus);

    /**
     * 构建前端所需要树结构
     *
     * @param menus 菜单列表
     * @return 树结构列表
     */
    List<TdsMenu> buildMenuTree(List<TdsMenu> menus);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param menus 菜单列表
     * @return 下拉树结构列表
     */
    List<TreeSelectVo> buildMenuTreeSelect(List<TdsMenu> menus);

}
