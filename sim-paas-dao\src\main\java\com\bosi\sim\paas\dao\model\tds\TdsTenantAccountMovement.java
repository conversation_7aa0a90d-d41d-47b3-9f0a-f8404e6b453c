package com.bosi.sim.paas.dao.model.tms;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 账动对象
 */
@Data
@TableName("tms_tenant_account_movement")
public class TdsTenantAccountMovement extends BaseEntity {

    private Integer movementSource;

    private String accountId;

    private Integer movementType;

    private BigDecimal movementAmount;

}
