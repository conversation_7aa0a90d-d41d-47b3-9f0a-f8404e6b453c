package com.bosi.sim.paas.tenant.server.controller;

import com.alibaba.fastjson2.JSONObject;
import com.bosi.sim.paas.common.core.api.BizCode;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.exception.CommonResult;
import com.bosi.sim.paas.common.core.api.SystemCode;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.PageUtil;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.common.optlog.annotation.OperateLog;
import com.bosi.sim.paas.common.security.annotation.RequiresPermissions;
import com.bosi.sim.paas.common.security.auth.CurrentAuthorization;
import com.bosi.sim.paas.common.security.util.PasswordUtils;
import com.bosi.sim.paas.dao.model.tds.TdsRole;
import com.bosi.sim.paas.dao.model.tds.TdsUser;
import com.bosi.sim.paas.tenant.server.enums.DistributorTokenExtendKeyEnum;
import com.bosi.sim.paas.tenant.server.service.TmsRoleService;
import com.bosi.sim.paas.tenant.server.service.TmsUserService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户信息
 */
@RestController
@Api(tags = "TmsUserController", value = "用户管理")
@RequestMapping("/tms/user")
public class TmsUserController {
    @Autowired
    private TmsUserService userService;

    @Autowired
    private TmsRoleService roleService;

    /**
     * 获取用户列表
     */
    @RequiresPermissions("tms:user:page")
    @GetMapping("/page")
    public CommonResult page(
            @RequestParam(value = "pageNum") Integer pageNum,
            @RequestParam(value = "pageSize") Integer pageSize,
            TdsUser user) {
        user.setDistributorId(CurrentAuthorization.getStringFromExtendJson(DistributorTokenExtendKeyEnum.DISTRIBUTORID_KEY.getKey()));
        CommonPage<TdsUser> page = userService.page(PageUtil.buildPage(pageNum, pageSize, "whether_admin", false), user);
        return CommonResult.success(page);
    }

    /**
     * 根据用户编号获取详细信息
     */
    @RequiresPermissions("tms:user:query")
    @GetMapping({"/{userId}"})
    public CommonResult getInfo(@PathVariable(value = "userId", required = false) String userId) {
        JSONObject jsonObject = new JSONObject();
        TdsUser tmsUser = userService.selectUserById(userId);
        if (tmsUser == null) {
            throw BizException.build(SystemCode.RESOURCE_NOTFOUND);
        }
        TdsRole tmsRole = new TdsRole();
        tmsRole.setDistributorId(tmsUser.getDistributorId());
        tmsRole.setWhetherEnable(true);
        List<TdsRole> roles = roleService.list(tmsRole);
        jsonObject.put("roles", roles.stream().filter(r -> !r.getWhetherAdmin()).collect(Collectors.toList()));
        jsonObject.put("user", tmsUser);
        return CommonResult.success(jsonObject);
    }

    /**
     * 新增用户
     */
    @RequiresPermissions("tms:user:add")
    @OperateLog("用户新增")
    @PostMapping
    public CommonResult add(@Validated @RequestBody TdsUser user) {
        if (!userService.checkUserNameUnique(user.getId(), user.getUserName())) {
            throw BizException.build(BizCode.USER_ACCOUNT_ALREADY_EXISTED);
        } else if (StringUtils.isNotEmpty(user.getPhone()) && !userService.checkPhoneUnique(user.getId(), user.getPhone())) {
            throw BizException.build(BizCode.USER_PHONE_ALREADY_EXISTED);
        } else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user.getId(), user.getEmail())) {
            throw BizException.build(BizCode.USER_EMAIL_ALREADY_EXISTED);
        }
        user.setDistributorId(CurrentAuthorization.getStringFromExtendJson(DistributorTokenExtendKeyEnum.DISTRIBUTORID_KEY.getKey()));
        user.setPassword(PasswordUtils.encryptPassword(user.getPassword()));
        return CommonResult.success(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @RequiresPermissions("tms:user:edit")
    @OperateLog("用户修改")
    @PutMapping
    public CommonResult edit(@Validated @RequestBody TdsUser user) {
        userService.checkUserAllowed(user.getId());
        if (!userService.checkUserNameUnique(user.getId(), user.getUserName())) {
            throw BizException.build(BizCode.USER_ACCOUNT_ALREADY_EXISTED);
        } else if (StringUtils.isNotEmpty(user.getPhone()) && !userService.checkPhoneUnique(user.getId(), user.getPhone())) {
            throw BizException.build(BizCode.USER_PHONE_ALREADY_EXISTED);
        } else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user.getId(), user.getEmail())) {
            throw BizException.build(BizCode.USER_EMAIL_ALREADY_EXISTED);
        }
        return CommonResult.success(userService.updateUser(user));
    }

    /**
     * 删除用户
     */
    @RequiresPermissions("tms:user:remove")
    @OperateLog("用户删除")
    @DeleteMapping("/{userIds}")
    public CommonResult remove(@PathVariable String[] userIds) {
        return CommonResult.success(userService.deleteUserByIds(userIds));
    }

    /**
     * 重置密码
     */
    @RequiresPermissions("tms:user:edit")
    @OperateLog("重置密码")
    @PutMapping("/resetPwd")
    public CommonResult resetPwd(@RequestBody TdsUser user) {
        userService.checkUserAllowed(user.getId());
        user.setPassword(PasswordUtils.encryptPassword(user.getPassword()));
        return CommonResult.success(userService.resetPwd(user));
    }

    /**
     * 状态修改
     */
    @RequiresPermissions("tms:user:edit")
    @OperateLog("修改用户状态")
    @PutMapping("/changeStatus")
    public CommonResult changeStatus(@RequestBody TdsUser user) {
        userService.checkUserAllowed(user.getId());
        return CommonResult.success(userService.updateUserStatus(user));
    }
}
