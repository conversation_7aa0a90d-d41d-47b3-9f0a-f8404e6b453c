package com.bosi.sim.paas.tenant.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.dao.model.sds.SdsProduct;

public interface SdsProductService {
    /**
     * 分页查询商品
     */
    Page<SdsProduct> page(SdsProduct product, Integer pageNum, Integer pageSize);

    /**
     * 获取商品详情
     */
    SdsProduct getById(String id);

    /**
     * 创建商品
     */
    boolean create(SdsProduct product);

    /**
     * 更新商品
     */
    boolean update(SdsProduct product);

    /**
     * 删除商品
     */
    boolean delete(String id);
}