package com.bosi.sim.paas.admin.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.BizCode;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.common.core.api.SystemCode;
import com.bosi.sim.paas.common.core.exception.BizException;
import com.bosi.sim.paas.common.core.utils.IdUtils;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.dao.mapper.tms.TmsRoleMapper;
import com.bosi.sim.paas.dao.mapper.tms.TmsRoleMenuMapper;
import com.bosi.sim.paas.dao.mapper.tms.TmsUserRoleMapper;
import com.bosi.sim.paas.dao.model.tds.TdsRole;
import com.bosi.sim.paas.dao.model.tds.TdsRoleMenu;
import com.bosi.sim.paas.dao.model.tds.TdsUserRole;
import com.bosi.sim.paas.admin.server.dao.TmsRoleDao;
import com.bosi.sim.paas.admin.server.dao.TmsRoleMenuDao;
import com.bosi.sim.paas.admin.server.service.TmsRoleService;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * 角色 业务层处理
 */
@Service
public class TmsRoleServiceImpl implements TmsRoleService {

    @Autowired
    private TmsRoleMapper roleMapper;

    @Autowired
    private TmsRoleDao tmsRoleDao;

    @Autowired
    private TmsRoleMenuMapper roleMenuMapper;

    @Autowired
    private TmsRoleMenuDao roleMenuDao;

    @Autowired
    private TmsUserRoleMapper userRoleMapper;

    /**
     * 根据条件分页查询角色数据
     *
     * @param role 角色信息
     * @return 角色数据集合信息
     */
    @Override
    public CommonPage<TdsRole> page(Page<TdsRole> page, TdsRole role) {
        return CommonPage.restPage(tmsRoleDao.selectRoleList(page, role));
    }

    /**
     * 查询所有角色
     *
     * @return 角色列表
     */
    @Override
    public List<TdsRole> list(TdsRole role) {
        LambdaQueryWrapper<TdsRole> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotNull(role.getDistributorId())) {
            queryWrapper.like(TdsRole::getDistributorId, role.getDistributorId());
        }
        if (StringUtils.isNotNull(role.getWhetherEnable())) {
            queryWrapper.eq(TdsRole::getWhetherEnable, role.getWhetherEnable());
        }
        return roleMapper.selectList(queryWrapper);
    }

    /**
     * 通过角色ID查询角色
     *
     * @param roleId 角色ID
     * @return 角色对象信息
     */
    @Override
    public TdsRole selectRoleById(String roleId) {
        return roleMapper.selectById(roleId);
    }

    /**
     * 新增保存角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertRole(TdsRole role) {
        // 新增角色信息
        roleMapper.insert(role);
        return insertRoleMenu(role);
    }

    /**
     * 修改保存角色信息
     *
     * @param role 角色信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateRole(TdsRole role) {
        TdsRole tmsRole = roleMapper.selectById(role.getId());
        if (tmsRole == null) {
            throw BizException.build(SystemCode.RESOURCE_NOTFOUND);
        }
        if (tmsRole.getWhetherAdmin()) {
            throw BizException.build(SystemCode.FORBIDDEN_OPERATION);
        }
        // 修改角色信息
        roleMapper.updateById(role);
        // 删除角色与菜单关联
        LambdaQueryWrapper<TdsRoleMenu> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.eq(TdsRoleMenu::getRoleId, role.getId());
        roleMenuMapper.delete(deleteWrapper);
        return insertRoleMenu(role);
    }

    @Override
    public int updateRoleStatus(TdsRole role) {
        TdsRole tmsRole = roleMapper.selectById(role.getId());
        if (tmsRole == null) {
            throw BizException.build(SystemCode.RESOURCE_NOTFOUND);
        }
        if (tmsRole.getWhetherAdmin()) {
            throw BizException.build(SystemCode.FORBIDDEN_OPERATION);
        }
        return roleMapper.updateById(role);
    }

    /**
     * 新增角色菜单信息
     *
     * @param role 角色对象
     */
    public int insertRoleMenu(TdsRole role) {
        int rows = 1;
        // 新增用户与角色管理
        List<TdsRoleMenu> list = Lists.newArrayList();
        for (String menuId : role.getMenuIds()) {
            TdsRoleMenu rm = new TdsRoleMenu();
            rm.setId(IdUtils.fastSimpleUUID());
            rm.setRoleId(role.getId());
            rm.setMenuId(menuId);
            list.add(rm);
        }
        roleMenuDao.insertList(list);
        return rows;
    }


    /**
     * 批量删除角色信息
     *
     * @param roleIds 需要删除的角色ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteRoleByIds(String[] roleIds) {
        LambdaQueryWrapper<TdsRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TdsRole::getId, Arrays.asList(roleIds));
        List<TdsRole> roles = roleMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(roles)) {
            return roleIds.length;
        }
        for (TdsRole role : roles) {
            if (role.getWhetherAdmin()) {
                throw BizException.build(SystemCode.FORBIDDEN_OPERATION);
            }
            LambdaQueryWrapper<TdsUserRole> countWrapper = new LambdaQueryWrapper<>();
            countWrapper.eq(TdsUserRole::getRoleId, role.getId());
            if (userRoleMapper.selectCount(countWrapper) > 0L) {
                throw BizException.build(BizCode.ROLE_ALREADY_ALLOCATION);
            }
        }
        // 删除角色与菜单关联
        LambdaQueryWrapper<TdsRoleMenu> deleteWrapper = new LambdaQueryWrapper<>();
        deleteWrapper.in(TdsRoleMenu::getRoleId, Arrays.asList(roleIds));
        roleMenuMapper.delete(deleteWrapper);
        return roleMapper.deleteBatchIds(Arrays.asList(roleIds));
    }

    @Override
    public boolean checkRoleNameUnique(String roleId, String roleName, String distributorId) {
        String rid = StringUtils.isNull(roleId) ? "" : roleId;
        LambdaQueryWrapper<TdsRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TdsRole::getRoleName, roleName);
        queryWrapper.eq(TdsRole::getDistributorId, distributorId);
        TdsRole info = roleMapper.selectOne(queryWrapper);
        return StringUtils.isNull(info) || info.getId().equals(rid);
    }
}
