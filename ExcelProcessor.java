import java.io.*;
import java.util.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

public class ExcelProcessor {
    
    public static void main(String[] args) {
        try {
            processExcelFile();
        } catch (Exception e) {
            System.err.println("处理Excel文件时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public static void processExcelFile() throws IOException {
        String inputFile = "doc/test.xlsx";
        String outputFile = "doc/test1.xlsx";
        
        // 读取原始Excel文件
        FileInputStream fis = new FileInputStream(inputFile);
        Workbook workbook = new XSSFWorkbook(fis);
        Sheet sheet = workbook.getSheetAt(0);
        
        // 获取表头行
        Row headerRow = sheet.getRow(0);
        int mccmncColumnIndex = -1;
        int totalColumns = headerRow.getLastCellNum();
        
        // 查找MCCMNC列的索引
        for (int i = 0; i < totalColumns; i++) {
            Cell cell = headerRow.getCell(i);
            if (cell != null && "MCCMNC".equals(getCellValueAsString(cell))) {
                mccmncColumnIndex = i;
                break;
            }
        }
        
        if (mccmncColumnIndex == -1) {
            System.err.println("未找到MCCMNC列");
            workbook.close();
            fis.close();
            return;
        }
        
        System.out.println("找到MCCMNC列，索引: " + mccmncColumnIndex);
        System.out.println("原始数据行数: " + (sheet.getLastRowNum() + 1));
        
        // 创建新的工作簿
        Workbook newWorkbook = new XSSFWorkbook();
        Sheet newSheet = newWorkbook.createSheet("Sheet1");
        
        // 复制表头
        Row newHeaderRow = newSheet.createRow(0);
        for (int i = 0; i < totalColumns; i++) {
            Cell originalCell = headerRow.getCell(i);
            Cell newCell = newHeaderRow.createCell(i);
            if (originalCell != null) {
                newCell.setCellValue(getCellValueAsString(originalCell));
            }
        }
        
        int newRowIndex = 1;
        int splitCount = 0;
        
        // 处理数据行
        for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null) continue;
            
            Cell mccmncCell = row.getCell(mccmncColumnIndex);
            String mccmncValue = getCellValueAsString(mccmncCell);
            
            if (mccmncValue != null && mccmncValue.contains("/")) {
                // 分割包含'/'的数据
                String[] parts = mccmncValue.split("/");
                splitCount++;
                
                System.out.println("行 " + rowIndex + ": '" + mccmncValue + "' 分裂为 " + parts.length + " 行");
                
                // 为每个分割后的值创建新行
                for (String part : parts) {
                    Row newRow = newSheet.createRow(newRowIndex++);
                    
                    // 复制所有列的数据
                    for (int colIndex = 0; colIndex < totalColumns; colIndex++) {
                        Cell originalCell = row.getCell(colIndex);
                        Cell newCell = newRow.createCell(colIndex);
                        
                        if (colIndex == mccmncColumnIndex) {
                            // MCCMNC列使用分割后的值
                            newCell.setCellValue(part.trim());
                        } else if (originalCell != null) {
                            // 其他列复制原始值
                            String cellValue = getCellValueAsString(originalCell);
                            if (cellValue != null) {
                                newCell.setCellValue(cellValue);
                            }
                        }
                    }
                }
            } else {
                // 不包含'/'的行直接复制
                Row newRow = newSheet.createRow(newRowIndex++);
                
                for (int colIndex = 0; colIndex < totalColumns; colIndex++) {
                    Cell originalCell = row.getCell(colIndex);
                    Cell newCell = newRow.createCell(colIndex);
                    
                    if (originalCell != null) {
                        String cellValue = getCellValueAsString(originalCell);
                        if (cellValue != null) {
                            newCell.setCellValue(cellValue);
                        }
                    }
                }
            }
        }
        
        // 保存新文件
        FileOutputStream fos = new FileOutputStream(outputFile);
        newWorkbook.write(fos);
        
        System.out.println("处理完成!");
        System.out.println("分裂了 " + splitCount + " 行包含'/'的数据");
        System.out.println("处理后数据行数: " + newRowIndex);
        System.out.println("结果已保存到: " + outputFile);
        
        // 关闭资源
        fos.close();
        newWorkbook.close();
        workbook.close();
        fis.close();
    }
    
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 处理数字，避免科学计数法
                    double numValue = cell.getNumericCellValue();
                    if (numValue == (long) numValue) {
                        return String.valueOf((long) numValue);
                    } else {
                        return String.valueOf(numValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            case BLANK:
                return "";
            default:
                return "";
        }
    }
}
