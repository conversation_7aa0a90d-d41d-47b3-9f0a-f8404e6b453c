{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/components/layout/CachedViews.tsx"], "sourcesContent": ["'use client';\n\nimport { usePathname } from 'next/navigation';\nimport { ReactNode, useEffect, useState } from 'react';\n\ninterface CachedViewsProps {\n  children: ReactNode;\n  refreshFlag?: number; // 用于触发刷新\n}\n\ninterface CachedView {\n  key: string;\n  component: ReactNode;\n}\n\n/**\n * 页面缓存组件\n * 参考若依框架实现，缓存已访问的页面，避免重复渲染\n */\nexport default function CachedViews({ children, refreshFlag = 0 }: CachedViewsProps) {\n  const pathname = usePathname();\n  const [cachedViews, setCachedViews] = useState<CachedView[]>([]);\n  const [activeKey, setActiveKey] = useState(pathname);\n\n  // 当路径变化时更新缓存\n  useEffect(() => {\n    // 检查是否已缓存\n    const existingViewIndex = cachedViews.findIndex(view => view.key === pathname);\n    \n    if (existingViewIndex === -1) {\n      // 如果没有缓存，添加到缓存\n      setCachedViews(prev => [...prev, { key: pathname, component: children }]);\n    } else if (refreshFlag > 0) {\n      // 如果需要刷新，更新缓存\n      setCachedViews(prev => {\n        const newViews = [...prev];\n        newViews[existingViewIndex] = { key: pathname, component: children };\n        return newViews;\n      });\n    }\n    \n    // 更新当前活动的视图\n    setActiveKey(pathname);\n  }, [pathname, children, refreshFlag]);\n\n  return (\n    <div className=\"cached-views-container\">\n      {cachedViews.map(view => (\n        <div\n          key={view.key}\n          className=\"cached-view\"\n          style={{\n            display: view.key === activeKey ? 'block' : 'none',\n            transition: 'opacity 0.2s ease-in-out',\n          }}\n        >\n          {view.component}\n        </div>\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAmBe,SAAS,YAAY,EAAE,QAAQ,EAAE,cAAc,CAAC,EAAoB;;IACjF,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,aAAa;IACb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,UAAU;YACV,MAAM,oBAAoB,YAAY,SAAS;2DAAC,CAAA,OAAQ,KAAK,GAAG,KAAK;;YAErE,IAAI,sBAAsB,CAAC,GAAG;gBAC5B,eAAe;gBACf;6CAAe,CAAA,OAAQ;+BAAI;4BAAM;gCAAE,KAAK;gCAAU,WAAW;4BAAS;yBAAE;;YAC1E,OAAO,IAAI,cAAc,GAAG;gBAC1B,cAAc;gBACd;6CAAe,CAAA;wBACb,MAAM,WAAW;+BAAI;yBAAK;wBAC1B,QAAQ,CAAC,kBAAkB,GAAG;4BAAE,KAAK;4BAAU,WAAW;wBAAS;wBACnE,OAAO;oBACT;;YACF;YAEA,YAAY;YACZ,aAAa;QACf;gCAAG;QAAC;QAAU;QAAU;KAAY;IAEpC,qBACE,6LAAC;QAAI,WAAU;kBACZ,YAAY,GAAG,CAAC,CAAA,qBACf,6LAAC;gBAEC,WAAU;gBACV,OAAO;oBACL,SAAS,KAAK,GAAG,KAAK,YAAY,UAAU;oBAC5C,YAAY;gBACd;0BAEC,KAAK,SAAS;eAPV,KAAK,GAAG;;;;;;;;;;AAYvB;GA1CwB;;QACL,qIAAA,CAAA,cAAW;;;KADN", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/components/layout/TabsNav.tsx"], "sourcesContent": ["'use client';\n\nimport { Menu } from '@/types';\nimport { CloseOutlined, ReloadOutlined } from '@ant-design/icons';\nimport { Dropdown, Tabs, message } from 'antd';\nimport { usePathname, useRouter } from 'next/navigation';\nimport { useEffect, useState } from 'react';\n\ninterface TabsNavProps {\n  menus: Menu[];\n  refreshCurrentPage: () => void;\n}\n\ninterface TabItem {\n  key: string;\n  label: string;\n  closable: boolean;\n}\n\n// 查找菜单项\nconst findMenuByPath = (menus: Menu[], path: string): Menu | null => {\n  for (const menu of menus) {\n    if (menu.path === path) {\n      return menu;\n    }\n    if (menu.children) {\n      const found = findMenuByPath(menu.children, path);\n      if (found) return found;\n    }\n  }\n  return null;\n};\n\nexport default function TabsNav({ menus, refreshCurrentPage }: TabsNavProps) {\n  const router = useRouter();\n  const pathname = usePathname();\n  const [activeKey, setActiveKey] = useState(pathname);\n  const [tabs, setTabs] = useState<TabItem[]>([\n    { key: '/dashboard', label: '首页', closable: false }\n  ]);\n\n  // 初始化标签页\n  useEffect(() => {\n    // 如果当前路径不是首页，添加到标签页\n    if (pathname !== '/dashboard') {\n      addTab(pathname);\n    }\n    setActiveKey(pathname);\n  }, []);\n\n  // 当路由变化时更新标签页\n  useEffect(() => {\n    if (pathname !== activeKey) {\n      addTab(pathname);\n      setActiveKey(pathname);\n    }\n  }, [pathname]);\n\n  // 添加标签页\n  const addTab = (path: string) => {\n    // 查找菜单项\n    const menuItem = findMenuByPath(menus, path);\n    if (!menuItem) return;\n\n    // 检查标签页是否已存在\n    const exist = tabs.find(tab => tab.key === path);\n    if (!exist) {\n      setTabs(prev => [\n        ...prev,\n        { key: path, label: menuItem.name, closable: true }\n      ]);\n    }\n  };\n\n  // 关闭标签页\n  const removeTab = (targetKey: string) => {\n    // 不允许关闭首页\n    if (targetKey === '/dashboard') return;\n\n    // 找到要关闭的标签页的索引\n    const targetIndex = tabs.findIndex(tab => tab.key === targetKey);\n    \n    // 更新标签页列表\n    const newTabs = tabs.filter(tab => tab.key !== targetKey);\n    setTabs(newTabs);\n\n    // 如果关闭的是当前活动的标签页，需要激活其他标签页\n    if (targetKey === activeKey) {\n      // 默认激活左侧标签页\n      const newActiveKey = newTabs[targetIndex - 1]?.key || '/dashboard';\n      setActiveKey(newActiveKey);\n      router.push(newActiveKey);\n    }\n  };\n\n  // 处理标签页切换\n  const handleTabChange = (key: string) => {\n    setActiveKey(key);\n    router.push(key);\n  };\n\n  // 处理标签页编辑（关闭）\n  const handleTabEdit = (targetKey: string, action: 'add' | 'remove') => {\n    if (action === 'remove') {\n      removeTab(targetKey);\n    }\n  };\n\n  // 刷新当前页面\n  const handleRefresh = () => {\n    refreshCurrentPage();\n    message.success('页面已刷新');\n  };\n\n  // 关闭所有标签页\n  const closeAllTabs = () => {\n    setTabs([{ key: '/dashboard', label: '首页', closable: false }]);\n    setActiveKey('/dashboard');\n    router.push('/dashboard');\n  };\n\n  // 关闭其他标签页\n  const closeOtherTabs = () => {\n    const currentTab = tabs.find(tab => tab.key === activeKey);\n    if (!currentTab) return;\n\n    setTabs([\n      { key: '/dashboard', label: '首页', closable: false },\n      ...(activeKey !== '/dashboard' ? [currentTab] : [])\n    ]);\n  };\n\n  // 右键菜单项\n  const getDropdownItems = (tabKey: string) => [\n    {\n      key: 'refresh',\n      label: '刷新页面',\n      icon: <ReloadOutlined />,\n      onClick: () => {\n        if (tabKey === activeKey) {\n          handleRefresh();\n        } else {\n          setActiveKey(tabKey);\n          router.push(tabKey);\n          setTimeout(handleRefresh, 300);\n        }\n      },\n    },\n    {\n      key: 'close',\n      label: '关闭标签页',\n      icon: <CloseOutlined />,\n      disabled: tabKey === '/dashboard',\n      onClick: () => removeTab(tabKey),\n    },\n    {\n      key: 'closeOthers',\n      label: '关闭其他标签页',\n      onClick: closeOtherTabs,\n    },\n    {\n      key: 'closeAll',\n      label: '关闭所有标签页',\n      onClick: closeAllTabs,\n    },\n  ];\n\n  // 自定义标签页\n  const renderTabLabel = (tab: TabItem) => {\n    return (\n      <Dropdown\n        menu={{ items: getDropdownItems(tab.key) }}\n        trigger={['contextMenu']}\n      >\n        <span>{tab.label}</span>\n      </Dropdown>\n    );\n  };\n\n  return (\n    <Tabs\n      type=\"editable-card\"\n      activeKey={activeKey}\n      onChange={handleTabChange}\n      onEdit={handleTabEdit}\n      items={tabs.map(tab => ({\n        key: tab.key,\n        label: renderTabLabel(tab),\n        closable: tab.closable,\n      }))}\n      hideAdd\n      tabBarStyle={{ margin: 0, backgroundColor: '#fff', padding: '0 16px' }}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAAA;AAAA;AACA;AACA;;;AANA;;;;;AAmBA,QAAQ;AACR,MAAM,iBAAiB,CAAC,OAAe;IACrC,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,KAAK,IAAI,KAAK,MAAM;YACtB,OAAO;QACT;QACA,IAAI,KAAK,QAAQ,EAAE;YACjB,MAAM,QAAQ,eAAe,KAAK,QAAQ,EAAE;YAC5C,IAAI,OAAO,OAAO;QACpB;IACF;IACA,OAAO;AACT;AAEe,SAAS,QAAQ,EAAE,KAAK,EAAE,kBAAkB,EAAgB;;IACzE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QAC1C;YAAE,KAAK;YAAc,OAAO;YAAM,UAAU;QAAM;KACnD;IAED,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,oBAAoB;YACpB,IAAI,aAAa,cAAc;gBAC7B,OAAO;YACT;YACA,aAAa;QACf;4BAAG,EAAE;IAEL,cAAc;IACd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,aAAa,WAAW;gBAC1B,OAAO;gBACP,aAAa;YACf;QACF;4BAAG;QAAC;KAAS;IAEb,QAAQ;IACR,MAAM,SAAS,CAAC;QACd,QAAQ;QACR,MAAM,WAAW,eAAe,OAAO;QACvC,IAAI,CAAC,UAAU;QAEf,aAAa;QACb,MAAM,QAAQ,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK;QAC3C,IAAI,CAAC,OAAO;YACV,QAAQ,CAAA,OAAQ;uBACX;oBACH;wBAAE,KAAK;wBAAM,OAAO,SAAS,IAAI;wBAAE,UAAU;oBAAK;iBACnD;QACH;IACF;IAEA,QAAQ;IACR,MAAM,YAAY,CAAC;QACjB,UAAU;QACV,IAAI,cAAc,cAAc;QAEhC,eAAe;QACf,MAAM,cAAc,KAAK,SAAS,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK;QAEtD,UAAU;QACV,MAAM,UAAU,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK;QAC/C,QAAQ;QAER,2BAA2B;QAC3B,IAAI,cAAc,WAAW;YAC3B,YAAY;YACZ,MAAM,eAAe,OAAO,CAAC,cAAc,EAAE,EAAE,OAAO;YACtD,aAAa;YACb,OAAO,IAAI,CAAC;QACd;IACF;IAEA,UAAU;IACV,MAAM,kBAAkB,CAAC;QACvB,aAAa;QACb,OAAO,IAAI,CAAC;IACd;IAEA,cAAc;IACd,MAAM,gBAAgB,CAAC,WAAmB;QACxC,IAAI,WAAW,UAAU;YACvB,UAAU;QACZ;IACF;IAEA,SAAS;IACT,MAAM,gBAAgB;QACpB;QACA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;IAClB;IAEA,UAAU;IACV,MAAM,eAAe;QACnB,QAAQ;YAAC;gBAAE,KAAK;gBAAc,OAAO;gBAAM,UAAU;YAAM;SAAE;QAC7D,aAAa;QACb,OAAO,IAAI,CAAC;IACd;IAEA,UAAU;IACV,MAAM,iBAAiB;QACrB,MAAM,aAAa,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK;QAChD,IAAI,CAAC,YAAY;QAEjB,QAAQ;YACN;gBAAE,KAAK;gBAAc,OAAO;gBAAM,UAAU;YAAM;eAC9C,cAAc,eAAe;gBAAC;aAAW,GAAG,EAAE;SACnD;IACH;IAEA,QAAQ;IACR,MAAM,mBAAmB,CAAC,SAAmB;YAC3C;gBACE,KAAK;gBACL,OAAO;gBACP,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;gBACrB,SAAS;oBACP,IAAI,WAAW,WAAW;wBACxB;oBACF,OAAO;wBACL,aAAa;wBACb,OAAO,IAAI,CAAC;wBACZ,WAAW,eAAe;oBAC5B;gBACF;YACF;YACA;gBACE,KAAK;gBACL,OAAO;gBACP,oBAAM,6LAAC,uNAAA,CAAA,gBAAa;;;;;gBACpB,UAAU,WAAW;gBACrB,SAAS,IAAM,UAAU;YAC3B;YACA;gBACE,KAAK;gBACL,OAAO;gBACP,SAAS;YACX;YACA;gBACE,KAAK;gBACL,OAAO;gBACP,SAAS;YACX;SACD;IAED,SAAS;IACT,MAAM,iBAAiB,CAAC;QACtB,qBACE,6LAAC,yLAAA,CAAA,WAAQ;YACP,MAAM;gBAAE,OAAO,iBAAiB,IAAI,GAAG;YAAE;YACzC,SAAS;gBAAC;aAAc;sBAExB,cAAA,6LAAC;0BAAM,IAAI,KAAK;;;;;;;;;;;IAGtB;IAEA,qBACE,6LAAC,iLAAA,CAAA,OAAI;QACH,MAAK;QACL,WAAW;QACX,UAAU;QACV,QAAQ;QACR,OAAO,KAAK,GAAG,CAAC,CAAA,MAAO,CAAC;gBACtB,KAAK,IAAI,GAAG;gBACZ,OAAO,eAAe;gBACtB,UAAU,IAAI,QAAQ;YACxB,CAAC;QACD,OAAO;QACP,aAAa;YAAE,QAAQ;YAAG,iBAAiB;YAAQ,SAAS;QAAS;;;;;;AAG3E;GAjKwB;;QACP,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAFN", "debugId": null}}, {"offset": {"line": 347, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/components/layout/AdminLayout.tsx"], "sourcesContent": ["'use client';\n\nimport CachedViews from '@/components/layout/CachedViews';\nimport TabsNav from '@/components/layout/TabsNav';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { menuService } from '@/services';\nimport { Menu } from '@/types';\nimport {\n  DashboardOutlined,\n  LogoutOutlined,\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  SettingOutlined,\n  TeamOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport { Avatar, Breadcrumb, Dropdown, Layout, Menu as AntMenu, Spin, theme } from 'antd';\nimport Link from 'next/link';\nimport { usePathname, useRouter } from 'next/navigation';\nimport { useEffect, useState } from 'react';\n\nconst { Header, Sider, Content } = Layout;\n\n// 图标映射\nconst iconMap: Record<string, React.ReactNode> = {\n  dashboard: <DashboardOutlined />,\n  setting: <SettingOutlined />,\n  user: <UserOutlined />,\n  team: <TeamOutlined />,\n  menu: <MenuOutlined />,\n};\n\n// 自定义 MenuOutlined 组件\nfunction MenuOutlined() {\n  return <span className=\"anticon\">\n    <svg viewBox=\"64 64 896 896\" focusable=\"false\" data-icon=\"menu\" width=\"1em\" height=\"1em\" fill=\"currentColor\" aria-hidden=\"true\">\n      <path d=\"M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z\"></path>\n    </svg>\n  </span>;\n}\n\n// 将菜单数据转换为 Ant Design Menu 组件所需的格式\nconst convertMenuItems = (menus: Menu[]) => {\n  return menus.map(menu => {\n    const item = {\n      key: menu.path,\n      icon: menu.icon ? iconMap[menu.icon] || <MenuOutlined /> : null,\n      label: menu.children && menu.children.length > 0\n        ? menu.name\n        : <Link href={menu.path} style={{ color: 'inherit', textDecoration: 'none' }}>{menu.name}</Link>,\n      children: menu.children && menu.children.length > 0 ? convertMenuItems(menu.children) : undefined,\n    };\n    return item;\n  });\n};\n\n// 获取当前路径的面包屑\nconst getBreadcrumbItems = (pathname: string, menus: Menu[]) => {\n  const paths = pathname.split('/').filter(Boolean);\n  const breadcrumbItems = [{\n    title: <Link href=\"/dashboard\" style={{ color: 'inherit', textDecoration: 'none' }}>首页</Link>,\n  }];\n\n  let currentPath = '';\n  let currentMenus = menus;\n\n  for (const path of paths) {\n    currentPath += `/${path}`;\n\n    // 在当前菜单层级中查找匹配的菜单\n    const findMenu = (menus: Menu[], path: string): Menu | undefined => {\n      for (const menu of menus) {\n        if (menu.path === path) {\n          return menu;\n        }\n        if (menu.children) {\n          const found = findMenu(menu.children, path);\n          if (found) {\n            return found;\n          }\n        }\n      }\n      return undefined;\n    };\n\n    const menu = findMenu(currentMenus, currentPath);\n    if (menu) {\n      breadcrumbItems.push({\n        title: <Link href={menu.path} style={{ color: 'inherit', textDecoration: 'none' }}>{menu.name}</Link>,\n      });\n      if (menu.children) {\n        currentMenus = menu.children;\n      }\n    }\n  }\n\n  return breadcrumbItems;\n};\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n}\n\nconst AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {\n  const { user, logout } = useAuth();\n  const router = useRouter();\n  const pathname = usePathname();\n  const [collapsed, setCollapsed] = useState(false);\n  const [menuItems, setMenuItems] = useState<any[]>([]);\n  const [menuTree, setMenuTree] = useState<Menu[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [refreshFlag, setRefreshFlag] = useState(0); // 用于触发页面刷新\n\n  const {\n    token: { colorBgContainer, borderRadiusLG },\n  } = theme.useToken();\n\n  // 获取菜单数据\n  useEffect(() => {\n    const fetchMenus = async () => {\n      try {\n        setLoading(true);\n        const menuTree = await menuService.getMenuTree();\n        setMenuTree(menuTree);\n        setMenuItems(convertMenuItems(menuTree));\n      } catch (error) {\n        console.error('获取菜单失败:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchMenus();\n  }, []);\n\n  // 处理菜单点击 - 参考若依框架实现\n  const handleMenuClick = ({ key }: { key: string }) => {\n    // 如果点击的是当前路径，不进行跳转\n    if (pathname === key) return;\n\n    // 使用 router.push 进行客户端导航\n    router.push(key);\n\n    // 阻止默认行为，防止页面刷新\n    // 注意：这是模拟若依框架的行为，实际上 Next.js 的 router.push 已经是客户端导航\n  };\n\n  // 处理登出\n  const handleLogout = async () => {\n    await logout();\n    router.push('/login');\n  };\n\n  // 刷新当前页面\n  const refreshCurrentPage = () => {\n    setRefreshFlag(prev => prev + 1);\n  };\n\n  // 用户下拉菜单\n  const userMenuItems = [\n    {\n      key: 'logout',\n      icon: <LogoutOutlined />,\n      label: '退出登录',\n      onClick: handleLogout,\n    },\n  ];\n\n  // 如果正在加载，显示加载中\n  if (loading) {\n    return (\n      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>\n        <Spin size=\"large\" />\n      </div>\n    );\n  }\n\n  return (\n    <Layout style={{ minHeight: '100vh' }}>\n      <Sider trigger={null} collapsible collapsed={collapsed} theme=\"light\">\n        <div style={{ height: 64, display: 'flex', alignItems: 'center', justifyContent: 'center', borderBottom: '1px solid #f0f0f0' }}>\n          <h1 style={{ margin: 0, fontSize: collapsed ? 16 : 20, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>\n            {collapsed ? 'IOT' : 'IOT Admin'}\n          </h1>\n        </div>\n        <AntMenu\n          mode=\"inline\"\n          selectedKeys={[pathname]}\n          defaultOpenKeys={['/system']}\n          style={{ height: '100%', borderRight: 0 }}\n          items={menuItems}\n          onClick={handleMenuClick}\n        />\n      </Sider>\n      <Layout>\n        <Header style={{ padding: 0, background: colorBgContainer, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n          <div style={{ paddingLeft: 16 }}>\n            {collapsed ? (\n              <MenuUnfoldOutlined onClick={() => setCollapsed(false)} style={{ fontSize: 18 }} />\n            ) : (\n              <MenuFoldOutlined onClick={() => setCollapsed(true)} style={{ fontSize: 18 }} />\n            )}\n          </div>\n          <div style={{ paddingRight: 24 }}>\n            <Dropdown menu={{ items: userMenuItems }} placement=\"bottomRight\">\n              <div style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}>\n                <Avatar src={user?.avatar} style={{ marginRight: 8 }} />\n                <span>{user?.name}</span>\n              </div>\n            </Dropdown>\n          </div>\n        </Header>\n        <Content style={{ margin: '0', display: 'flex', flexDirection: 'column', height: 'calc(100vh - 64px)' }}>\n          {/* 标签页导航 */}\n          <div className=\"tabs-nav-container\" style={{ background: '#fff', borderBottom: '1px solid #f0f0f0' }}>\n            <TabsNav menus={menuTree} refreshCurrentPage={refreshCurrentPage} />\n          </div>\n\n          {/* 内容区域 */}\n          <div\n            style={{\n              padding: 16,\n              flex: 1,\n              overflow: 'auto',\n              background: '#f0f2f5',\n            }}\n          >\n            <div\n              style={{\n                padding: 24,\n                minHeight: 360,\n                background: colorBgContainer,\n                borderRadius: borderRadiusLG,\n              }}\n            >\n              <CachedViews refreshFlag={refreshFlag}>\n                {children}\n              </CachedViews>\n            </div>\n          </div>\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\nexport default AdminLayout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAnBA;;;;;;;;;;AAqBA,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,qLAAA,CAAA,SAAM;AAEzC,OAAO;AACP,MAAM,UAA2C;IAC/C,yBAAW,6LAAC,+NAAA,CAAA,oBAAiB;;;;;IAC7B,uBAAS,6LAAC,2NAAA,CAAA,kBAAe;;;;;IACzB,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;IACnB,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;IACnB,oBAAM,6LAAC;;;;;AACT;AAEA,sBAAsB;AACtB,SAAS;IACP,qBAAO,6LAAC;QAAK,WAAU;kBACrB,cAAA,6LAAC;YAAI,SAAQ;YAAgB,WAAU;YAAQ,aAAU;YAAO,OAAM;YAAM,QAAO;YAAM,MAAK;YAAe,eAAY;sBACvH,cAAA,6LAAC;gBAAK,GAAE;;;;;;;;;;;;;;;;AAGd;KANS;AAQT,mCAAmC;AACnC,MAAM,mBAAmB,CAAC;IACxB,OAAO,MAAM,GAAG,CAAC,CAAA;QACf,MAAM,OAAO;YACX,KAAK,KAAK,IAAI;YACd,MAAM,KAAK,IAAI,GAAG,OAAO,CAAC,KAAK,IAAI,CAAC,kBAAI,6LAAC;;;;uBAAkB;YAC3D,OAAO,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,IAC3C,KAAK,IAAI,iBACT,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAM,KAAK,IAAI;gBAAE,OAAO;oBAAE,OAAO;oBAAW,gBAAgB;gBAAO;0BAAI,KAAK,IAAI;;;;;;YAC1F,UAAU,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,IAAI,iBAAiB,KAAK,QAAQ,IAAI;QAC1F;QACA,OAAO;IACT;AACF;AAEA,aAAa;AACb,MAAM,qBAAqB,CAAC,UAAkB;IAC5C,MAAM,QAAQ,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;IACzC,MAAM,kBAAkB;QAAC;YACvB,qBAAO,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAK;gBAAa,OAAO;oBAAE,OAAO;oBAAW,gBAAgB;gBAAO;0BAAG;;;;;;QACtF;KAAE;IAEF,IAAI,cAAc;IAClB,IAAI,eAAe;IAEnB,KAAK,MAAM,QAAQ,MAAO;QACxB,eAAe,CAAC,CAAC,EAAE,MAAM;QAEzB,kBAAkB;QAClB,MAAM,WAAW,CAAC,OAAe;YAC/B,KAAK,MAAM,QAAQ,MAAO;gBACxB,IAAI,KAAK,IAAI,KAAK,MAAM;oBACtB,OAAO;gBACT;gBACA,IAAI,KAAK,QAAQ,EAAE;oBACjB,MAAM,QAAQ,SAAS,KAAK,QAAQ,EAAE;oBACtC,IAAI,OAAO;wBACT,OAAO;oBACT;gBACF;YACF;YACA,OAAO;QACT;QAEA,MAAM,OAAO,SAAS,cAAc;QACpC,IAAI,MAAM;YACR,gBAAgB,IAAI,CAAC;gBACnB,qBAAO,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAM,KAAK,IAAI;oBAAE,OAAO;wBAAE,OAAO;wBAAW,gBAAgB;oBAAO;8BAAI,KAAK,IAAI;;;;;;YAC/F;YACA,IAAI,KAAK,QAAQ,EAAE;gBACjB,eAAe,KAAK,QAAQ;YAC9B;QACF;IACF;IAEA,OAAO;AACT;AAMA,MAAM,cAA0C,CAAC,EAAE,QAAQ,EAAE;;IAC3D,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACpD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,WAAW;IAE9D,MAAM,EACJ,OAAO,EAAE,gBAAgB,EAAE,cAAc,EAAE,EAC5C,GAAG,mLAAA,CAAA,QAAK,CAAC,QAAQ;IAElB,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;oDAAa;oBACjB,IAAI;wBACF,WAAW;wBACX,MAAM,WAAW,MAAM,qIAAA,CAAA,cAAW,CAAC,WAAW;wBAC9C,YAAY;wBACZ,aAAa,iBAAiB;oBAChC,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;oBAC3B,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;gCAAG,EAAE;IAEL,oBAAoB;IACpB,MAAM,kBAAkB,CAAC,EAAE,GAAG,EAAmB;QAC/C,mBAAmB;QACnB,IAAI,aAAa,KAAK;QAEtB,yBAAyB;QACzB,OAAO,IAAI,CAAC;IAEZ,gBAAgB;IAChB,oDAAoD;IACtD;IAEA,OAAO;IACP,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,SAAS;IACT,MAAM,qBAAqB;QACzB,eAAe,CAAA,OAAQ,OAAO;IAChC;IAEA,SAAS;IACT,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;YACrB,OAAO;YACP,SAAS;QACX;KACD;IAED,eAAe;IACf,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,OAAO;gBAAE,SAAS;gBAAQ,gBAAgB;gBAAU,YAAY;gBAAU,QAAQ;YAAQ;sBAC7F,cAAA,6LAAC,iLAAA,CAAA,OAAI;gBAAC,MAAK;;;;;;;;;;;IAGjB;IAEA,qBACE,6LAAC,qLAAA,CAAA,SAAM;QAAC,OAAO;YAAE,WAAW;QAAQ;;0BAClC,6LAAC;gBAAM,SAAS;gBAAM,WAAW;gBAAC,WAAW;gBAAW,OAAM;;kCAC5D,6LAAC;wBAAI,OAAO;4BAAE,QAAQ;4BAAI,SAAS;4BAAQ,YAAY;4BAAU,gBAAgB;4BAAU,cAAc;wBAAoB;kCAC3H,cAAA,6LAAC;4BAAG,OAAO;gCAAE,QAAQ;gCAAG,UAAU,YAAY,KAAK;gCAAI,YAAY;gCAAU,UAAU;gCAAU,cAAc;4BAAW;sCACvH,YAAY,QAAQ;;;;;;;;;;;kCAGzB,6LAAC,iLAAA,CAAA,OAAO;wBACN,MAAK;wBACL,cAAc;4BAAC;yBAAS;wBACxB,iBAAiB;4BAAC;yBAAU;wBAC5B,OAAO;4BAAE,QAAQ;4BAAQ,aAAa;wBAAE;wBACxC,OAAO;wBACP,SAAS;;;;;;;;;;;;0BAGb,6LAAC,qLAAA,CAAA,SAAM;;kCACL,6LAAC;wBAAO,OAAO;4BAAE,SAAS;4BAAG,YAAY;4BAAkB,SAAS;4BAAQ,YAAY;4BAAU,gBAAgB;wBAAgB;;0CAChI,6LAAC;gCAAI,OAAO;oCAAE,aAAa;gCAAG;0CAC3B,0BACC,6LAAC,iOAAA,CAAA,qBAAkB;oCAAC,SAAS,IAAM,aAAa;oCAAQ,OAAO;wCAAE,UAAU;oCAAG;;;;;yDAE9E,6LAAC,6NAAA,CAAA,mBAAgB;oCAAC,SAAS,IAAM,aAAa;oCAAO,OAAO;wCAAE,UAAU;oCAAG;;;;;;;;;;;0CAG/E,6LAAC;gCAAI,OAAO;oCAAE,cAAc;gCAAG;0CAC7B,cAAA,6LAAC,yLAAA,CAAA,WAAQ;oCAAC,MAAM;wCAAE,OAAO;oCAAc;oCAAG,WAAU;8CAClD,cAAA,6LAAC;wCAAI,OAAO;4CAAE,QAAQ;4CAAW,SAAS;4CAAQ,YAAY;wCAAS;;0DACrE,6LAAC,qLAAA,CAAA,SAAM;gDAAC,KAAK,MAAM;gDAAQ,OAAO;oDAAE,aAAa;gDAAE;;;;;;0DACnD,6LAAC;0DAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAKrB,6LAAC;wBAAQ,OAAO;4BAAE,QAAQ;4BAAK,SAAS;4BAAQ,eAAe;4BAAU,QAAQ;wBAAqB;;0CAEpG,6LAAC;gCAAI,WAAU;gCAAqB,OAAO;oCAAE,YAAY;oCAAQ,cAAc;gCAAoB;0CACjG,cAAA,6LAAC,0IAAA,CAAA,UAAO;oCAAC,OAAO;oCAAU,oBAAoB;;;;;;;;;;;0CAIhD,6LAAC;gCACC,OAAO;oCACL,SAAS;oCACT,MAAM;oCACN,UAAU;oCACV,YAAY;gCACd;0CAEA,cAAA,6LAAC;oCACC,OAAO;wCACL,SAAS;wCACT,WAAW;wCACX,YAAY;wCACZ,cAAc;oCAChB;8CAEA,cAAA,6LAAC,8IAAA,CAAA,UAAW;wCAAC,aAAa;kDACvB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB;GA7IM;;QACqB,kIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QASxB,mLAAA,CAAA,QAAK,CAAC;;;MAZN;uCA+IS", "debugId": null}}, {"offset": {"line": 873, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/code/bosi/iot-admin/src/app/system/menus/page.tsx"], "sourcesContent": ["'use client';\n\nimport AdminLayout from '@/components/layout/AdminLayout';\nimport { menuService } from '@/services';\nimport { Menu, MenuCreateInput, MenuUpdateInput } from '@/types';\nimport {\n  DashboardOutlined,\n  DeleteOutlined,\n  EditOutlined,\n  MenuOutlined,\n  PlusOutlined,\n  SearchOutlined,\n  SettingOutlined,\n  TeamOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport { Button, Form, Input, InputNumber, Modal, Popconfirm, Select, Space, Table, Tag, message } from 'antd';\nimport { ColumnsType } from 'antd/es/table';\nimport { useEffect, useState } from 'react';\n\n// 图标选项\nconst iconOptions = [\n  { label: '仪表盘', value: 'dashboard', icon: <DashboardOutlined /> },\n  { label: '设置', value: 'setting', icon: <SettingOutlined /> },\n  { label: '用户', value: 'user', icon: <UserOutlined /> },\n  { label: '团队', value: 'team', icon: <TeamOutlined /> },\n  { label: '菜单', value: 'menu', icon: <MenuOutlined /> },\n];\n\nexport default function MenusPage() {\n  const [menus, setMenus] = useState<Menu[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize, setPageSize] = useState(10);\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isEditing, setIsEditing] = useState(false);\n  const [currentMenu, setCurrentMenu] = useState<Menu | null>(null);\n  const [form] = Form.useForm();\n\n  // 获取菜单列表\n  const fetchMenus = async () => {\n    try {\n      setLoading(true);\n      const response = await menuService.getMenus({ page: currentPage, pageSize });\n      setMenus(response.data);\n      setTotal(response.total);\n    } catch (error) {\n      console.error('获取菜单列表失败:', error);\n      message.error('获取菜单列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 初始化\n  useEffect(() => {\n    fetchMenus();\n  }, [currentPage, pageSize]);\n\n  // 处理分页变化\n  const handleTableChange = (pagination: any) => {\n    setCurrentPage(pagination.current);\n    setPageSize(pagination.pageSize);\n  };\n\n  // 打开创建菜单模态框\n  const showCreateModal = () => {\n    setIsEditing(false);\n    setCurrentMenu(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  // 打开编辑菜单模态框\n  const showEditModal = (menu: Menu) => {\n    setIsEditing(true);\n    setCurrentMenu(menu);\n    form.setFieldsValue({\n      ...menu,\n    });\n    setIsModalVisible(true);\n  };\n\n  // 关闭模态框\n  const handleCancel = () => {\n    setIsModalVisible(false);\n    form.resetFields();\n  };\n\n  // 处理表单提交\n  const handleSubmit = async () => {\n    try {\n      const values = await form.validateFields();\n      \n      if (isEditing && currentMenu) {\n        // 更新菜单\n        await menuService.updateMenu({\n          id: currentMenu.id,\n          ...values,\n        } as MenuUpdateInput);\n        message.success('菜单更新成功');\n      } else {\n        // 创建菜单\n        await menuService.createMenu(values as MenuCreateInput);\n        message.success('菜单创建成功');\n      }\n      \n      setIsModalVisible(false);\n      fetchMenus();\n    } catch (error) {\n      console.error('提交表单失败:', error);\n      message.error('操作失败，请重试');\n    }\n  };\n\n  // 处理删除菜单\n  const handleDelete = async (id: string) => {\n    try {\n      await menuService.deleteMenu(id);\n      message.success('菜单删除成功');\n      fetchMenus();\n    } catch (error) {\n      console.error('删除菜单失败:', error);\n      message.error('删除菜单失败');\n    }\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<Menu> = [\n    {\n      title: '菜单名称',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text, record) => (\n        <Space>\n          {record.icon && (\n            <span className=\"anticon\">\n              {iconOptions.find(option => option.value === record.icon)?.icon}\n            </span>\n          )}\n          {text}\n        </Space>\n      ),\n    },\n    {\n      title: '路径',\n      dataIndex: 'path',\n      key: 'path',\n    },\n    {\n      title: '上级菜单',\n      dataIndex: 'parentId',\n      key: 'parentId',\n      render: (parentId: string | null) => {\n        if (!parentId) return <Tag color=\"green\">根菜单</Tag>;\n        const parentMenu = menus.find(menu => menu.id === parentId);\n        return parentMenu ? parentMenu.name : '-';\n      },\n    },\n    {\n      title: '排序',\n      dataIndex: 'order',\n      key: 'order',\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space size=\"middle\">\n          <Button \n            type=\"text\" \n            icon={<EditOutlined />} \n            onClick={() => showEditModal(record)}\n          />\n          <Popconfirm\n            title=\"确定要删除此菜单吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button \n              type=\"text\" \n              danger \n              icon={<DeleteOutlined />} \n            />\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <AdminLayout>\n      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>\n        <h1>菜单管理</h1>\n        <Button type=\"primary\" icon={<PlusOutlined />} onClick={showCreateModal}>\n          新增菜单\n        </Button>\n      </div>\n      \n      <div style={{ marginBottom: 16 }}>\n        <Input.Search\n          placeholder=\"搜索菜单\"\n          allowClear\n          enterButton={<SearchOutlined />}\n          onSearch={(value) => console.log(value)}\n          style={{ width: 300 }}\n        />\n      </div>\n      \n      <Table\n        columns={columns}\n        dataSource={menus}\n        rowKey=\"id\"\n        loading={loading}\n        pagination={{\n          current: currentPage,\n          pageSize: pageSize,\n          total: total,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total) => `共 ${total} 条记录`,\n        }}\n        onChange={handleTableChange}\n      />\n      \n      <Modal\n        title={isEditing ? '编辑菜单' : '新增菜单'}\n        open={isModalVisible}\n        onOk={handleSubmit}\n        onCancel={handleCancel}\n        maskClosable={false}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"菜单名称\"\n            rules={[{ required: true, message: '请输入菜单名称' }]}\n          >\n            <Input placeholder=\"请输入菜单名称\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"path\"\n            label=\"路径\"\n            rules={[{ required: true, message: '请输入路径' }]}\n          >\n            <Input placeholder=\"请输入路径，例如：/system/users\" />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"icon\"\n            label=\"图标\"\n          >\n            <Select\n              placeholder=\"请选择图标\"\n              allowClear\n              options={iconOptions}\n              optionLabelProp=\"label\"\n              optionRender={(option) => (\n                <Space>\n                  {option.data.icon}\n                  {option.data.label}\n                </Space>\n              )}\n            />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"parentId\"\n            label=\"上级菜单\"\n          >\n            <Select\n              placeholder=\"请选择上级菜单\"\n              allowClear\n              options={menus\n                .filter(menu => !menu.parentId) // 只显示根菜单作为父菜单选项\n                .map(menu => ({ label: menu.name, value: menu.id }))}\n            />\n          </Form.Item>\n          \n          <Form.Item\n            name=\"order\"\n            label=\"排序\"\n            initialValue={1}\n          >\n            <InputNumber min={1} placeholder=\"请输入排序\" style={{ width: '100%' }} />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </AdminLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;;;AAlBA;;;;;;AAoBA,OAAO;AACP,MAAM,cAAc;IAClB;QAAE,OAAO;QAAO,OAAO;QAAa,oBAAM,6LAAC,+NAAA,CAAA,oBAAiB;;;;;IAAI;IAChE;QAAE,OAAO;QAAM,OAAO;QAAW,oBAAM,6LAAC,2NAAA,CAAA,kBAAe;;;;;IAAI;IAC3D;QAAE,OAAO;QAAM,OAAO;QAAQ,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;IAAI;IACrD;QAAE,OAAO;QAAM,OAAO;QAAQ,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;IAAI;IACrD;QAAE,OAAO;QAAM,OAAO;QAAQ,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;IAAI;CACtD;AAEc,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC5D,MAAM,CAAC,KAAK,GAAG,iLAAA,CAAA,OAAI,CAAC,OAAO;IAE3B,SAAS;IACT,MAAM,aAAa;QACjB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,qIAAA,CAAA,cAAW,CAAC,QAAQ,CAAC;gBAAE,MAAM;gBAAa;YAAS;YAC1E,SAAS,SAAS,IAAI;YACtB,SAAS,SAAS,KAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM;IACN,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG;QAAC;QAAa;KAAS;IAE1B,SAAS;IACT,MAAM,oBAAoB,CAAC;QACzB,eAAe,WAAW,OAAO;QACjC,YAAY,WAAW,QAAQ;IACjC;IAEA,YAAY;IACZ,MAAM,kBAAkB;QACtB,aAAa;QACb,eAAe;QACf,KAAK,WAAW;QAChB,kBAAkB;IACpB;IAEA,YAAY;IACZ,MAAM,gBAAgB,CAAC;QACrB,aAAa;QACb,eAAe;QACf,KAAK,cAAc,CAAC;YAClB,GAAG,IAAI;QACT;QACA,kBAAkB;IACpB;IAEA,QAAQ;IACR,MAAM,eAAe;QACnB,kBAAkB;QAClB,KAAK,WAAW;IAClB;IAEA,SAAS;IACT,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,SAAS,MAAM,KAAK,cAAc;YAExC,IAAI,aAAa,aAAa;gBAC5B,OAAO;gBACP,MAAM,qIAAA,CAAA,cAAW,CAAC,UAAU,CAAC;oBAC3B,IAAI,YAAY,EAAE;oBAClB,GAAG,MAAM;gBACX;gBACA,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB,OAAO;gBACL,OAAO;gBACP,MAAM,qIAAA,CAAA,cAAW,CAAC,UAAU,CAAC;gBAC7B,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAClB;YAEA,kBAAkB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,SAAS;IACT,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,qIAAA,CAAA,cAAW,CAAC,UAAU,CAAC;YAC7B,uLAAA,CAAA,UAAO,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;YACzB,uLAAA,CAAA,UAAO,CAAC,KAAK,CAAC;QAChB;IACF;IAEA,QAAQ;IACR,MAAM,UAA6B;QACjC;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC,MAAM,uBACb,6LAAC,mMAAA,CAAA,QAAK;;wBACH,OAAO,IAAI,kBACV,6LAAC;4BAAK,WAAU;sCACb,YAAY,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK,OAAO,IAAI,GAAG;;;;;;wBAG9D;;;;;;;QAGP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;YACL,QAAQ,CAAC;gBACP,IAAI,CAAC,UAAU,qBAAO,6LAAC,+KAAA,CAAA,MAAG;oBAAC,OAAM;8BAAQ;;;;;;gBACzC,MAAM,aAAa,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;gBAClD,OAAO,aAAa,WAAW,IAAI,GAAG;YACxC;QACF;QACA;YACE,OAAO;YACP,WAAW;YACX,KAAK;QACP;QACA;YACE,OAAO;YACP,KAAK;YACL,QAAQ,CAAC,GAAG,uBACV,6LAAC,mMAAA,CAAA,QAAK;oBAAC,MAAK;;sCACV,6LAAC,qMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;4BACnB,SAAS,IAAM,cAAc;;;;;;sCAE/B,6LAAC,6LAAA,CAAA,aAAU;4BACT,OAAM;4BACN,WAAW,IAAM,aAAa,OAAO,EAAE;4BACvC,QAAO;4BACP,YAAW;sCAEX,cAAA,6LAAC,qMAAA,CAAA,SAAM;gCACL,MAAK;gCACL,MAAM;gCACN,oBAAM,6LAAC,yNAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;QAK/B;KACD;IAED,qBACE,6LAAC,8IAAA,CAAA,UAAW;;0BACV,6LAAC;gBAAI,OAAO;oBAAE,SAAS;oBAAQ,gBAAgB;oBAAiB,cAAc;gBAAG;;kCAC/E,6LAAC;kCAAG;;;;;;kCACJ,6LAAC,qMAAA,CAAA,SAAM;wBAAC,MAAK;wBAAU,oBAAM,6LAAC,qNAAA,CAAA,eAAY;;;;;wBAAK,SAAS;kCAAiB;;;;;;;;;;;;0BAK3E,6LAAC;gBAAI,OAAO;oBAAE,cAAc;gBAAG;0BAC7B,cAAA,6LAAC,mLAAA,CAAA,QAAK,CAAC,MAAM;oBACX,aAAY;oBACZ,UAAU;oBACV,2BAAa,6LAAC,yNAAA,CAAA,iBAAc;;;;;oBAC5B,UAAU,CAAC,QAAU,QAAQ,GAAG,CAAC;oBACjC,OAAO;wBAAE,OAAO;oBAAI;;;;;;;;;;;0BAIxB,6LAAC,mLAAA,CAAA,QAAK;gBACJ,SAAS;gBACT,YAAY;gBACZ,QAAO;gBACP,SAAS;gBACT,YAAY;oBACV,SAAS;oBACT,UAAU;oBACV,OAAO;oBACP,iBAAiB;oBACjB,iBAAiB;oBACjB,WAAW,CAAC,QAAU,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC;gBACxC;gBACA,UAAU;;;;;;0BAGZ,6LAAC,mLAAA,CAAA,QAAK;gBACJ,OAAO,YAAY,SAAS;gBAC5B,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,cAAc;0BAEd,cAAA,6LAAC,iLAAA,CAAA,OAAI;oBACH,MAAM;oBACN,QAAO;;sCAEP,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAU;6BAAE;sCAE/C,cAAA,6LAAC,mLAAA,CAAA,QAAK;gCAAC,aAAY;;;;;;;;;;;sCAGrB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO;gCAAC;oCAAE,UAAU;oCAAM,SAAS;gCAAQ;6BAAE;sCAE7C,cAAA,6LAAC,mLAAA,CAAA,QAAK;gCAAC,aAAY;;;;;;;;;;;sCAGrB,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;sCAEN,cAAA,6LAAC,qLAAA,CAAA,SAAM;gCACL,aAAY;gCACZ,UAAU;gCACV,SAAS;gCACT,iBAAgB;gCAChB,cAAc,CAAC,uBACb,6LAAC,mMAAA,CAAA,QAAK;;4CACH,OAAO,IAAI,CAAC,IAAI;4CAChB,OAAO,IAAI,CAAC,KAAK;;;;;;;;;;;;;;;;;sCAM1B,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;sCAEN,cAAA,6LAAC,qLAAA,CAAA,SAAM;gCACL,aAAY;gCACZ,UAAU;gCACV,SAAS,MACN,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ,EAAE,gBAAgB;iCAC/C,GAAG,CAAC,CAAA,OAAQ,CAAC;wCAAE,OAAO,KAAK,IAAI;wCAAE,OAAO,KAAK,EAAE;oCAAC,CAAC;;;;;;;;;;;sCAIxD,6LAAC,iLAAA,CAAA,OAAI,CAAC,IAAI;4BACR,MAAK;4BACL,OAAM;4BACN,cAAc;sCAEd,cAAA,6LAAC,mMAAA,CAAA,cAAW;gCAAC,KAAK;gCAAG,aAAY;gCAAQ,OAAO;oCAAE,OAAO;gCAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5E;GA3QwB;;QASP,iLAAA,CAAA,OAAI,CAAC;;;KATE", "debugId": null}}]}