import java.io.*;
import java.util.*;

public class MCC<PERSON><PERSON>Splitter {
    
    public static void main(String[] args) {
        System.out.println("=== Excel MCCMNC Data Splitter ===");
        System.out.println("Author: Augment Agent");
        System.out.println("Function: Split MCCMNC column data containing '/' into multiple rows");
        System.out.println("=========================================");
        
        try {
            String inputFile = "doc/test.xlsx";
            File excelFile = new File(inputFile);
            
            if (!excelFile.exists()) {
                System.err.println("Error: Cannot find file " + inputFile);
                System.out.println("Please ensure the file exists in the doc directory");
                return;
            }
            
            System.out.println("Found Excel file: " + inputFile);
            System.out.println("File size: " + excelFile.length() + " bytes");
            
            String csvFile = "doc/test.csv";
            String outputCsvFile = "doc/test1.csv";
            
            File csvInputFile = new File(csvFile);
            if (!csvInputFile.exists()) {
                System.out.println("\nNeed to convert Excel to CSV format first");
                System.out.println("Please follow these steps:");
                System.out.println("1. Open Excel file: " + inputFile);
                System.out.println("2. Click File -> Save As");
                System.out.println("3. Select file type: 'CSV UTF-8 (Comma delimited)(*.csv)'");
                System.out.println("4. Save as: " + csvFile);
                System.out.println("5. Run this program again");
                System.out.println("\nWaiting for CSV file creation...");
                return;
            }
            
            System.out.println("\nFound CSV file, starting processing...");
            boolean success = processCsvFile(csvFile, outputCsvFile);
            
            if (success) {
                System.out.println("\nProcessing completed successfully!");
                System.out.println("Result file: " + outputCsvFile);
                System.out.println("\nNext steps:");
                System.out.println("1. Open Excel");
                System.out.println("2. Import " + outputCsvFile + " file");
                System.out.println("3. Save as doc/test1.xlsx");
                
                createBatchFile();
            }
            
        } catch (Exception e) {
            System.err.println("Error occurred during processing: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public static boolean processCsvFile(String inputFile, String outputFile) {
        try {
            List<String[]> allRows = new ArrayList<>();
            int mccmncColumnIndex = -1;
            int originalRowCount = 0;
            int splitRowCount = 0;
            
            System.out.println("Reading CSV file...");
            
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(new FileInputStream(inputFile), "UTF-8"))) {
                
                String line;
                boolean isFirstLine = true;
                
                while ((line = reader.readLine()) != null) {
                    originalRowCount++;
                    String[] columns = parseCSVLine(line);
                    
                    if (isFirstLine) {
                        System.out.println("Header info: " + Arrays.toString(columns));
                        
                        for (int i = 0; i < columns.length; i++) {
                            if ("MCCMNC".equalsIgnoreCase(columns[i].trim())) {
                                mccmncColumnIndex = i;
                                break;
                            }
                        }
                        
                        if (mccmncColumnIndex == -1) {
                            System.err.println("Error: MCCMNC column not found");
                            System.out.println("Available columns: " + Arrays.toString(columns));
                            return false;
                        }
                        
                        System.out.println("Found MCCMNC column at position: " + (mccmncColumnIndex + 1));
                        allRows.add(columns);
                        isFirstLine = false;
                        continue;
                    }
                    
                    if (mccmncColumnIndex < columns.length) {
                        String mccmncValue = columns[mccmncColumnIndex];
                        
                        if (mccmncValue != null && mccmncValue.contains("/")) {
                            String[] parts = mccmncValue.split("/");
                            splitRowCount++;
                            
                            System.out.println("Row " + originalRowCount + ": '" + 
                                             mccmncValue + "' -> Split into " + parts.length + " rows");
                            
                            for (String part : parts) {
                                String[] newRow = columns.clone();
                                newRow[mccmncColumnIndex] = part.trim();
                                allRows.add(newRow);
                            }
                        } else {
                            allRows.add(columns);
                        }
                    } else {
                        allRows.add(columns);
                    }
                }
            }
            
            System.out.println("Saving result file...");
            
            try (PrintWriter writer = new PrintWriter(
                    new OutputStreamWriter(new FileOutputStream(outputFile), "UTF-8"))) {
                
                for (String[] row : allRows) {
                    writer.println(formatCSVLine(row));
                }
            }
            
            System.out.println("\n=== Processing Results ===");
            System.out.println("Original data rows: " + originalRowCount + " rows");
            System.out.println("Split data rows: " + splitRowCount + " rows");
            System.out.println("Final row count: " + allRows.size() + " rows");
            System.out.println("Output file: " + outputFile);
            
            return true;
            
        } catch (IOException e) {
            System.err.println("File processing error: " + e.getMessage());
            return false;
        }
    }
    
    private static String[] parseCSVLine(String line) {
        List<String> fields = new ArrayList<>();
        StringBuilder currentField = new StringBuilder();
        boolean inQuotes = false;
        
        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);
            
            if (c == '"') {
                if (inQuotes && i + 1 < line.length() && line.charAt(i + 1) == '"') {
                    currentField.append('"');
                    i++;
                } else {
                    inQuotes = !inQuotes;
                }
            } else if (c == ',' && !inQuotes) {
                fields.add(currentField.toString());
                currentField = new StringBuilder();
            } else {
                currentField.append(c);
            }
        }
        
        fields.add(currentField.toString());
        return fields.toArray(new String[0]);
    }
    
    private static String formatCSVLine(String[] fields) {
        StringBuilder line = new StringBuilder();
        
        for (int i = 0; i < fields.length; i++) {
            if (i > 0) {
                line.append(",");
            }
            
            String field = fields[i];
            if (field.contains(",") || field.contains("\"") || field.contains("\n") || field.contains("\r")) {
                line.append("\"").append(field.replace("\"", "\"\"")).append("\"");
            } else {
                line.append(field);
            }
        }
        
        return line.toString();
    }
    
    private static void createBatchFile() {
        try {
            PrintWriter writer = new PrintWriter(new FileWriter("run_mccmnc_splitter.bat"));
            writer.println("@echo off");
            writer.println("echo Excel MCCMNC Data Splitter Tool");
            writer.println("echo ================================");
            writer.println("java MCCMNCSplitter");
            writer.println("pause");
            writer.close();
            System.out.println("Created batch file: run_mccmnc_splitter.bat");
        } catch (IOException e) {
            System.out.println("Cannot create batch file: " + e.getMessage());
        }
    }
}
