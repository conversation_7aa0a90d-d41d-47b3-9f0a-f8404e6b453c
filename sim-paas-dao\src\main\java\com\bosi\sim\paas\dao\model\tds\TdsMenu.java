package com.bosi.sim.paas.dao.model.tds;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 菜单权限表 ads_menu
 */
@Data
@TableName("tds_menu")
public class TdsMenu extends BaseEntity {

    /**
     * 菜单名称
     */
    private String menuName;

    /**
     * 父菜单ID
     */
    private String parentId;

    /**
     * 显示顺序
     */
    private Integer orderNum;

    /**
     * 路由地址
     */
    private String path;

    /**
     * 路由参数
     */
    private String query;

    /**
     * 是否为外链（0是 1否）
     */
    private Boolean whetherFrame;


    private Boolean whetherCache;

    /**
     * 类型（M目录 C菜单 F按钮）
     */
    private String menuType;

    /**
     * 显示状态（0显示 1隐藏）
     */
    private Boolean whetherVisible;

    private Boolean whetherEnable;

    /**
     * 权限字符串
     */
    private String perms;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 组件路径
     */
    private String component;

    /**
     * 父菜单名称
     */
    @TableField(exist = false)
    private String parentName;

    @TableField(exist = false)
    private String userId;

    @TableField(exist = false)
    private List<TdsMenu> children = new ArrayList<>();

}
