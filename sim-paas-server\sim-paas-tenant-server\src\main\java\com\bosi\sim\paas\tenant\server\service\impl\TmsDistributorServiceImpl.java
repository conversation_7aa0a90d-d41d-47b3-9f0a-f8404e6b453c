package com.bosi.sim.paas.tenant.server.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.bosi.sim.paas.dao.mapper.tms.TmsDistributorMapper;
import com.bosi.sim.paas.dao.model.tds.TdsTenant;
import com.bosi.sim.paas.tenant.server.service.TmsDistributorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 分销商管理 服务实现
 */
@Service
public class TmsDistributorServiceImpl implements TmsDistributorService {
    @Autowired
    private TmsDistributorMapper distributorMapper;

    /**
     * 根据分销商ID查询信息
     *
     * @param distributorId 分销商ID
     * @return 分销商信息
     */
    @Override
    public TdsTenant selectDistributorById(String distributorId) {
        return distributorMapper.selectById(distributorId);
    }

    @Override
    public JSONObject statistics(String distributorId) {
        return null;
    }


}
