package com.bosi.sim.paas.tenant.server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bosi.sim.paas.common.core.api.CommonPage;
import com.bosi.sim.paas.dao.mapper.tms.TmsNotifyRecordMapper;
import com.bosi.sim.paas.dao.model.tds.TdsNotifyRecord;
import com.bosi.sim.paas.tenant.server.dao.TmsNotifyRecordDao;
import com.bosi.sim.paas.tenant.server.service.TmsNotifyRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TmsNotifyRecordServiceImpl implements TmsNotifyRecordService {

    @Autowired
    private TmsNotifyRecordDao tmsNotifyRecordDao;

    @Autowired
    private TmsNotifyRecordMapper tmsNotifyRecordMapper;

    @Override
    public CommonPage<TdsNotifyRecord> page(Page<TdsNotifyRecord> page, TdsNotifyRecord notifyRecord) {
        return CommonPage.restPage(tmsNotifyRecordDao.page(page, notifyRecord));
    }

    @Override
    public int read(String id, String distributorId) {
        TdsNotifyRecord update = new TdsNotifyRecord();
        update.setWhetherRead(true);
        LambdaQueryWrapper<TdsNotifyRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TdsNotifyRecord::getId, id);
        wrapper.eq(TdsNotifyRecord::getDistributorId, distributorId);
        return tmsNotifyRecordMapper.update(update, wrapper);
    }
}
