package com.bosi.sim.paas.admin.server.handler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bosi.sim.paas.common.core.utils.IdUtils;
import com.bosi.sim.paas.common.core.utils.StringUtils;
import com.bosi.sim.paas.dao.enums.mms.MmsChangeTypeEnum;
import com.bosi.sim.paas.dao.enums.mms.MmsSourceTypeEnum;
import com.bosi.sim.paas.dao.enums.oms.OmsOrderStatusEnum;
import com.bosi.sim.paas.dao.enums.oms.OmsSettleSituationEnum;
import com.bosi.sim.paas.dao.enums.tms.TmsMovementTypeEnum;
import com.bosi.sim.paas.dao.mapper.mms.MmsMemberGrowthHistoryMapper;
import com.bosi.sim.paas.dao.mapper.mms.MmsMemberMapper;
import com.bosi.sim.paas.dao.mapper.mms.MmsMemberPointHistoryMapper;
import com.bosi.sim.paas.dao.mapper.oms.OmsOrderItemMapper;
import com.bosi.sim.paas.dao.mapper.oms.OmsOrderMapper;
import com.bosi.sim.paas.dao.model.sds.SdsMember;
import com.bosi.sim.paas.dao.model.sds.SdsMemberGrowthHistory;
import com.bosi.sim.paas.dao.model.sds.SdsMemberPointHistory;
import com.bosi.sim.paas.dao.model.sds.OmsOrder;
import com.bosi.sim.paas.dao.model.sds.OmsOrderDivide;
import com.bosi.sim.paas.dao.model.sds.OmsOrderDivideSettle;
import com.bosi.sim.paas.dao.model.sds.OmsOrderItem;
import com.bosi.sim.paas.dao.model.sds.jsonclz.DivideDetailJsonClass;
import com.bosi.sim.paas.admin.server.config.rabbitmq.RabbitMqAccountMovementProducer;
import com.bosi.sim.paas.admin.server.dao.OmsOrderDivideDao;
import com.bosi.sim.paas.admin.server.dao.OmsOrderDivideSettleDao;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class OmsOrderHandler {

    @Autowired
    private OmsOrderMapper orderMapper;

    @Autowired
    private MmsMemberMapper memberMapper;

    @Autowired
    private MmsMemberGrowthHistoryMapper memberGrowthHistoryMapper;

    @Autowired
    private MmsMemberPointHistoryMapper memberPointHistoryMapper;

    @Autowired
    private OmsOrderItemMapper orderItemMapper;

    @Autowired
    private OmsOrderDivideDao omsOrderDivideDao;

    @Autowired
    private OmsOrderDivideSettleDao omsOrderDivideSettleDao;

    @Autowired
    private RabbitMqAccountMovementProducer rabbitMqAccountMovementProducer;

    public void afterOrderConfirm(String orderId) {
        OmsOrder omsOrder = orderMapper.selectById(orderId);
        if (!omsOrder.getStatus().equals(OmsOrderStatusEnum.OVER.getOrderStatus())) {
            log.error("当前订单状态有误,终止执行订单收货后逻辑,orderId is {}", orderId);
            return;
        }
        if (!omsOrder.getWhetherConfirm()) {
            log.error("当前订单确认有误,终止执行订单收货后逻辑,orderId is {}", orderId);
            return;
        }
        this.parseGift(omsOrder);
        this.parseDivide(omsOrder);
    }

    private void parseGift(OmsOrder omsOrder) {
        if ((omsOrder.getTotalGiftGrowth() != null && !omsOrder.getTotalGiftGrowth().equals(0))
                || (omsOrder.getTotalGiftPoint() != null && !omsOrder.getTotalGiftPoint().equals(0))) {
            SdsMember mmsMember = memberMapper.selectById(omsOrder.getMemberId());
            if (omsOrder.getTotalGiftGrowth() != null && !omsOrder.getTotalGiftGrowth().equals(0)) {
                mmsMember.setGrowth(mmsMember.getGrowth() + omsOrder.getTotalGiftGrowth());

                SdsMemberGrowthHistory growthHistory = new SdsMemberGrowthHistory();
                growthHistory.setMemberId(omsOrder.getMemberId());
                growthHistory.setChangeType(MmsChangeTypeEnum.INCREASE.getChangeType());
                growthHistory.setChangeCount(omsOrder.getTotalGiftGrowth());
                growthHistory.setSourceType(MmsSourceTypeEnum.SHOPPING.getSourceType());
                memberGrowthHistoryMapper.insert(growthHistory);
            }
            if (omsOrder.getTotalGiftPoint() != null && !omsOrder.getTotalGiftPoint().equals(0)) {
                mmsMember.setPoint(mmsMember.getPoint() + omsOrder.getTotalGiftPoint());

                SdsMemberPointHistory pointHistory = new SdsMemberPointHistory();
                pointHistory.setMemberId(omsOrder.getMemberId());
                pointHistory.setChangeType(MmsChangeTypeEnum.INCREASE.getChangeType());
                pointHistory.setChangeCount(omsOrder.getTotalGiftGrowth());
                pointHistory.setSourceType(MmsSourceTypeEnum.SHOPPING.getSourceType());
                memberPointHistoryMapper.insert(pointHistory);
            }
            memberMapper.updateById(mmsMember);
        }
    }

    private void parseDivide(OmsOrder omsOrder) {
        LambdaQueryWrapper<OmsOrderItem> orderItemExample = new LambdaQueryWrapper<>();
        orderItemExample.eq(OmsOrderItem::getOrderId, omsOrder.getId());
        List<OmsOrderItem> omsOrderItemList = orderItemMapper.selectList(orderItemExample);
        List<OmsOrderDivide> omsOrderDivideList = Lists.newArrayList();
        List<OmsOrderDivideSettle> omsOrderDivideSettleList = Lists.newArrayList();
        for (OmsOrderItem orderItem : omsOrderItemList) {
            if (StringUtils.isNotEmpty(orderItem.getDivideDetailJson())) {
                DivideDetailJsonClass divideDetailJsonClass = JSON.parseObject(orderItem.getDivideDetailJson(), DivideDetailJsonClass.class);
                String divideId = IdUtils.fastSimpleUUID();
                divideDetailJsonClass.getDivideAccountJsonarrayClassList().forEach(account -> {
                    OmsOrderDivideSettle omsOrderDivideSettle = new OmsOrderDivideSettle();
                    omsOrderDivideSettle.setId(IdUtils.fastSimpleUUID());
                    omsOrderDivideSettle.setAccountId(account.getAccountId());
                    omsOrderDivideSettle.setWhetherSettle(false);
                    omsOrderDivideSettle.setSettleAmount(account.getDivideAmount());
                    omsOrderDivideSettle.setOrderDivideId(divideId);
                    omsOrderDivideSettleList.add(omsOrderDivideSettle);
                });
                OmsOrderDivide orderDivide = new OmsOrderDivide();
                orderDivide.setId(divideId);
                orderDivide.setOrderId(omsOrder.getId());
                orderDivide.setOrderItemId(orderItem.getId());
                orderDivide.setSettleMode(divideDetailJsonClass.getSettleMode());
                orderDivide.setDistributorId(divideDetailJsonClass.getDistributorId());
                orderDivide.setSettleSituation(OmsSettleSituationEnum.NOT_SETTLE.getSettleSituation());
                omsOrderDivideList.add(orderDivide);
            }
        }
        if (CollUtil.isNotEmpty(omsOrderDivideList)) {
            omsOrderDivideDao.insertList(omsOrderDivideList);
        }
        if (CollUtil.isNotEmpty(omsOrderDivideSettleList)) {
            omsOrderDivideSettleDao.insertList(omsOrderDivideSettleList);
            omsOrderDivideSettleList.forEach(omsOrderDivideSettle -> {
                try {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("intransitBalanceChangeType", TmsMovementTypeEnum.INCOME.getMovementType());
                    jsonObject.put("intransitBalanceChangeValue", omsOrderDivideSettle.getSettleAmount());
                    jsonObject.put("accountId", omsOrderDivideSettle.getAccountId());
                    rabbitMqAccountMovementProducer.sendMessage(jsonObject);
                } catch (Exception e) {
                    log.error("rabbitMqAccountMovementProducer send msg error", e);
                }
            });
        }
    }

}
