package com.bosi.sim.paas.dao.model.sds;

import com.baomidou.mybatisplus.annotation.TableName;
import com.bosi.sim.paas.dao.model.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("sds_attribute_type")
public class SdsAttributeType extends BaseEntity {
    private String attributeId;

    private String name;

    private String code;

    @ApiModelProperty(value = "属性选择类型：1->单选；2->多选")
    private Integer selectType;

    @ApiModelProperty(value = "可选值列表，以逗号隔开")
    private String inputList;

    @ApiModelProperty(value = "排序字段：最高的可以单独上传图片")
    private Integer sort;

    @ApiModelProperty(value = "属性的类型；1->规格；2->参数")
    private Integer classify;

}
